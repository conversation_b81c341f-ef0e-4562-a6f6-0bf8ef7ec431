{"manifest": {"translationVersion": 1730867438352, "pluginVersion": "8.1.1"}, "description": {"original": "Change the colors, fonts and features of Minimal Theme.", "translation": "更改Minimal主题的颜色、字体和功能。"}, "dict": {".log(\"Folding is on\")": ".log(\"折叠已打开\")", ".log(\"Folding is off\")": ".log(\"折叠功能已关闭\")", ".log(\"Line numbers are on\")": ".log(\"行号已显示\")", ".log(\"Line numbers are off\")": ".log(\"行号已关闭\")", ".log(\"Readable line length is on\")": ".log(\"可读线路长度已打开\")", ".log(\"Readable line length is off\")": ".log(\"可读线路长度已关闭\")", ".log(\"Unloading Minimal Theme Settings plugin\")": ".log(\"卸载Minimal主题设置插件\")", "name:\"Increase body font size\"": "name:\"增加正文字体大小\"", "name:\"Decrease body font size\"": "name:\"减小正文字体大小\"", "name:\"Cycle between dark mode styles\"": "name:\"在暗黑模式样式之间循环\"", "name:\"Cycle between light mode styles\"": "name:\"在明亮模式样式之间循环\"", "name:\"Toggle sidebar borders\"": "name:\"切换侧边栏边框\"", "name:\"Toggle colorful headings\"": "name:\"切换彩色标题\"", "name:\"Toggle focus mode\"": "name:\"切换聚焦模式\"", "name:\"Toggle colorful window frame\"": "name:\"切换彩色窗框\"", "name:\"Cycle between table width options\"": "name:\"在表格宽度选项之间循环\"", "name:\"Cycle between image width options\"": "name:\"在图像宽度选项之间循环\"", "name:\"Cycle between iframe width options\"": "name:\"在iframe宽度选项之间循环\"", "name:\"Cycle between chart width options\"": "name:\"在图表宽度选项之间循环\"", "name:\"Cycle between map width options\"": "name:\"在地图宽度选项之间循环\"", "name:\"Toggle image grids\"": "name:\"切换图像网格\"", "name:\"Switch between light and dark mode\"": "name:\"在明亮模式和暗黑模式之间切换\"", "name:\"Use light mode (default)\"": "name:\"使用明亮模式（默认）\"", "name:\"Use light mode (all white)\"": "name:\"使用明亮模式（全白色）\"", "name:\"Use light mode (low contrast)\"": "name:\"使用明亮模式（低对比度）\"", "name:\"Use light mode (high contrast)\"": "name:\"使用明亮模式（高对比度）\"", "name:\"Use dark mode (default)\"": "name:\"使用暗黑模式（默认）\"", "name:\"Use dark mode (low contrast)\"": "name:\"使用暗黑模式（低对比度）\"", "name:\"Use dark mode (true black)\"": "name:\"使用暗黑模式（真黑）\"", "name:\"Switch light color scheme to Atom (light)\"": "name:\"将明亮配色方案切换为Atom（明亮）\"", "name:\"Switch light color scheme to Ayu (light)\"": "name:\"将明亮配色方案切换为Ayu（明亮）\"", "name:\"Switch light color scheme to Catppuccin (light)\"": "name:\"将明亮配色方案切换为Catppuccin（明亮）\"", "name:\"Switch light color scheme to default (light)\"": "name:\"将明亮配色方案切换为默认（明亮）\"", "name:\"Switch light color scheme to Gruvbox (light)\"": "name:\"将明亮配色方案切换到Gruvbox（明亮）\"", "name:\"Switch light color scheme to E-ink (light)\"": "name:\"将明亮配色方案切换为E-ink（明亮）\"", "name:\"Switch light color scheme to Everforest (light)\"": "name:\"将明亮配色方案切换到Everforest（明亮）\"", "name:\"Switch light color scheme to Flexoki (light)\"": "name:\"将明亮配色方案切换到<PERSON><PERSON><PERSON>（明亮）\"", "name:\"Switch light color scheme to macOS (light)\"": "name:\"将明亮配色方案切换到macOS（明亮）\"", "name:\"Switch light color scheme to Sky (light)\"": "name:\"将明亮配色方案切换到Sky（明亮）\"", "name:\"Switch light color scheme to Nord (light)\"": "name:\"将明亮配色方案切换到Nord（明亮）\"", "name:\"Switch light color scheme to Ros\\xE9 Pine (light)\"": "name:\"将明亮配色方案切换到Ros\\xE9 Pine（明亮）\"", "name:\"Switch light color scheme to Solarized (light)\"": "name:\"将明亮配色方案切换为Solarized（明亮）\"", "name:\"Switch light color scheme to Things (light)\"": "name:\"将明亮配色方案切换到Things（明亮）\"", "name:\"Switch dark color scheme to Atom (dark)\"": "name:\"将暗黑配色方案切换到Atom（暗黑）\"", "name:\"Switch dark color scheme to Ayu (dark)\"": "name:\"将暗黑配色方案切换为Ayu（暗黑）\"", "name:\"Switch dark color scheme to Catppuccin (dark)\"": "name:\"将暗黑配色方案切换到Catppuccin（暗黑）\"", "name:\"Switch dark color scheme to Dracula (dark)\"": "name:\"将暗黑配色方案切换为Dracula（暗黑）\"", "name:\"Switch dark color scheme to default (dark)\"": "name:\"将暗黑配色方案切换为默认（暗黑）\"", "name:\"Switch dark color scheme to E-ink (dark)\"": "name:\"将暗黑配色方案切换到E-ink（暗黑）\"", "name:\"Switch dark color scheme to Everforest (dark)\"": "name:\"将暗黑配色方案切换到Everforest（暗黑）\"", "name:\"Switch dark color scheme to Flexoki (dark)\"": "name:\"将暗黑配色方案切换到Flex<PERSON>（暗黑）\"", "name:\"Switch dark color scheme to Gruvbox (dark)\"": "name:\"将暗黑配色方案切换到Gruvbox（暗黑）\"", "name:\"Switch dark color scheme to macOS (dark)\"": "name:\"将暗黑配色方案切换到macOS（暗黑）\"", "name:\"Switch dark color scheme to Nord (dark)\"": "name:\"将暗黑配色方案切换到Nord（暗黑）\"", "name:\"Switch dark color scheme to Sky (dark)\"": "name:\"将暗黑配色方案切换到Sky（暗黑）\"", "name:\"Switch dark color scheme to Ros\\xE9 Pine (dark)\"": "name:\"将暗黑配色方案切换到Ros\\xE9 Pine（暗黑）\"", "name:\"Switch dark color scheme to Solarized (dark)\"": "name:\"将暗黑配色方案切换为Solarized（暗黑）\"", "name:\"Switch dark color scheme to Things (dark)\"": "name:\"将暗黑配色方案切换到Things（暗黑）\"", "name:\"Dev \\u2014 Show block widths\"": "name:\"Dev\\u2014显示块宽度\"", "text:\"Color scheme\"": "text:\"配色方案\"", "text:\"To create a custom color scheme use the \"": "text:\"要创建自定义配色方案，请使用\"", "text:\"Style Settings\"": "text:\"Style Settings\"", "text:\"documentation\"": "text:\"文档\"", "text:\"Features\"": "text:\"特性\"", "text:\"See \"": "text:\"请参阅\"", "text:\"Layout\"": "text:\"布局\"", "text:\"These options can also be defined on a per-file basis, see \"": "text:\"这些选项也可以根据每个文件的基础上自定义，详情请参阅\"", "text:\"Typography\"": "text:\"排版\"", ".setName(\"Light mode color scheme\")": ".<PERSON><PERSON><PERSON>(\"明亮模式配色方案\")", ".setName(\"Light mode background contrast\")": ".set<PERSON>ame(\"明亮模式背景对比度\")", ".setName(\"Dark mode color scheme\")": ".setName(\"暗黑模式配色方案\")", ".setName(\"Dark mode background contrast\")": ".setName(\"暗黑模式背景对比度\")", ".setName(\"Text labels for primary navigation\")": ".setName(\"资源导航图标 📁 和 🔍 标签化\")", ".setName(\"Colorful window frame\")": ".setName(\"窗口颜色\")", ".setName(\"Colorful active states\")": ".setName(\"选中状态的背景色\")", ".setName(\"Colorful headings\")": ".setName(\"多彩标题\")", ".setName(\"Minimal status bar\")": ".setName(\"Minimal 底部状态栏\")", ".setName(\"Trim file names in sidebars\")": ".setName(\"裁剪侧边栏中过长的文件名\")", ".setName(\"Workspace borders\")": ".setName(\"工作区边界\")", ".setName(\"Focus mode\")": ".setName(\"聚焦模式\")", ".setName(\"Underline internal links\")": ".setName(\"在内部链接下划线\")", ".setName(\"Underline external links\")": ".setName(\"为外部链接加下划线\")", ".setName(\"Maximize media\")": ".setName(\"媒体文件尺寸最大化嵌入\")", ".setName(\"Image grids\")": ".setName(\"图像网格布局\")", ".setName(\"Chart width\")": ".setName(\"图表宽度\")", ".setName(\"Iframe width\")": ".setName(\"Iframe宽度\")", ".setName(\"Image width\")": ".setName(\"图像宽度\")", ".setName(\"Map width\")": ".setName(\"地图宽度\")", ".setName(\"Table width\")": ".setName(\"表格宽度\")", ".setName(\"Text font size\")": ".setName(\"文本字体尺寸\")", ".setName(\"Small font size\")": ".setName(\"小号字体尺寸\")", ".setName(\"Line height\")": ".set<PERSON><PERSON>(\"行高\")", ".setName(\"Normal line width\")": ".setName(\"标准行宽\")", ".setName(\"Wide line width\")": ".setName(\"宽元素行宽\")", ".setName(\"Maximum line width %\")": ".setName(\"最大行宽%\")", ".setName(\"Editor font\")": ".setName(\"编辑器字体\")", ".setDesc(\"Preset color options for light mode.\")": ".setDesc(\"明亮模式的预设配色选项。\")", ".setDesc(\"Level of contrast between sidebar and main content.\")": ".setDesc(\"侧边栏和主要内容之间的对比度。\")", ".setDesc(\"Preset colors options for dark mode.\")": ".setDesc(\"暗黑模式的预设配色选项。\")", ".setDesc(\"Navigation items in the left sidebar uses text labels.\")": ".setDesc(\"左侧边栏顶部的资源导航图标栏改为上下标签栏的形式。\")", ".setDesc(\"The top area of the app uses your accent color.\")": ".setDesc(\"应用顶部窗口框使用外观设置里的主题色。\")", ".setDesc(\"Active file and menu items use your accent color.\")": ".setDesc(\"当前选中的文件和菜单项使用您设置的主题色。\")", ".setDesc(\"Headings use a different color for each size.\")": ".setDesc(\"每种尺寸的标题使用不同的颜色。\")", ".setDesc(\"Turn off to use full-width status bar.\")": ".setDesc(\"关闭以使用全宽状态栏。\")", ".setDesc(\"Use ellipses to fit file names on a single line.\")": ".setDesc(\"使用省略号替代过长的文字部分使得文件名归于同一行上。\")", ".setDesc(\"Display divider lines between workspace elements.\")": ".setDesc(\"显示工作区空间分隔线（如侧边栏与编辑器之间的分隔线）。\")", ".setDesc(\"Hide tab bar and status bar, hover to display. Can be toggled via hotkey.\")": ".setDesc(\"隐藏标签栏和状态栏，悬停可显示。此特性可以通过热键切换。\")", ".setDesc(\"Show underlines on internal links.\")": ".setDesc(\"在内部链接上显示下划线。\")", ".setDesc(\"Show underlines on external links.\")": ".setDesc(\"在外部链接上显示下划线。\")", ".setDesc(\"Images and videos fill the width of the line.\")": ".setDesc(\"图像和视频的宽度填满整行。\")", ".setDesc(\"Turn consecutive images into columns \\u2014 to make a new row, add an extra line break between images.\")": ".setDesc(\"将连续图像转换为列\\u2014以创建新行，在图像之间添加额外的换行符。\")", ".setDesc(\"Default width for chart blocks.\")": ".setDesc(\"图表块的默认宽度。\")", ".setDesc(\"Default width for iframe blocks.\")": ".setDesc(\"iframe块的默认宽度。\")", ".setDesc(\"Default width for image blocks.\")": ".setDesc(\"图像块的默认宽度。\")", ".setDesc(\"Default width for map blocks.\")": ".setDesc(\"地图块的默认宽度。\")", ".setDesc(\"Default width for table and Dataview blocks.\")": ".setDesc(\"表格数据视图块的默认宽度。\")", ".setDesc(\"Used for the main text (default 16).\")": ".setDesc(\"用于正文（默认值16）。\")", ".setDesc(\"Used for text in the sidebars and tabs (default 13).\")": ".setDesc(\"用于侧边栏和选项卡中的文本（默认值13）。\")", ".setDesc(\"Line height of text (default 1.5).\")": ".setDesc(\"文本的行高（默认值为1.5）。\")", ".setDesc(\"Number of characters per line (default 40).\")": ".setDesc(\"标准行宽每行所占字符数（默认值为40）。\")", ".setDesc(\"Number of characters per line for wide elements (default 50).\")": ".setDesc(\"宽元素每行所占的字符数（默认值为50）。\")", ".setDesc(\"Percentage of space inside a pane that a line can fill (default 88).\")": ".setDesc(\"一行文字能够填充编辑器横向空间的百分比（默认值为88）。\")", ".setDesc(\"Overrides the text font defined in Obsidian Appearance settings when in edit mode.\")": ".setDesc(\"在编辑模式下，覆盖在obsidian外观(Appearance)设置中定义的文本字体。\")", ".setPlaceholder(\"16\")": ".setPlaceholder(\"16\")", ".setPlaceholder(\"13\")": ".setPlaceholder(\"13\")", ".setPlaceholder(\"1.5\")": ".setPlaceholder(\"1.5\")", ".setPlaceholder(\"40\")": ".setPlaceholder(\"40\")", ".setPlaceholder(\"50\")": ".setPlaceholder(\"50\")", ".setPlaceholder(\"88\")": ".setPlaceholder(\"88\")", ".setPlaceholder(\"\").setValue((this.plugin.settings.editorFont||\"\")": ".setPlaceholder(\"\").setValue((this.plugin.settings.editorFont||\"\")", ".appendText(\" plugin. See \")": ".appendText(\"插件。请参阅\")", ".appendText(\" for details.\")": ".appendText(\"了解详情。\")", ".innerText=\"body.minimal-theme{--font-ui-small:\"": ".innerText=\"body.minimal-theme{--font-ui-small:\""}}