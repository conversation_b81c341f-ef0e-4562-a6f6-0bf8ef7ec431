/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var D=Object.create;var b=Object.defineProperty;var j=Object.getOwnPropertyDescriptor;var B=Object.getOwnPropertyNames;var A=Object.getPrototypeOf,L=Object.prototype.hasOwnProperty;var N=(r,h)=>{for(var t in h)b(r,t,{get:h[t],enumerable:!0})},M=(r,h,t,s)=>{if(h&&typeof h=="object"||typeof h=="function")for(let i of B(h))!L.call(r,i)&&i!==t&&b(r,i,{get:()=>h[i],enumerable:!(s=j(h,i))||s.enumerable});return r};var $=(r,h,t)=>(t=r!=null?D(A(r)):{},M(h||!r||!r.__esModule?b(t,"default",{value:r,enumerable:!0}):t,r)),O=r=>M(b({},"__esModule",{value:!0}),r);var V={};N(V,{default:()=>P});module.exports=O(V);var e=require("obsidian"),u=$(require("fs")),p=$(require("path")),v=require("child_process"),z={pythonPath:"python",enablePlugins:!1,docintelEndpoint:"",outputPath:""},P=class extends e.Plugin{constructor(){super(...arguments);this.pythonInstalled=!1;this.markitdownInstalled=!1}async onload(){await this.loadSettings(),await this.checkDependencies();let t=this.addRibbonIcon("file-text","Convert to Markdown with Markitdown",()=>{new C(this.app,this).open()});this.addCommand({id:"convert-file-markitdown",name:"Convert file to Markdown with Markitdown",callback:()=>{if(!this.markitdownInstalled){new I(this.app,this).open();return}new C(this.app,this).open()}}),this.addCommand({id:"convert-folder-markitdown",name:"Convert folder contents to Markdown with Markitdown",callback:()=>{if(!this.markitdownInstalled){new I(this.app,this).open();return}new S(this.app,this).open()}}),this.addSettingTab(new T(this.app,this))}async checkDependencies(){try{await this.execCommand(`${this.settings.pythonPath} --version`),this.pythonInstalled=!0;try{await this.execCommand(`${this.settings.pythonPath} -m pip show markitdown`),this.markitdownInstalled=!0}catch(t){this.markitdownInstalled=!1}}catch(t){this.pythonInstalled=!1,this.markitdownInstalled=!1,console.error("Failed to check Python installation",t)}}async execCommand(t){return new Promise((s,i)=>{(0,v.exec)(t,(o,f,c)=>{if(o){i(o);return}s(f)})})}async installMarkitdown(){try{return await this.execCommand(`${this.settings.pythonPath} -m pip install 'markitdown[all]'`),this.markitdownInstalled=!0,!0}catch(t){return console.error("Failed to install Markitdown",t),!1}}async convertFile(t,s){return new Promise((i,o)=>{let f=`markitdown "${t}" > "${s}"`;(0,v.exec)(f,(c,n,a)=>{if(c){let g=`${this.settings.pythonPath} -c "from markitdown import convert; convert('${t}', output_file='${s}')"`;(0,v.exec)(g,(l,m,y)=>{if(l){let w=`${this.settings.pythonPath} -m markitdown "${t}" > "${s}"`;(0,v.exec)(w,(d,k,x)=>{if(d){o(new Error(`Markitdown failed to convert the file: ${d.message}
${x}`));return}i(k)});return}i(m)});return}i(n)})})}onunload(){}async loadSettings(){this.settings=Object.assign({},z,await this.loadData())}async saveSettings(){await this.saveData(this.settings)}},C=class extends e.Modal{constructor(t,s){super(t);this.plugin=s}onOpen(){let{contentEl:t}=this;if(t.addClass("markitdown-modal"),t.createEl("h2",{text:"Convert File to Markdown"}),!this.plugin.markitdownInstalled){t.createEl("p",{text:"Markitdown is not installed. Please install it in the settings tab."}),t.createEl("button",{text:"Go to Settings"}).addEventListener("click",()=>{this.close(),"setting"in this.app&&(this.app.setting.open(),this.app.setting.openTabById("obsidian-markitdown"))});return}t.createEl("p",{text:"Select a file to convert:"});let i=t.createDiv("markitdown-file-input-container").createEl("input",{attr:{type:"file",accept:".pdf,.docx,.pptx,.xlsx,.xls,.html,.htm,.txt,.csv,.json,.xml,.jpg,.jpeg,.png,.gif,.wav,.mp3,.zip"}}),o=t.createDiv("markitdown-button-container");o.style.display="flex",o.style.justifyContent="flex-end",o.style.marginTop="20px",o.createEl("button",{text:"Convert"}).addEventListener("click",async()=>{if(i.files&&i.files.length>0){let c=i.files[0];try{let n="";if(this.app.vault.adapter instanceof e.FileSystemAdapter&&(n=this.app.vault.adapter.getBasePath()),!n){new e.Notice("Could not determine vault path. This plugin requires a local vault.");return}let a=this.plugin.settings.outputPath||"";a?(a=p.join(n,a),u.existsSync(a)||u.mkdirSync(a,{recursive:!0})):(a=p.join(n,"markitdown-output"),u.existsSync(a)||u.mkdirSync(a,{recursive:!0}));let g=p.basename(c.name,p.extname(c.name)),l=p.join(a,`${g}.md`);new e.Notice("Converting file...");let m=p.join(a,`${Date.now()}_${c.name}`),y=await c.arrayBuffer();u.writeFileSync(m,Buffer.from(y)),await this.plugin.convertFile(m,l),u.existsSync(m)&&u.unlinkSync(m),await this.app.vault.adapter.exists(l),new e.Notice(`File converted and saved to ${l}`),this.close();let w=p.relative(n,l).replace(/\\/g,"/"),d=this.app.vault.getAbstractFileByPath(w);d instanceof e.TFile&&this.app.workspace.getLeaf().openFile(d)}catch(n){console.error("Error during conversion:",n),new e.Notice(`Error: ${n.message}`)}}else new e.Notice("Please select a file first")})}onClose(){let{contentEl:t}=this;t.empty()}},S=class extends e.Modal{constructor(t,s){super(t);this.plugin=s}onOpen(){let{contentEl:t}=this;if(t.addClass("markitdown-modal"),t.createEl("h2",{text:"Convert Folder Contents to Markdown"}),!this.plugin.markitdownInstalled){t.createEl("p",{text:"Markitdown is not installed. Please install it in the settings tab."}),t.createEl("button",{text:"Go to Settings"}).addEventListener("click",()=>{this.close(),"setting"in this.app&&(this.app.setting.open(),this.app.setting.openTabById("obsidian-markitdown"))});return}t.createEl("p",{text:"Select a folder to process:"});let i=t.createDiv("markitdown-file-input-container").createEl("input",{attr:{type:"file",webkitdirectory:"",directory:""}});t.createEl("p",{text:"Select file types to convert:"});let o=[{name:"PDF Files",ext:".pdf"},{name:"Word Documents",ext:".docx"},{name:"PowerPoint Presentations",ext:".pptx"},{name:"Excel Spreadsheets",ext:".xlsx,.xls"},{name:"Web Pages",ext:".html,.htm"},{name:"Text Files",ext:".txt"},{name:"Data Files",ext:".csv,.json,.xml"},{name:"Images",ext:".jpg,.jpeg,.png,.gif"},{name:"Audio Files",ext:".wav,.mp3"},{name:"Archives",ext:".zip"}],f=t.createDiv("markitdown-checkbox-grid"),c=[];o.forEach(g=>{let l=f.createEl("label",{cls:"markitdown-checkbox-label"}),m=l.createEl("input",{attr:{type:"checkbox",value:g.ext}});m.addEventListener("change",()=>{let y=g.ext.split(",");m.checked?y.forEach(w=>{c.includes(w)||c.push(w)}):y.forEach(w=>{let d=c.indexOf(w);d>-1&&c.splice(d,1)})}),l.appendText(g.name)});let n=t.createDiv("markitdown-button-container");n.style.display="flex",n.style.justifyContent="flex-end",n.style.marginTop="20px",n.createEl("button",{text:"Convert"}).addEventListener("click",async()=>{if(i.files&&i.files.length>0){if(c.length===0){new e.Notice("Please select at least one file type");return}try{let g="";if(this.app.vault.adapter instanceof e.FileSystemAdapter&&(g=this.app.vault.adapter.getBasePath()),!g){new e.Notice("Could not determine vault path. This plugin requires a local vault.");return}let l=this.plugin.settings.outputPath||"";l?(l=p.join(g,l),u.existsSync(l)||u.mkdirSync(l,{recursive:!0})):(l=p.join(g,"markitdown-output"),u.existsSync(l)||u.mkdirSync(l,{recursive:!0}));let m=[];for(let d=0;d<i.files.length;d++){let k=i.files[d],x=p.extname(k.name).toLowerCase();c.includes(x)&&m.push(k)}if(m.length===0){new e.Notice("No matching files found in the selected folder");return}new e.Notice(`Converting ${m.length} files...`),this.close();let y=0,w=0;for(let d of m)try{let k=p.basename(d.name,p.extname(d.name)),x=p.join(l,`${k}.md`),E=p.join(l,`${Date.now()}_${d.name}`),F=await d.arrayBuffer();u.writeFileSync(E,Buffer.from(F)),await this.plugin.convertFile(E,x),u.existsSync(E)&&u.unlinkSync(E),y++}catch(k){console.error(`Error converting ${d.name}:`,k),w++}await this.app.vault.adapter.list(l),new e.Notice(`Conversion complete: ${y} successful, ${w} failed`)}catch(g){console.error("Error during folder conversion:",g),new e.Notice(`Error: ${g.message}`)}}else new e.Notice("Please select a folder first")})}onClose(){let{contentEl:t}=this;t.empty()}},I=class extends e.Modal{constructor(t,s){super(t);this.plugin=s}onOpen(){let{contentEl:t}=this;if(t.addClass("markitdown-modal"),t.createEl("h2",{text:"Markitdown Setup"}),!this.plugin.pythonInstalled){t.createEl("p",{text:"Python is not installed or not found at the specified path. Please install Python and configure the path in settings."}),t.createEl("button",{text:"Go to Settings"}).addEventListener("click",()=>{this.close(),"setting"in this.app&&(this.app.setting.open(),this.app.setting.openTabById("obsidian-markitdown"))});return}t.createEl("p",{text:"Markitdown is not installed. Would you like to install it now?"}),t.createEl("p",{text:"This will install the Markitdown Python package using pip."});let s=t.createDiv("markitdown-button-container");s.style.display="flex",s.style.justifyContent="flex-end",s.style.gap="10px",s.style.marginTop="20px",s.createEl("button",{text:"Cancel"}).addEventListener("click",()=>{this.close()});let o=s.createEl("button",{text:"Install Markitdown"});o.addEventListener("click",async()=>{o.disabled=!0,o.setText("Installing...");try{await this.plugin.installMarkitdown()?(new e.Notice("Markitdown installed successfully!"),this.close()):(t.createEl("p",{text:"Failed to install Markitdown. Please check the console for errors."}),o.disabled=!1,o.setText("Try Again"))}catch(f){console.error("Error installing Markitdown:",f),t.createEl("p",{text:`Error: ${f.message}`}),o.disabled=!1,o.setText("Try Again")}})}onClose(){let{contentEl:t}=this;t.empty()}},T=class extends e.PluginSettingTab{constructor(t,s){super(t,s);this.plugin=s}display(){let{containerEl:t}=this;t.empty(),t.createEl("h2",{text:"Markitdown Settings"}),new e.Setting(t).setName("Python Path").setDesc("Path to Python executable (e.g., python, python3, or full path)").addText(n=>n.setPlaceholder("python").setValue(this.plugin.settings.pythonPath).onChange(async a=>{this.plugin.settings.pythonPath=a,await this.plugin.saveSettings(),this.plugin.checkDependencies()})),new e.Setting(t).setName("Enable Markitdown Plugins").setDesc("Enable third-party plugins for Markitdown").addToggle(n=>n.setValue(this.plugin.settings.enablePlugins).onChange(async a=>{this.plugin.settings.enablePlugins=a,await this.plugin.saveSettings()})),new e.Setting(t).setName("Azure Document Intelligence Endpoint").setDesc("Optional: Use Azure Document Intelligence for better conversion (requires API key setup)").addText(n=>n.setPlaceholder("https://your-resource.cognitiveservices.azure.com/").setValue(this.plugin.settings.docintelEndpoint).onChange(async a=>{this.plugin.settings.docintelEndpoint=a,await this.plugin.saveSettings()})),new e.Setting(t).setName("Output Folder").setDesc('Folder path for converted files (relative to vault root, leave empty for default "markitdown-output")').addText(n=>n.setPlaceholder("markitdown-output").setValue(this.plugin.settings.outputPath).onChange(async a=>{this.plugin.settings.outputPath=a,await this.plugin.saveSettings()})),t.createEl("h3",{text:"Status"});let s=t.createDiv("markitdown-status-container"),i=s.createDiv("markitdown-status-item"),o=i.createSpan();o.addClass("markitdown-status-icon"),o.addClass(this.plugin.pythonInstalled?"success":"error"),o.setText(this.plugin.pythonInstalled?"\u2713":"\u2717"),i.createSpan().setText(`Python: ${this.plugin.pythonInstalled?"Installed":"Not installed"}`);let f=s.createDiv("markitdown-status-item"),c=f.createSpan();if(c.addClass("markitdown-status-icon"),c.addClass(this.plugin.markitdownInstalled?"success":"error"),c.setText(this.plugin.markitdownInstalled?"\u2713":"\u2717"),f.createSpan().setText(`Markitdown: ${this.plugin.markitdownInstalled?"Installed":"Not installed"}`),!this.plugin.markitdownInstalled&&this.plugin.pythonInstalled){let n=t.createEl("button",{text:"Install Markitdown",cls:"markitdown-install-button"});n.addEventListener("click",async()=>{n.disabled=!0,n.setText("Installing...");try{await this.plugin.installMarkitdown()?(new e.Notice("Markitdown installed successfully!"),this.display()):(new e.Notice("Failed to install Markitdown. Please check the console for errors."),n.disabled=!1,n.setText("Try Again"))}catch(a){console.error("Error installing Markitdown:",a),new e.Notice(`Error: ${a.message}`),n.disabled=!1,n.setText("Try Again")}})}}};

/* nosourcemap */