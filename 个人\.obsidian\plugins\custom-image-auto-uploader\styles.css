.custom-image-auto-uploader-settings-tag .setting-item-name{
  color: var(--interactive-accent);
}
.custom-image-auto-uploader-settings-openapi {
  width: 100%;
  margin-bottom: 20px;
  border: 1px solid var(--background-modifier-border);
}

.custom-image-auto-uploader-settings-openapi th,
.custom-image-auto-uploader-settings-openapi td {
  border: 0 !important;
}
.custom-image-auto-uploader-settings-openapi th {
  text-align: left;
  padding: 8px 10px;
  font-size: var(--font-ui-smaller);
}
.custom-image-auto-uploader-settings-openapi thead {
  border-bottom: 1px solid var(--color-base-40) !important;
}
.custom-image-auto-uploader-settings-openapi thead tr th {
  background-color: var(--background-secondary);
}

.custom-image-auto-uploader-settings-openapi thead {
  border-bottom: 1px solid var(--color-base-40) !important;
}
.custom-image-auto-uploader-settings-openapi tbody tr {
  background-color: var(--background-secondary);
}
.custom-image-auto-uploader-settings-openapi tbody tr:nth-child(odd) {
  background-color: var(--background-secondary-alt);
}
.custom-image-auto-uploader-settings-openapi tbody td {
  padding: 4px 8px;
  font-size: var(--font-ui-small);
}
.custom-image-auto-uploader-settings-openapi tfoot td {
  padding: 8px 10px;
}
.custom-image-auto-uploader-settings-openapi a {
  color: var(--color-blue);
  text-decoration: none;
}
.custom-image-auto-uploader-settings-openapi a:hover {
  text-decoration: underline;
}

.custom-image-auto-uploader-settings {
  padding: 0.75em 0;
  border-top: 1px solid var(--background-modifier-border);
}

.clipboard-read {
  display: flex;
}
.clipboard-read .clipboard-read-button {
display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem; /* 间距 8px */
  white-space: nowrap;
  font-size: 0.875rem; /* 14px */
  line-height: 1.25rem; /* 20px */
  font-weight: 500;
  transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out;
  border: 1px solid var(--input); /* 假设使用自定义变量 */
  background-color: var(--background); /* 假设使用自定义变量 */
  height: 2.25rem; /* 36px */
  border-radius: 0.375rem; /* 6px */
  padding-left: 0.75rem; /* 12px */
  padding-right: 0.75rem; /* 12px */
  color: #6b7280; /* gray-500 */



  /* 伪类 */
  &:focus-visible {
    outline: none;
    /* 移除阴影效果 */
    /* box-shadow: 0 0 0 2px var(--ring), 0 0 0 4px var(--background); 已注释掉 */
  }

  &:hover {
    background-color: var(--accent);
    color: var(--accent-foreground);
  }

  &:disabled {
    pointer-events: none;
    opacity: 0.5;
  }

  /* 子元素 SVG 样式 */
  & svg {
    pointer-events: none;
    width: 1rem; /* 16px */
    height: 1rem; /* 16px */
    flex-shrink: 0;
  }
}

.clipboard-read .clipboard-read-description {
    color: var(--color-red);
    float: left;
    padding-left: 20px;
    height: 20px;
    padding-top: 8px;
}

.custom-image-auto-uploader-settings-table {
  width: 100%;
  margin-bottom: 20px;
  border: 0 !important;
  background-color: var(--background-secondary);
}
.custom-image-auto-uploader-settings-table th,
.custom-image-auto-uploader-settings-table td {
  border: 0 !important;
}
.custom-image-auto-uploader-settings-table th {
  text-align: left;
  padding: 8px 10px;
  font-size: var(--font-ui-smaller);
}
.custom-image-auto-uploader-settings-table .no-columns-added {
  padding: 15px;
  text-align: center;
  background-color: var(--background-secondary);
}
.custom-image-auto-uploader-settings-table .no-columns-added p {
  margin: 4px;
  font-size: 10px;
}
.custom-image-auto-uploader-settings-table thead {
  border-bottom: 1px solid var(--color-base-40) !important;
}
.custom-image-auto-uploader-settings-table tbody tr {
  background-color: var(--background-secondary);
}
.custom-image-auto-uploader-settings-table tbody tr:nth-child(odd) {
  background-color: var(--background-secondary-alt);
}
.custom-image-auto-uploader-settings-table tbody td {
  padding: 4px 8px;
  font-size: var(--font-ui-small);
}
.custom-image-auto-uploader-settings-table tfoot td {
  padding: 8px 10px;
}

/*# sourceMappingURL=styles.css.map */
