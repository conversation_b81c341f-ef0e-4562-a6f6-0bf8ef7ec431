{"manifest": {"translationVersion": 1743588232707, "pluginVersion": "5.5.11"}, "description": {"original": "This plugin aims to enhance and optimize the editing experience in Obsidian", "translation": "This plugin aims to enhance and optimize the editing experience in Obsidian"}, "dict": {"Notice(\"EasyTyping: \\u7B2C\" + String(i)": "Notice(\"EasyTyping: \\u7B2C\" + String(i)", "Notice(\"EasuTyping: Bad RegExp:\\n\" + regItem)": "Notice(\"EasuTyping: Bad RegExp:\\n\" + regItem)", "Notice(locale5.placeHolder.noticeInvaidTrigger)": "Notice(locale5.placeHolder.noticeInvaidTrigger)", "Notice((0, import_sprintf_js.sprintf)": "Notice((0, import_sprintf_js.sprintf)", "Notice(locale5.placeHolder.noticeMissingInput)": "Notice(locale5.placeHolder.noticeMissingInput)", "Notice(locale5.placeHolder.noticeInvaidTriggerPatternContainSymbol)": "Notice(locale5.placeHolder.noticeInvaidTriggerPatternContainSymbol)", "Notice(locale5.placeHolder.noticeInvalidPatternString)": "Notice(locale5.placeHolder.noticeInvalidPatternString)", "Notice(\"EasyTyping: Syntax tree is not ready yet, please wait a moment and try again later!\", 5e3)": "Notice(\"EasyTyping: Syntax tree is not ready yet, please wait a moment and try again later!\", 5e3)", "Notice(\"EasyTyping: Format Article Done!\")": "Notice(\"EasyTyping: Format Article Done!\")", "Notice(\"new md-file open: \" + file.path)": "Notice(\"new md-file open: \" + file.path)", "Notice(\"EasyTyping: Autoformat is \" + status + \"!\")": "Notice(\"EasyTyping: Autoformat is \" + status + \"!\")", ".log(message, ...optionalParams)": ".log(message, ...optionalParams)", ".log(\"insertStr\", insertedStr)": ".log(\"insertStr\", insertedStr)", ".log(\"line parts\\n\", lineParts)": ".log(\"line parts\\n\", lineParts)", ".log(\"[TransactionFilter] type, fromA, toA, changed, fromB, toB, inserted\")": ".log(\"[TransactionFilter] type, fromA, toA, changed, fromB, toB, inserted\")", ".log(changeTypeStr, fromA, toA, changedStr, fromB, toB, insertedStr)": ".log(changeTypeStr, fromA, toA, changedStr, fromB, toB, insertedStr)", ".log(\"[ViewUpdate] type, fromA, toA, changed, fromB, toB, inserted\")": ".log(\"[ViewUpdate] type, fromA, toA, changed, fromB, toB, inserted\")", ".log(changeType, fromA, toA, changedStr, fromB, toB, insertedStr)": ".log(changeType, fromA, toA, changedStr, fromB, toB, insertedStr)", ".log(\"==>[Composing]\", update.view.composing)": ".log(\"==>[Composing]\", update.view.composing)", ".log(\"Keyup:\", event.key)": ".log(\"Keyup:\", event.key)", ".log(\"config.strictLineBreaks\", this.app.vault.getConfig(\"strictLineBreaks\")": ".log(\"config.strictLineBreaks\", this.app.vault.getConfig(\"strictLineBreaks\")", ".log(\"can't get editor\")": ".log(\"can't get editor\")", ".log(\"Easy Typing Plugin loaded.\")": ".log(\"Easy Typing Plugin loaded.\")", ".log(\"Easy Typing Plugin unloaded.\")": ".log(\"Easy Typing Plugin unloaded.\")", ".log(\"Normal Paste!!\")": ".log(\"Normal Paste!!\")", ".log(\"----- EasyTyping: insert code block-----\")": ".log(\"----- EasyTyping: insert code block-----\")", "name: \"Symbol auto pair and delete with pair\"": "name: \"Symbol auto pair and delete with pair\"", "name: \"Selection Replace Enhancement\"": "name: \"Selection Replace Enhancement\"", "name: \"Convert successive full width symbol to half width symbol\"": "name: \"Convert successive full width symbol to half width symbol\"", "name: \"Basic symbol input enhance for Obsidian\"": "name: \"Basic symbol input enhance for Obsidian\"", "name: \"Enhance codeblock edit\"": "name: \"<PERSON><PERSON><PERSON> codeblock edit\"", "name: \"Enhance backspace edit\"": "name: \"<PERSON><PERSON><PERSON> backspace edit\"", "name: \"Tabout\"": "name: \"<PERSON><PERSON><PERSON>\"", "name: \"Auto formatting when typing\"": "name: \"Auto formatting when typing\"", "name: \"Space between Chinese and English\"": "name: \"Space between Chinese and English\"", "name: \"Space between Chinese and Number\"": "name: \"Space between Chinese and Number\"", "name: \"Space between English and Number\"": "name: \"Space between English and Number\"", "name: \"Space between quote character > and text\"": "name: \"Space between quote character > and text\"", "name: \"Delete the Space between Chinese characters\"": "name: \"Delete the Space between Chinese characters\"", "name: \"Capitalize the first letter of every sentence\"": "name: \"Capitalize the first letter of every sentence\"", "name: \"Smartly insert space between text and punctuation\"": "name: \"Smartly insert space between text and punctuation\"", "name: \"Space strategy between inline code and text\"": "name: \"Space strategy between inline code and text\"", "name: \"Space strategy between inline formula and text\"": "name: \"Space strategy between inline formula and text\"", "name: \"Space strategy between link and text\"": "name: \"Space strategy between link and text\"", "name: \"User Defined RegExp Switch\"": "name: \"User Defined RegExp Switch\"", "name: \"User-defined Regular Expression, one expression per line\"": "name: \"User-defined Regular Expression, one expression per line\"", "name: \"Exclude Folders/Files\"": "name: \"Exclude Folders/Files\"", "name: \"Fix MacOS context-menu cursor position (Need to restart Obsidian)\"": "name: \"Fix MacOS context-menu cursor position (Need to restart Obsidian)\"", "name: \"Fix Microsoft Input Method Issue\"": "name: \"Fix Microsoft Input Method Issue\"", "name: \"Strict Line breaks Mode\"": "name: \"Strict Line breaks Mode\"", "name: \"Enhance Mod+A selection in text\"": "name: \"<PERSON><PERSON><PERSON>+A selection in text\"", "name: \"Punc rectify\"": "name: \"<PERSON><PERSON><PERSON> rectify\"", "name: \"Print debug info in console\"": "name: \"Print debug info in console\"", "name: \"Selection Replace Rule\"": "name: \"Selection Replace Rule\"", "name: \"Delete Rule\"": "name: \"Delete Rule\"", "name: \"Convert Rule\"": "name: \"Convert Rule\"", "name: \"Trigger\"": "name: \"<PERSON><PERSON>\"", "name: \"Left\"": "name: \"Left\"", "name: \"Right\"": "name: \"Right\"", "name: \"Old Pattern\"": "name: \"Old Pattern\"", "name: \"New Pattern\"": "name: \"New Pattern\"", "name: \"\\u7B26\\u53F7\\u81EA\\u52A8\\u914D\\u5BF9\\u53CA\\u5220\\u9664\\u914D\\u5BF9\"": "name: \"\\u7B26\\u53F7\\u81EA\\u52A8\\u914D\\u5BF9\\u53CA\\u5220\\u9664\\u914D\\u5BF9\"", "name: \"\\u9009\\u4E2D\\u6587\\u672C\\u66FF\\u6362\\u589E\\u5F3A\"": "name: \"\\u9009\\u4E2D\\u6587\\u672C\\u66FF\\u6362\\u589E\\u5F3A\"", "name: \"\\u8FDE\\u7EED\\u8F93\\u5165\\u5168\\u89D2\\u7B26\\u53F7\\u8F6C\\u534A\\u89D2\\u7B26\\u53F7\"": "name: \"\\u8FDE\\u7EED\\u8F93\\u5165\\u5168\\u89D2\\u7B26\\u53F7\\u8F6C\\u534A\\u89D2\\u7B26\\u53F7\"", "name: \"Obsidian \\u7684\\u57FA\\u7840\\u7B26\\u53F7\\u8F93\\u5165\\u589E\\u5F3A\"": "name: \"Obsidian \\u7684\\u57FA\\u7840\\u7B26\\u53F7\\u8F93\\u5165\\u589E\\u5F3A\"", "name: \"\\u589E\\u5F3A\\u4EE3\\u7801\\u5757\\u7F16\\u8F91\"": "name: \"\\u589E\\u5F3A\\u4EE3\\u7801\\u5757\\u7F16\\u8F91\"", "name: \"\\u589E\\u5F3A\\u5220\\u9664\\u529F\\u80FD\"": "name: \"\\u589E\\u5F3A\\u5220\\u9664\\u529F\\u80FD\"", "name: \"Tab \\u952E\\u5149\\u6807\\u8DF3\\u51FA\"": "name: \"Tab \\u952E\\u5149\\u6807\\u8DF3\\u51FA\"", "name: \"\\u8F93\\u5165\\u65F6\\u81EA\\u52A8\\u683C\\u5F0F\\u5316\"": "name: \"\\u8F93\\u5165\\u65F6\\u81EA\\u52A8\\u683C\\u5F0F\\u5316\"", "name: \"\\u4E2D\\u6587\\u4E0E\\u82F1\\u6587\\u4E4B\\u95F4\\u7684\\u7A7A\\u683C\"": "name: \"\\u4E2D\\u6587\\u4E0E\\u82F1\\u6587\\u4E4B\\u95F4\\u7684\\u7A7A\\u683C\"", "name: \"\\u4E2D\\u6587\\u4E0E\\u6570\\u5B57\\u4E4B\\u95F4\\u7684\\u7A7A\\u683C\"": "name: \"\\u4E2D\\u6587\\u4E0E\\u6570\\u5B57\\u4E4B\\u95F4\\u7684\\u7A7A\\u683C\"", "name: \"\\u82F1\\u6587\\u4E0E\\u6570\\u5B57\\u4E4B\\u95F4\\u7684\\u7A7A\\u683C\"": "name: \"\\u82F1\\u6587\\u4E0E\\u6570\\u5B57\\u4E4B\\u95F4\\u7684\\u7A7A\\u683C\"", "name: \"\\u5F15\\u7528\\u7B26\\u53F7 > \\u4E0E\\u6587\\u672C\\u4E4B\\u95F4\\u81EA\\u52A8\\u7A7A\\u683C\"": "name: \"\\u5F15\\u7528\\u7B26\\u53F7 > \\u4E0E\\u6587\\u672C\\u4E4B\\u95F4\\u81EA\\u52A8\\u7A7A\\u683C\"", "name: \"\\u5220\\u9664\\u4E2D\\u6587\\u5B57\\u7B26\\u95F4\\u7684\\u7A7A\\u683C\"": "name: \"\\u5220\\u9664\\u4E2D\\u6587\\u5B57\\u7B26\\u95F4\\u7684\\u7A7A\\u683C\"", "name: \"\\u53E5\\u9996\\u5B57\\u6BCD\\u5927\\u5199\"": "name: \"\\u53E5\\u9996\\u5B57\\u6BCD\\u5927\\u5199\"", "name: \"\\u667A\\u80FD\\u63D2\\u5165\\u7A7A\\u683C\"": "name: \"\\u667A\\u80FD\\u63D2\\u5165\\u7A7A\\u683C\"", "name: \"\\u884C\\u5185\\u4EE3\\u7801\\u548C\\u6587\\u672C\\u4E4B\\u95F4\\u7684\\u7A7A\\u683C\\u7B56\\u7565\"": "name: \"\\u884C\\u5185\\u4EE3\\u7801\\u548C\\u6587\\u672C\\u4E4B\\u95F4\\u7684\\u7A7A\\u683C\\u7B56\\u7565\"", "name: \"\\u884C\\u5185\\u516C\\u5F0F\\u548C\\u6587\\u672C\\u4E4B\\u95F4\\u7684\\u7A7A\\u683C\\u7B56\\u7565\"": "name: \"\\u884C\\u5185\\u516C\\u5F0F\\u548C\\u6587\\u672C\\u4E4B\\u95F4\\u7684\\u7A7A\\u683C\\u7B56\\u7565\"", "name: \"\\u94FE\\u63A5\\u548C\\u6587\\u672C\\u4E4B\\u95F4\\u7684\\u7A7A\\u683C\\u7B56\\u7565\"": "name: \"\\u94FE\\u63A5\\u548C\\u6587\\u672C\\u4E4B\\u95F4\\u7684\\u7A7A\\u683C\\u7B56\\u7565\"", "name: \"\\u7528\\u6237\\u5B9A\\u4E49\\u7684\\u6B63\\u5219\\u8868\\u8FBE\\u5F0F\\u5F00\\u5173\"": "name: \"\\u7528\\u6237\\u5B9A\\u4E49\\u7684\\u6B63\\u5219\\u8868\\u8FBE\\u5F0F\\u5F00\\u5173\"", "name: \"\\u7528\\u6237\\u5B9A\\u4E49\\u7684\\u6B63\\u5219\\u8868\\u8FBE\\u5F0F\"": "name: \"\\u7528\\u6237\\u5B9A\\u4E49\\u7684\\u6B63\\u5219\\u8868\\u8FBE\\u5F0F\"", "name: \"\\u6392\\u9664\\u6587\\u4EF6\\u5939/\\u6587\\u4EF6\"": "name: \"\\u6392\\u9664\\u6587\\u4EF6\\u5939/\\u6587\\u4EF6\"", "name: \"\\u4FEE\\u590D MacOS \\u53F3\\u952E\\u83DC\\u5355\\u5149\\u6807\\u4F4D\\u7F6E\"": "name: \"\\u4FEE\\u590D MacOS \\u53F3\\u952E\\u83DC\\u5355\\u5149\\u6807\\u4F4D\\u7F6E\"", "name: \"\\u4FEE\\u590D\\u5FAE\\u8F6F\\u8F93\\u5165\\u6CD5\\u95EE\\u9898\"": "name: \"\\u4FEE\\u590D\\u5FAE\\u8F6F\\u8F93\\u5165\\u6CD5\\u95EE\\u9898\"", "name: \"\\u4E25\\u683C\\u6362\\u884C\\u6A21\\u5F0F\\u56DE\\u8F66\\u589E\\u5F3A\"": "name: \"\\u4E25\\u683C\\u6362\\u884C\\u6A21\\u5F0F\\u56DE\\u8F66\\u589E\\u5F3A\"", "name: \"\\u589E\\u5F3A Ctrl/Cmd+A \\u529F\\u80FD\"": "name: \"\\u589E\\u5F3A Ctrl/Cmd+A \\u529F\\u80FD\"", "name: \"\\u6807\\u70B9\\u77EB\\u6B63\"": "name: \"\\u6807\\u70B9\\u77EB\\u6B63\"", "name: \"\\u5728\\u63A7\\u5236\\u53F0\\u8F93\\u51FA\\u8C03\\u8BD5\\u4FE1\\u606F\"": "name: \"\\u5728\\u63A7\\u5236\\u53F0\\u8F93\\u51FA\\u8C03\\u8BD5\\u4FE1\\u606F\"", "name: \"\\u9009\\u4E2D\\u66FF\\u6362\\u89C4\\u5219\"": "name: \"\\u9009\\u4E2D\\u66FF\\u6362\\u89C4\\u5219\"", "name: \"\\u5220\\u9664\\u89C4\\u5219\"": "name: \"\\u5220\\u9664\\u89C4\\u5219\"", "name: \"\\u8F6C\\u6362\\u89C4\\u5219\"": "name: \"\\u8F6C\\u6362\\u89C4\\u5219\"", "name: \"\\u89E6\\u53D1\\u5668\"": "name: \"\\u89E6\\u53D1\\u5668\"", "name: \"\\u5DE6\"": "name: \"\\u5DE6\"", "name: \"\\u53F3\"": "name: \"\\u53F3\"", "name: \"\\u65E7\\u6A21\\u5F0F\"": "name: \"\\u65E7\\u6A21\\u5F0F\"", "name: \"\\u65B0\\u6A21\\u5F0F\"": "name: \"\\u65B0\\u6A21\\u5F0F\"", "name: \"\\u0410\\u0432\\u0442\\u043E\\u043C\\u0430\\u0442\\u0438\\u0447\\u0435\\u0441\\u043A\\u043E\\u0435 \\u0434\\u043E\\u0431\\u0430\\u0432\\u043B\\u0435\\u043D\\u0438\\u0435 \\u0438 \\u0443\\u0434\\u0430\\u043B\\u0435\\u043D\\u0438\\u0435 \\u0441\\u0438\\u043C\\u0432\\u043E\\u043B\\u043E\\u0432 \\u043F\\u0430\\u0440\\u0430\"": "name: \"\\u0410\\u0432\\u0442\\u043E\\u043C\\u0430\\u0442\\u0438\\u0447\\u0435\\u0441\\u043A\\u043E\\u0435 \\u0434\\u043E\\u0431\\u0430\\u0432\\u043B\\u0435\\u043D\\u0438\\u0435 \\u0438 \\u0443\\u0434\\u0430\\u043B\\u0435\\u043D\\u0438\\u0435 \\u0441\\u0438\\u043C\\u0432\\u043E\\u043B\\u043E\\u0432 \\u043F\\u0430\\u0440\\u0430\"", "name: \"\\u0423\\u043B\\u0443\\u0447\\u0448\\u0435\\u043D\\u0438\\u0435 \\u0437\\u0430\\u043C\\u0435\\u043D\\u044B \\u0432\\u044B\\u0434\\u0435\\u043B\\u0435\\u043D\\u043D\\u043E\\u0433\\u043E \\u0442\\u0435\\u043A\\u0441\\u0442\\u0430\"": "name: \"\\u0423\\u043B\\u0443\\u0447\\u0448\\u0435\\u043D\\u0438\\u0435 \\u0437\\u0430\\u043C\\u0435\\u043D\\u044B \\u0432\\u044B\\u0434\\u0435\\u043B\\u0435\\u043D\\u043D\\u043E\\u0433\\u043E \\u0442\\u0435\\u043A\\u0441\\u0442\\u0430\"", "name: \"\\u0423\\u043B\\u0443\\u0447\\u0448\\u0435\\u043D\\u0438\\u0435 \\u0431\\u0430\\u0437\\u043E\\u0432\\u043E\\u0433\\u043E \\u0432\\u0432\\u043E\\u0434\\u0430 \\u0441\\u0438\\u043C\\u0432\\u043E\\u043B\\u043E\\u0432 \\u0434\\u043B\\u044F Obsidian\"": "name: \"\\u0423\\u043B\\u0443\\u0447\\u0448\\u0435\\u043D\\u0438\\u0435 \\u0431\\u0430\\u0437\\u043E\\u0432\\u043E\\u0433\\u043E \\u0432\\u0432\\u043E\\u0434\\u0430 \\u0441\\u0438\\u043C\\u0432\\u043E\\u043B\\u043E\\u0432 \\u0434\\u043B\\u044F Obsidian\"", "name: \"\\u0423\\u043B\\u0443\\u0447\\u0448\\u0435\\u043D\\u0438\\u0435 \\u0440\\u0435\\u0434\\u0430\\u043A\\u0442\\u0438\\u0440\\u043E\\u0432\\u0430\\u043D\\u0438\\u044F \\u043A\\u043E\\u0434\\u043E\\u0432\\u044B\\u0445 \\u0431\\u043B\\u043E\\u043A\\u043E\\u0432\"": "name: \"\\u0423\\u043B\\u0443\\u0447\\u0448\\u0435\\u043D\\u0438\\u0435 \\u0440\\u0435\\u0434\\u0430\\u043A\\u0442\\u0438\\u0440\\u043E\\u0432\\u0430\\u043D\\u0438\\u044F \\u043A\\u043E\\u0434\\u043E\\u0432\\u044B\\u0445 \\u0431\\u043B\\u043E\\u043A\\u043E\\u0432\"", "name: \"\\u0423\\u043B\\u0443\\u0447\\u0448\\u0435\\u043D\\u0438\\u0435 \\u0443\\u0434\\u0430\\u043B\\u0435\\u043D\\u0438\\u044F\"": "name: \"\\u0423\\u043B\\u0443\\u0447\\u0448\\u0435\\u043D\\u0438\\u0435 \\u0443\\u0434\\u0430\\u043B\\u0435\\u043D\\u0438\\u044F\"", "name: \"\\u0410\\u0432\\u0442\\u043E\\u0444\\u043E\\u0440\\u043C\\u0430\\u0442\\u0438\\u0440\\u043E\\u0432\\u0430\\u043D\\u0438\\u0435 \\u043F\\u0440\\u0438 \\u043D\\u0430\\u0431\\u043E\\u0440\\u0435 \\u0442\\u0435\\u043A\\u0441\\u0442\\u0430\"": "name: \"\\u0410\\u0432\\u0442\\u043E\\u0444\\u043E\\u0440\\u043C\\u0430\\u0442\\u0438\\u0440\\u043E\\u0432\\u0430\\u043D\\u0438\\u0435 \\u043F\\u0440\\u0438 \\u043D\\u0430\\u0431\\u043E\\u0440\\u0435 \\u0442\\u0435\\u043A\\u0441\\u0442\\u0430\"", "name: \"\\u041F\\u0440\\u043E\\u0431\\u0435\\u043B \\u043C\\u0435\\u0436\\u0434\\u0443 \\u043A\\u0438\\u0442\\u0430\\u0439\\u0441\\u043A\\u0438\\u043C\\u0438 \\u0438 \\u0430\\u043D\\u0433\\u043B\\u0438\\u0439\\u0441\\u043A\\u0438\\u043C\\u0438 \\u0441\\u0438\\u043C\\u0432\\u043E\\u043B\\u0430\\u043C\\u0438\"": "name: \"\\u041F\\u0440\\u043E\\u0431\\u0435\\u043B \\u043C\\u0435\\u0436\\u0434\\u0443 \\u043A\\u0438\\u0442\\u0430\\u0439\\u0441\\u043A\\u0438\\u043C\\u0438 \\u0438 \\u0430\\u043D\\u0433\\u043B\\u0438\\u0439\\u0441\\u043A\\u0438\\u043C\\u0438 \\u0441\\u0438\\u043C\\u0432\\u043E\\u043B\\u0430\\u043C\\u0438\"", "name: \"\\u041F\\u0440\\u043E\\u0431\\u0435\\u043B \\u043C\\u0435\\u0436\\u0434\\u0443 \\u043A\\u0438\\u0442\\u0430\\u0439\\u0441\\u043A\\u0438\\u043C\\u0438 \\u0441\\u0438\\u043C\\u0432\\u043E\\u043B\\u0430\\u043C\\u0438 \\u0438 \\u0447\\u0438\\u0441\\u043B\\u0430\\u043C\\u0438\"": "name: \"\\u041F\\u0440\\u043E\\u0431\\u0435\\u043B \\u043C\\u0435\\u0436\\u0434\\u0443 \\u043A\\u0438\\u0442\\u0430\\u0439\\u0441\\u043A\\u0438\\u043C\\u0438 \\u0441\\u0438\\u043C\\u0432\\u043E\\u043B\\u0430\\u043C\\u0438 \\u0438 \\u0447\\u0438\\u0441\\u043B\\u0430\\u043C\\u0438\"", "name: \"\\u041F\\u0440\\u043E\\u0431\\u0435\\u043B \\u043C\\u0435\\u0436\\u0434\\u0443 \\u0430\\u043D\\u0433\\u043B\\u0438\\u0439\\u0441\\u043A\\u0438\\u043C\\u0438 \\u0441\\u0438\\u043C\\u0432\\u043E\\u043B\\u0430\\u043C\\u0438 \\u0438 \\u0447\\u0438\\u0441\\u043B\\u0430\\u043C\\u0438\"": "name: \"\\u041F\\u0440\\u043E\\u0431\\u0435\\u043B \\u043C\\u0435\\u0436\\u0434\\u0443 \\u0430\\u043D\\u0433\\u043B\\u0438\\u0439\\u0441\\u043A\\u0438\\u043C\\u0438 \\u0441\\u0438\\u043C\\u0432\\u043E\\u043B\\u0430\\u043C\\u0438 \\u0438 \\u0447\\u0438\\u0441\\u043B\\u0430\\u043C\\u0438\"", "name: \"\\u041F\\u0440\\u043E\\u0431\\u0435\\u043B \\u043C\\u0435\\u0436\\u0434\\u0443 \\u0441\\u0438\\u043C\\u0432\\u043E\\u043B\\u043E\\u043C > \\u0438 \\u0442\\u0435\\u043A\\u0441\\u0442\\u043E\\u043C\"": "name: \"\\u041F\\u0440\\u043E\\u0431\\u0435\\u043B \\u043C\\u0435\\u0436\\u0434\\u0443 \\u0441\\u0438\\u043C\\u0432\\u043E\\u043B\\u043E\\u043C > \\u0438 \\u0442\\u0435\\u043A\\u0441\\u0442\\u043E\\u043C\"", "name: \"\\u0423\\u0434\\u0430\\u043B\\u0435\\u043D\\u0438\\u0435 \\u043F\\u0440\\u043E\\u0431\\u0435\\u043B\\u0430 \\u043C\\u0435\\u0436\\u0434\\u0443 \\u043A\\u0438\\u0442\\u0430\\u0439\\u0441\\u043A\\u0438\\u043C\\u0438 \\u0441\\u0438\\u043C\\u0432\\u043E\\u043B\\u0430\\u043C\\u0438\"": "name: \"\\u0423\\u0434\\u0430\\u043B\\u0435\\u043D\\u0438\\u0435 \\u043F\\u0440\\u043E\\u0431\\u0435\\u043B\\u0430 \\u043C\\u0435\\u0436\\u0434\\u0443 \\u043A\\u0438\\u0442\\u0430\\u0439\\u0441\\u043A\\u0438\\u043C\\u0438 \\u0441\\u0438\\u043C\\u0432\\u043E\\u043B\\u0430\\u043C\\u0438\"", "name: \"\\u0417\\u0430\\u0433\\u043B\\u0430\\u0432\\u043D\\u0430\\u044F \\u0431\\u0443\\u043A\\u0432\\u0430 \\u0432 \\u043D\\u0430\\u0447\\u0430\\u043B\\u0435 \\u043A\\u0430\\u0436\\u0434\\u043E\\u0433\\u043E \\u043F\\u0440\\u0435\\u0434\\u043B\\u043E\\u0436\\u0435\\u043D\\u0438\\u044F\"": "name: \"\\u0417\\u0430\\u0433\\u043B\\u0430\\u0432\\u043D\\u0430\\u044F \\u0431\\u0443\\u043A\\u0432\\u0430 \\u0432 \\u043D\\u0430\\u0447\\u0430\\u043B\\u0435 \\u043A\\u0430\\u0436\\u0434\\u043E\\u0433\\u043E \\u043F\\u0440\\u0435\\u0434\\u043B\\u043E\\u0436\\u0435\\u043D\\u0438\\u044F\"", "name: \"\\u0421\\u0442\\u0440\\u0430\\u0442\\u0435\\u0433\\u0438\\u044F \\u043F\\u0440\\u043E\\u0431\\u0435\\u043B\\u043E\\u0432 \\u043C\\u0435\\u0436\\u0434\\u0443 \\u0432\\u0441\\u0442\\u0440\\u043E\\u0435\\u043D\\u043D\\u044B\\u043C \\u043A\\u043E\\u0434\\u043E\\u043C \\u0438 \\u0442\\u0435\\u043A\\u0441\\u0442\\u043E\\u043C\"": "name: \"\\u0421\\u0442\\u0440\\u0430\\u0442\\u0435\\u0433\\u0438\\u044F \\u043F\\u0440\\u043E\\u0431\\u0435\\u043B\\u043E\\u0432 \\u043C\\u0435\\u0436\\u0434\\u0443 \\u0432\\u0441\\u0442\\u0440\\u043E\\u0435\\u043D\\u043D\\u044B\\u043C \\u043A\\u043E\\u0434\\u043E\\u043C \\u0438 \\u0442\\u0435\\u043A\\u0441\\u0442\\u043E\\u043C\"", "name: \"\\u0421\\u0442\\u0440\\u0430\\u0442\\u0435\\u0433\\u0438\\u044F \\u043F\\u0440\\u043E\\u0431\\u0435\\u043B\\u043E\\u0432 \\u043C\\u0435\\u0436\\u0434\\u0443 \\u0441\\u0441\\u044B\\u043B\\u043A\\u043E\\u0439 \\u0438 \\u0442\\u0435\\u043A\\u0441\\u0442\\u043E\\u043C\"": "name: \"\\u0421\\u0442\\u0440\\u0430\\u0442\\u0435\\u0433\\u0438\\u044F \\u043F\\u0440\\u043E\\u0431\\u0435\\u043B\\u043E\\u0432 \\u043C\\u0435\\u0436\\u0434\\u0443 \\u0441\\u0441\\u044B\\u043B\\u043A\\u043E\\u0439 \\u0438 \\u0442\\u0435\\u043A\\u0441\\u0442\\u043E\\u043C\"", "name: \"\\u041F\\u0435\\u0440\\u0435\\u043A\\u043B\\u044E\\u0447\\u0435\\u043D\\u0438\\u0435 \\u043F\\u043E\\u043B\\u044C\\u0437\\u043E\\u0432\\u0430\\u0442\\u0435\\u043B\\u044C\\u0441\\u043A\\u0438\\u0445 \\u0440\\u0435\\u0433\\u0443\\u043B\\u044F\\u0440\\u043D\\u044B\\u0445 \\u0432\\u044B\\u0440\\u0430\\u0436\\u0435\\u043D\\u0438\\u0439\"": "name: \"\\u041F\\u0435\\u0440\\u0435\\u043A\\u043B\\u044E\\u0447\\u0435\\u043D\\u0438\\u0435 \\u043F\\u043E\\u043B\\u044C\\u0437\\u043E\\u0432\\u0430\\u0442\\u0435\\u043B\\u044C\\u0441\\u043A\\u0438\\u0445 \\u0440\\u0435\\u0433\\u0443\\u043B\\u044F\\u0440\\u043D\\u044B\\u0445 \\u0432\\u044B\\u0440\\u0430\\u0436\\u0435\\u043D\\u0438\\u0439\"", "name: \"\\u0418\\u0441\\u043A\\u043B\\u044E\\u0447\\u0438\\u0442\\u044C \\u043F\\u0430\\u043F\\u043A\\u0438/\\u0444\\u0430\\u0439\\u043B\\u044B\"": "name: \"\\u0418\\u0441\\u043A\\u043B\\u044E\\u0447\\u0438\\u0442\\u044C \\u043F\\u0430\\u043F\\u043A\\u0438/\\u0444\\u0430\\u0439\\u043B\\u044B\"", "name: \"\\u0418\\u0441\\u043F\\u0440\\u0430\\u0432\\u043B\\u0435\\u043D\\u0438\\u0435 \\u043F\\u0440\\u043E\\u0431\\u043B\\u0435\\u043C\\u044B \\u0441 Microsoft Input Method\"": "name: \"\\u0418\\u0441\\u043F\\u0440\\u0430\\u0432\\u043B\\u0435\\u043D\\u0438\\u0435 \\u043F\\u0440\\u043E\\u0431\\u043B\\u0435\\u043C\\u044B \\u0441 Microsoft Input Method\"", "name: \"\\u0420\\u0435\\u0436\\u0438\\u043C \\u0441\\u0442\\u0440\\u043E\\u0433\\u0438\\u0445 \\u0440\\u0430\\u0437\\u0440\\u044B\\u0432\\u043E\\u0432 \\u0441\\u0442\\u0440\\u043E\\u043A\"": "name: \"\\u0420\\u0435\\u0436\\u0438\\u043C \\u0441\\u0442\\u0440\\u043E\\u0433\\u0438\\u0445 \\u0440\\u0430\\u0437\\u0440\\u044B\\u0432\\u043E\\u0432 \\u0441\\u0442\\u0440\\u043E\\u043A\"", "name: \"\\u0423\\u043B\\u0443\\u0447\\u0448\\u0438\\u0442\\u044C \\u0432\\u044B\\u0434\\u0435\\u043B\\u0435\\u043D\\u0438\\u0435 Mod+A \\u0432 \\u0442\\u0435\\u043A\\u0441\\u0442\\u0435\"": "name: \"\\u0423\\u043B\\u0443\\u0447\\u0448\\u0438\\u0442\\u044C \\u0432\\u044B\\u0434\\u0435\\u043B\\u0435\\u043D\\u0438\\u0435 Mod+A \\u0432 \\u0442\\u0435\\u043A\\u0441\\u0442\\u0435\"", "name: \"\\u041A\\u043E\\u0440\\u0440\\u0435\\u043A\\u0446\\u0438\\u044F \\u043F\\u0443\\u043D\\u043A\\u0442\\u0443\\u0430\\u0446\\u0438\\u0438\"": "name: \"\\u041A\\u043E\\u0440\\u0440\\u0435\\u043A\\u0446\\u0438\\u044F \\u043F\\u0443\\u043D\\u043A\\u0442\\u0443\\u0430\\u0446\\u0438\\u0438\"", "name: \"\\u0412\\u044B\\u0432\\u043E\\u0434 \\u043E\\u0442\\u043B\\u0430\\u0434\\u043E\\u0447\\u043D\\u043E\\u0439 \\u0438\\u043D\\u0444\\u043E\\u0440\\u043C\\u0430\\u0446\\u0438\\u0438 \\u0432 \\u043A\\u043E\\u043D\\u0441\\u043E\\u043B\\u044C\"": "name: \"\\u0412\\u044B\\u0432\\u043E\\u0434 \\u043E\\u0442\\u043B\\u0430\\u0434\\u043E\\u0447\\u043D\\u043E\\u0439 \\u0438\\u043D\\u0444\\u043E\\u0440\\u043C\\u0430\\u0446\\u0438\\u0438 \\u0432 \\u043A\\u043E\\u043D\\u0441\\u043E\\u043B\\u044C\"", "name: \"\\u041F\\u0440\\u0430\\u0432\\u0438\\u043B\\u043E \\u0437\\u0430\\u043C\\u0435\\u043D\\u044B \\u0432\\u044B\\u0434\\u0435\\u043B\\u0435\\u043D\\u043D\\u043E\\u0433\\u043E \\u0442\\u0435\\u043A\\u0441\\u0442\\u0430\"": "name: \"\\u041F\\u0440\\u0430\\u0432\\u0438\\u043B\\u043E \\u0437\\u0430\\u043C\\u0435\\u043D\\u044B \\u0432\\u044B\\u0434\\u0435\\u043B\\u0435\\u043D\\u043D\\u043E\\u0433\\u043E \\u0442\\u0435\\u043A\\u0441\\u0442\\u0430\"", "name: \"\\u041F\\u0440\\u0430\\u0432\\u0438\\u043B\\u043E \\u0443\\u0434\\u0430\\u043B\\u0435\\u043D\\u0438\\u044F\"": "name: \"\\u041F\\u0440\\u0430\\u0432\\u0438\\u043B\\u043E \\u0443\\u0434\\u0430\\u043B\\u0435\\u043D\\u0438\\u044F\"", "name: \"\\u041F\\u0440\\u0430\\u0432\\u0438\\u043B\\u043E \\u043F\\u0440\\u0435\\u043E\\u0431\\u0440\\u0430\\u0437\\u043E\\u0432\\u0430\\u043D\\u0438\\u044F\"": "name: \"\\u041F\\u0440\\u0430\\u0432\\u0438\\u043B\\u043E \\u043F\\u0440\\u0435\\u043E\\u0431\\u0440\\u0430\\u0437\\u043E\\u0432\\u0430\\u043D\\u0438\\u044F\"", "name: \"\\u0422\\u0440\\u0438\\u0433\\u0433\\u0435\\u0440\"": "name: \"\\u0422\\u0440\\u0438\\u0433\\u0433\\u0435\\u0440\"", "name: \"\\u041B\\u0435\\u0432\\u044B\\u0439\"": "name: \"\\u041B\\u0435\\u0432\\u044B\\u0439\"", "name: \"\\u041F\\u0440\\u0430\\u0432\\u044B\\u0439\"": "name: \"\\u041F\\u0440\\u0430\\u0432\\u044B\\u0439\"", "name: \"\\u0421\\u0442\\u0430\\u0440\\u044B\\u0439 \\u0448\\u0430\\u0431\\u043B\\u043E\\u043D\"": "name: \"\\u0421\\u0442\\u0430\\u0440\\u044B\\u0439 \\u0448\\u0430\\u0431\\u043B\\u043E\\u043D\"", "name: \"\\u041D\\u043E\\u0432\\u044B\\u0439 \\u0448\\u0430\\u0431\\u043B\\u043E\\u043D\"": "name: \"\\u041D\\u043E\\u0432\\u044B\\u0439 \\u0448\\u0430\\u0431\\u043B\\u043E\\u043D\"", "name: \"\\u7B26\\u865F\\u81EA\\u52D5\\u914D\\u5C0D\\u53CA\\u522A\\u9664\\u914D\\u5C0D\"": "name: \"\\u7B26\\u865F\\u81EA\\u52D5\\u914D\\u5C0D\\u53CA\\u522A\\u9664\\u914D\\u5C0D\"", "name: \"\\u9078\\u4E2D\\u6587\\u672C\\u66FF\\u63DB\\u589E\\u5F3A\"": "name: \"\\u9078\\u4E2D\\u6587\\u672C\\u66FF\\u63DB\\u589E\\u5F3A\"", "name: \"\\u9023\\u7E8C\\u8F38\\u5165\\u5168\\u89D2\\u7B26\\u865F\\u8F49\\u534A\\u89D2\\u7B26\\u865F\"": "name: \"\\u9023\\u7E8C\\u8F38\\u5165\\u5168\\u89D2\\u7B26\\u865F\\u8F49\\u534A\\u89D2\\u7B26\\u865F\"", "name: \"Obsidian \\u7684\\u57FA\\u790E\\u7B26\\u865F\\u8F38\\u5165\\u589E\\u5F3A\"": "name: \"Obsidian \\u7684\\u57FA\\u790E\\u7B26\\u865F\\u8F38\\u5165\\u589E\\u5F3A\"", "name: \"\\u589E\\u5F3A\\u4EE3\\u78BC\\u584A\\u7DE8\\u8F2F\"": "name: \"\\u589E\\u5F3A\\u4EE3\\u78BC\\u584A\\u7DE8\\u8F2F\"", "name: \"\\u589E\\u5F37\\u522A\\u9664\\u529F\\u80FD\"": "name: \"\\u589E\\u5F37\\u522A\\u9664\\u529F\\u80FD\"", "name: \"\\u8F38\\u5165\\u6642\\u81EA\\u52D5\\u683C\\u5F0F\\u5316\"": "name: \"\\u8F38\\u5165\\u6642\\u81EA\\u52D5\\u683C\\u5F0F\\u5316\"", "name: \"\\u4E2D\\u6587\\u8207\\u82F1\\u6587\\u4E4B\\u9593\\u7684\\u7A7A\\u683C\"": "name: \"\\u4E2D\\u6587\\u8207\\u82F1\\u6587\\u4E4B\\u9593\\u7684\\u7A7A\\u683C\"", "name: \"\\u4E2D\\u6587\\u8207\\u6578\\u5B57\\u4E4B\\u9593\\u7684\\u7A7A\\u683C\"": "name: \"\\u4E2D\\u6587\\u8207\\u6578\\u5B57\\u4E4B\\u9593\\u7684\\u7A7A\\u683C\"", "name: \"\\u82F1\\u6587\\u8207\\u6578\\u5B57\\u4E4B\\u9593\\u7684\\u7A7A\\u683C\"": "name: \"\\u82F1\\u6587\\u8207\\u6578\\u5B57\\u4E4B\\u9593\\u7684\\u7A7A\\u683C\"", "name: \"\\u5F15\\u7528\\u7B26\\u865F > \\u8207\\u6587\\u672C\\u4E4B\\u9593\\u81EA\\u52D5\\u7A7A\\u683C\"": "name: \"\\u5F15\\u7528\\u7B26\\u865F > \\u8207\\u6587\\u672C\\u4E4B\\u9593\\u81EA\\u52D5\\u7A7A\\u683C\"", "name: \"\\u522A\\u9664\\u4E2D\\u6587\\u5B57\\u7B26\\u9593\\u7684\\u7A7A\\u683C\"": "name: \"\\u522A\\u9664\\u4E2D\\u6587\\u5B57\\u7B26\\u9593\\u7684\\u7A7A\\u683C\"", "name: \"\\u53E5\\u9996\\u5B57\\u6BCD\\u5927\\u5BEB\"": "name: \"\\u53E5\\u9996\\u5B57\\u6BCD\\u5927\\u5BEB\"", "name: \"\\u884C\\u5167\\u4EE3\\u78BC\\u548C\\u6587\\u672C\\u4E4B\\u9593\\u7684\\u7A7A\\u683C\\u7B56\\u7565\"": "name: \"\\u884C\\u5167\\u4EE3\\u78BC\\u548C\\u6587\\u672C\\u4E4B\\u9593\\u7684\\u7A7A\\u683C\\u7B56\\u7565\"", "name: \"\\u884C\\u5167\\u516C\\u5F0F\\u548C\\u6587\\u672C\\u4E4B\\u9593\\u7684\\u7A7A\\u683C\\u7B56\\u7565\"": "name: \"\\u884C\\u5167\\u516C\\u5F0F\\u548C\\u6587\\u672C\\u4E4B\\u9593\\u7684\\u7A7A\\u683C\\u7B56\\u7565\"", "name: \"\\u9023\\u7D50\\u548C\\u6587\\u672C\\u4E4B\\u9593\\u7684\\u7A7A\\u683C\\u7B56\\u7565\"": "name: \"\\u9023\\u7D50\\u548C\\u6587\\u672C\\u4E4B\\u9593\\u7684\\u7A7A\\u683C\\u7B56\\u7565\"", "name: \"\\u7528\\u6236\\u5B9A\\u7FA9\\u7684\\u6B63\\u5247\\u8868\\u9054\\u5F0F\\u958B\\u95DC\"": "name: \"\\u7528\\u6236\\u5B9A\\u7FA9\\u7684\\u6B63\\u5247\\u8868\\u9054\\u5F0F\\u958B\\u95DC\"", "name: \"\\u7528\\u6236\\u5B9A\\u7FA9\\u7684\\u6B63\\u5247\\u8868\\u9054\\u5F0F\"": "name: \"\\u7528\\u6236\\u5B9A\\u7FA9\\u7684\\u6B63\\u5247\\u8868\\u9054\\u5F0F\"", "name: \"\\u6392\\u9664\\u6587\\u4EF6\\u593E/\\u6587\\u4EF6\"": "name: \"\\u6392\\u9664\\u6587\\u4EF6\\u593E/\\u6587\\u4EF6\"", "name: \"\\u4FEE\\u5FA9 MacOS \\u53F3\\u9375\\u83DC\\u55AE\\u5149\\u6A19\\u4F4D\\u7F6E\"": "name: \"\\u4FEE\\u5FA9 MacOS \\u53F3\\u9375\\u83DC\\u55AE\\u5149\\u6A19\\u4F4D\\u7F6E\"", "name: \"\\u4FEE\\u5FA9\\u5FAE\\u8EDF\\u8F38\\u5165\\u6CD5\\u554F\\u984C\"": "name: \"\\u4FEE\\u5FA9\\u5FAE\\u8EDF\\u8F38\\u5165\\u6CD5\\u554F\\u984C\"", "name: \"\\u56B4\\u683C\\u63DB\\u884C\\u6A21\\u5F0F\\u56DE\\u8ECA\\u589E\\u5F37\"": "name: \"\\u56B4\\u683C\\u63DB\\u884C\\u6A21\\u5F0F\\u56DE\\u8ECA\\u589E\\u5F37\"", "name: \"\\u589E\\u5F37 Mod+A \\u529F\\u80FD\"": "name: \"\\u589E\\u5F37 Mod+A \\u529F\\u80FD\"", "name: \"\\u6A19\\u9EDE\\u77EB\\u6B63\"": "name: \"\\u6A19\\u9EDE\\u77EB\\u6B63\"", "name: \"\\u5728\\u63A7\\u5236\\u53F0\\u8F38\\u51FA\\u8ABF\\u8A66\\u8CC7\\u8A0A\"": "name: \"\\u5728\\u63A7\\u5236\\u53F0\\u8F38\\u51FA\\u8ABF\\u8A66\\u8CC7\\u8A0A\"", "name: \"\\u9078\\u4E2D\\u66FF\\u63DB\\u898F\\u5247\"": "name: \"\\u9078\\u4E2D\\u66FF\\u63DB\\u898F\\u5247\"", "name: \"\\u522A\\u9664\\u898F\\u5247\"": "name: \"\\u522A\\u9664\\u898F\\u5247\"", "name: \"\\u8F49\\u63DB\\u898F\\u5247\"": "name: \"\\u8F49\\u63DB\\u898F\\u5247\"", "name: \"\\u89F8\\u767C\\u5668\"": "name: \"\\u89F8\\u767C\\u5668\"", "name: \"\\u820A\\u6A21\\u5F0F\"": "name: \"\\u820A\\u6A21\\u5F0F\"", "text: \" \"": "text: \" \"", "text: \"Yifeng Nguyen: A Concise Tutorial on Regular Expressions\"": "text: \"<PERSON><PERSON>: A Concise Tutorial on Regular Expressions\"", "text: \"Customizing Regular Expression Rules\"": "text: \"Customizing Regular Expression Rules\"", "text: \"\\u300A\\u962E\\u4E00\\u5CF0\\uFF1A\\u6B63\\u5219\\u8868\\u8FBE\\u5F0F\\u7B80\\u660E\\u6559\\u7A0B\\u300B\"": "text: \"\\u300A\\u962E\\u4E00\\u5CF0\\uFF1A\\u6B63\\u5219\\u8868\\u8FBE\\u5F0F\\u7B80\\u660E\\u6559\\u7A0B\\u300B\"", "text: \"\\u81EA\\u5B9A\\u4E49\\u6B63\\u5219\\u8868\\u8FBE\\u5F0F\\u89C4\\u5219\"": "text: \"\\u81EA\\u5B9A\\u4E49\\u6B63\\u5219\\u8868\\u8FBE\\u5F0F\\u89C4\\u5219\"", "text: \"Yifeng Nguyen: \\u041A\\u0440\\u0430\\u0442\\u043A\\u043E\\u0435 \\u0440\\u0443\\u043A\\u043E\\u0432\\u043E\\u0434\\u0441\\u0442\\u0432\\u043E \\u043F\\u043E \\u0440\\u0435\\u0433\\u0443\\u043B\\u044F\\u0440\\u043D\\u044B\\u043C \\u0432\\u044B\\u0440\\u0430\\u0436\\u0435\\u043D\\u0438\\u044F\\u043C\"": "text: \"<PERSON><PERSON>: \\u041A\\u0440\\u0430\\u0442\\u043A\\u043E\\u0435 \\u0440\\u0443\\u043A\\u043E\\u0432\\u043E\\u0434\\u0441\\u0442\\u0432\\u043E \\u043F\\u043E \\u0440\\u0435\\u0433\\u0443\\u043B\\u044F\\u0440\\u043D\\u044B\\u043C \\u0432\\u044B\\u0440\\u0430\\u0436\\u0435\\u043D\\u0438\\u044F\\u043C\"", "text: \"\\u300A\\u962E\\u4E00\\u5CF0\\uFF1A\\u6B63\\u5247\\u8868\\u9054\\u5F0F\\u7C21\\u660E\\u6559\\u7A0B\\u300B\"": "text: \"\\u300A\\u962E\\u4E00\\u5CF0\\uFF1A\\u6B63\\u5247\\u8868\\u9054\\u5F0F\\u7C21\\u660E\\u6559\\u7A0B\\u300B\"", "text: \"\\u81EA\\u5B9A\\u7FA9\\u6B63\\u5247\\u8868\\u9054\\u5F0F\\u898F\\u5247\"": "text: \"\\u81EA\\u5B9A\\u7FA9\\u6B63\\u5247\\u8868\\u9054\\u5F0F\\u898F\\u5247\"", "text: \"easy-typing-obsidian\"": "text: \"easy-typing-obsidian\"", "text: \"Edit \"": "text: \"Edit \"", ".setButtonText(\"+\")": ".setButtonText(\"+\")"}}