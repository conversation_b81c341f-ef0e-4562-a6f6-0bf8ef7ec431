{"manifest": {"translationVersion": 1743603304028, "pluginVersion": "1.0.6"}, "description": {"original": "Easily install a beta version of a plugin for testing.", "translation": "Easily install a beta version of a plugin for testing."}, "dict": {"Notice(\"Failed to read the daily note template\")": "Notice(\"读取日报模板失败\")", "Notice(\"Unable to create new file.\")": "Notice(\"无法创建新文件。\")", "Notice(`BRAT\n${t}\n${i}`,n*1e3)": "Notice(`BRAT\n${t}\n${i}`,n*1e3)", "Notice(`BRAT\n${i}`,3e4)": "Notice(`BRAT\n${i}`,3e4)", "Notice(`BRAT\n${e}`,3e4)": "Notice(`BRAT\n${e}`,3e4)", ".log(\"error in isPrivateRepo\",e,i)": ".log(\"isPrivateRepo中的错误\",e,i)", ".log(\"error in grabReleaseFileFromRepository\",URL,o)": ".log(\"GrabreleaseFileFromRepository中的错误\",URL,o)", ".log(\"grabManifestJsonFromRepository manifestJsonPath\",i)": ".log(\"grabManifestJsonFromRepository manifestJsonPath\",i)", ".log(\"Token validation error:\",g)": ".log(\"令牌验证错误：\",g)", ".log(\"Token valid:\",a)": ".log(\"令牌有效：\",a)", ".log(\"grabManifestJsonFromRepository response\",l)": ".log(\"grabManifestJsonFromRepository响应\",l)", ".log(`error in grabManifestJsonFromRepository for ${i}`,l)": ".log(`${i}的grabManifestJsonFromRepository中出错`,l)", ".log(\"error in grabCommmunityPluginList\",n)": ".log(\"grabCommunityPluginList中出错\",n)", ".log(\"error in grabCommmunityThemesList\",n)": ".log(\"抓取社区主题列表时出错\",n)", ".log(\"error in grabCommmunityThemeCssFile\",i)": ".log(\"抓取CommmunityThemeCsf文件时出错\",i)", ".log(\"error in grabCommmunityThemeManifestFile\",e)": ".log(\"抓取CommmunityThemeManifest文件时出错\",e)", ".log(\"error in grabLastCommitInfoForAFile\",i)": ".log(\"GrablastCommitinFoForafile中的错误\",i)", ".log({reallyGetManifestOrNot:a,version:o})": ".log({reallyGetManifestOrNot:a,version:o})", ".log(\"BRAT: addPlugin\",t,n,e,i,o,a,l)": ".log(\"BRAT:addPlugin\",t,n,e,i,o,a,l)", ".log(c,!0)": ".log(c,!0)", ".log(\"rFiles\",c)": ".log(\"r文件\",c)", ".log(\"BRAT: rFiles.manifest\",p,c)": ".log(\"BRAT:rFiles.manifest\",p,c)", ".log(m,!0)": ".log(m,!0)", ".log(`${t} reinstalled`,!0)": ".log(`${t}已重新安装`,!0)", ".log(f,!0)": ".log(f,!0)", ".log(\"BRAT - Local Manifest Load\",r.id,JSON.stringify(w,null,2)": ".log(\"BRAT-本地舱单负载\",r.id,JSON.stringify(w,null,2)", ".log(`${h}[Release Info](https://github.com/${t}/releases/tag/${r.version})": ".log(`${h}[Release Info](https://github.com/${t}/releases/tag/${r.version})", ".log(\"reload plugin\",e)": ".log(\"重新加载插件\",e)", ".log(\"BRAT: No internet detected.\")": ".log(\"BRAT：未检测到互联网。\")", ".log(i,!0)": ".log(i,!0)", ".log(a,!0)": ".log(a,!0)", ".log(n,!0)": ".log(n,!0)", ".log(`${g}[Theme Info](https://github.com/${t})": ".log(`${g}[Theme Info](https://github.com/${t})", ".log(e,!0)": ".log(e,!0)", ".log(\"BRAT: lastUpdateOnline\",a)": ".log(\"BRAT：最新在线更新\",a)", ".log(o,!0)": ".log(o,!0)", ".log(`${e.display} plugin disabled`,!1)": ".log(`${e.display}插件已禁用`,!1)", ".log(e.info)": ".log(e.info)", ".log(`${e.display} plugin enabled`,!1)": ".log(`${e.display}插件已启用`,!1)", ".log(`BRAT: ${t}`,...n)": ".log(`错误：$｛t｝`,...n)", ".log(`BRAT: ${t}`)": ".log(`错误：$｛t｝`)", ".log(`loading ${this.APP_NAME}`)": ".log(`正在加载${this.APP_NAME}`)", " log(n,e=!1)": " log(n,e=!1)", ".log(`unloading ${this.APP_NAME}`)": ".log(`卸载${this.APP_NAME}`)", ".error(`Failed to read the daily note template '${e}'`,i)": ".error(`未能读取日记模板“${e}”`,i)", ".error(`Failed to create file: '${p}'`,T)": ".error(`创建文件失败：“$｛p｝”`,T)", ".error(`Failed to create file: '${g}'`,r)": ".error(`创建文件失败：“$｛g｝”`,r)", ".error(\"BRAT: validateRepository\",t,n,e)": ".error(\"BRAT：验证存储\",t,n,e)", ".error(\"Failed to load settings:\",n)": ".error(\"加载设置失败：\",n)", "name:\"Plugins: Add a beta plugin for testing\"": "name:\"插件：添加测试插件\"", "name:\"Plugins: Add a beta plugin with frozen version based on a release tag\"": "name:\"插件：添加一个基于发布标签的冻结版本的测试版插件\"", "name:\"Plugins: Check for updates to all beta plugins and UPDATE\"": "name:\"插件：检查所有测试版插件的更新并更新\"", "name:\"Plugins: Only check for updates to beta plugins, but don't Update\"": "name:\"插件：只检查测试版插件的更新，但不更新\"", "name:\"Plugins: Choose a single plugin version to update\"": "name:\"插件：选择一个插件版本进行更新\"", "name:\"Plugins: Choose a single plugin to reinstall\"": "name:\"插件：选择一个插件重新安装\"", "name:\"Plugins: Restart a plugin that is already installed\"": "name:\"插件：重新启动已安装的插件\"", "name:\"Plugins: Disable a plugin - toggle it off\"": "name:\"插件：禁用插件-将其关闭\"", "name:\"Plugins: Enable a plugin - toggle it on\"": "name:\"插件：启用插件-打开它\"", "name:\"Plugins: Open the GitHub repository for a plugin\"": "name:\"插件：打开插件的GitHub存储库\"", "name:\"Themes: Open the GitHub repository for a theme (appearance)\"": "name:\"主题：打开主题的GitHub存储库（外观）\"", "name:\"Plugins: Open Plugin Settings Tab\"": "name:\"插件：打开插件设置选项卡\"", "name:\"Themes: Grab a beta theme for testing from a Github repository\"": "name:\"主题：从Github仓库中获取测试主题进行测试\"", "name:\"Themes: Update beta themes\"": "name:\"主题：更新测试版主题\"", "name:\"All Commands list\"": "name:\"所有命令列表\"", "text:\"Github repository for beta plugin:\"": "text:\"Github测试版插件仓库：\"", "text:\"Never mind\"": "text:\"没关系\"", "text:\"Add Plugin\"": "text:\"添加插件\"", "text:\"Github repository for beta theme:\"": "text:\"Github beta主题仓库：\"", "text:\"Add Theme\"": "text:\"添加主题\"", "text:'The following is a list of beta plugins added via the command palette \"Add a beta plugin for testing\" or \"Add a beta plugin with frozen version for testing\". A frozen version is a specific release of a plugin based on its release tag. '": "text:'以下是通过命令面板“添加用于测试的beta插件”或“添加具有冻结版本的beta插件用于测试”添加的beta插件列表。冻结版本是基于其发布标签的插件的特定版本。'", "text:\"Click the x button next to a plugin to remove it from the list.\"": "text:\"单击插件旁边的x按钮将其从列表中删除。\"", "text:\"Note: \"": "text:\"注：\"", "text:\"This does not delete the plugin, this should be done from the  Community Plugins tab in Settings.\"": "text:\"这不会删除插件，这应该从“设置”中的“社区插件”选项卡中完成。\"", ".setButtonText(\"Add Beta plugin\")": ".setButtonText(\"添加Beta插件\")", ".setButtonText(\"Click once more to confirm removal\")": ".setButtonText(\"再次单击以确认删除\")", ".setButtonText(\"Add Beta plugin with frozen version\")": ".setButtonText(\"添加冻结版本的Beta插件\")", ".setButtonText(\"Add Beta Theme\")": ".setButtonText(\"添加Beta主题\")", ".setName(\"Auto-enable plugins after installation\")": ".setName(\"安装后自动启用插件\")", ".setName(\"Auto-update plugins at startup\")": ".setName(\"启动时自动更新插件\")", ".setName(\"Auto-update themes at startup\")": ".setName(\"启动时自动更新主题\")", ".setName(\"Beta plugin list\")": ".setName(\"Beta插件列表\")", ".setName(\"Beta themes list\")": ".setName(\"Beta主题列表\")", ".setName(\"Monitoring\")": ".setName(\"监视\")", ".setName(\"Enable Notifications\")": ".setName(\"启用通知\")", ".setName(\"Enable Logging\")": ".setName(\"启用日志记录\")", ".setName(\"BRAT Log File Location\")": ".setName(\"BRAT日志文件位置\")", ".setName(\"Enable Verbose Logging\")": ".setName(\"启用详细日志记录\")", ".setName(\"Debugging Mode\")": ".setName(\"调试模式\")", ".setName(\"Personal Access Token\")": ".setName(\"个人访问令牌\")", ".setDesc('If enabled beta plugins will be automatically enabled after installtion by default. Note: you can toggle this on and off for each plugin in the \"Add Plugin\" form.')": ".setDesc('如果启用了测试版插件，默认情况下安装后会自动启用。注意：您可以在“添加插件”窗体中为每个插件打开和关闭此选项。')", ".setDesc(\"If enabled all beta plugins will be checked for updates each time Obsidian starts. Note: this does not update frozen version plugins.\")": ".setDesc(\"如果启用，每次Obsidian启动时都会检查所有测试版插件的更新情况。注意：这不会更新冻结版本的插件。\")", ".setDesc(\"If enabled all beta themes will be checked for updates each time Obsidian starts.\")": ".setDesc(\"如果启用，每次黑曜石启动时都会检查所有测试版主题是否有更新。\")", ".setDesc(\"BRAT will provide popup notifications for its various activities. Turn this off means  no notifications from BRAT.\")": ".setDesc(\"BRAT将为其各种活动提供弹出通知。关闭此选项意味着BRAT不会发出通知。\")", ".setDesc(\"Plugin updates will be logged to a file in the log file.\")": ".setDesc(\"插件更新将记录到日志文件中的一个文件中。\")", ".setDesc(\"Logs will be saved to this file. Don't add .md to the file name.\")": ".setDesc(\"日志将保存到此文件中。不要在文件名中添加.md。\")", ".setDesc(\"Get a lot  more information in  the log.\")": ".setDesc(\"在日志中获取更多信息。\")", ".setDesc(\"Atomic Bomb level console logging. Can be used for troubleshoting and development.\")": ".setDesc(\"原子弹级控制台日志记录。可用于故障排除和开发。\")", ".setDesc(\"If you need to access private repositories, enter the personal access token here.\")": ".setDesc(\"如果您需要访问私有存储库，请在此处输入个人访问令牌。\")", ".setPlaceholder(\"Repository (example: https://github.com/GitubUserName/repository-name)\")": ".setPlaceholder(\"存储库（示例：https://github.com/GitubUserName/repository-name)\")", ".setPlaceholder(\"Specify the release version tag (example: 1.0.0)\")": ".setPlaceholder(\"指定发布版本标签（例如：1.0.0）\")", ".setPlaceholder(\"Repository (example: https://github.com/GitubUserName/repository-name\")": ".setPlaceholder(\"存储库（示例：https://github.com/GitubUserName/repository-name\")", ".setPlaceholder(\"Example: BRAT-log\")": ".setPlaceholder(\"示例：BRAT日志\")", ".setPlaceholder(\"Enter your personal access token\")": ".setPlaceholder(\"输入您的个人访问令牌\")", ".setTooltip(\"Delete this beta plugin\")": ".setTooltip(\"删除此测试版插件\")", ".setTooltip(\"Delete this beta theme\")": ".setTooltip(\"删除此测试版主题\")", ".appendText(\"Enable after installing the plugin\")": ".appendText(\"安装插件后启用\")", ".innerText=\"Learn more about my work at:\"": ".innerText=\"了解更多关于我的工作：\"", ".innerText=\"https://tfthacker.com\"": ".innerText=\"https://tfthacker.com\""}}