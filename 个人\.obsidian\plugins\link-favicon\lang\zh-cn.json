{"manifest": {"translationVersion": -639075600000, "pluginVersion": "1.8.4"}, "description": {"original": "See the favicon for a linked website.", "translation": "查看链接网站的favicon。"}, "dict": {"Notice(\"Please supply both a \"+this.name+\" & a icon\")": "Notice(\"请同时提供\"+this.name+\"和图标\")", "Notice(\"Cleared cache\")": "Notice(\"缓存已清除\")", "Notice(\"Link favicons:misconfigured providers, please check the settings\")": "Notice(\"链接favicon: 配置错误的提供者，请检查设置\")", ".log(\"empty icon for \"+this.qualifier)": ".log(\"空图标：\"+this.qualifier)", ".log(\"no icon for \"+n.href)": ".log(\"没有图标：\"+n.href)", ".log(\"enabling plugin: link favicons\")": ".log(\"启用插件：链接favicon\")", ".log(\"disabling plugin: link favicons\")": ".log(\"禁用插件：链接favicon\")", ".error(t)": ".error(t)", ".error(e)": ".error(e)", ".error(\"Link Favicons: misconfigured providers\")": ".error(\"链接favicon: 配置错误的提供者\")", ".error(m)": ".error(m)", ".error(o)": ".error(o)", ".error(c)": ".error(c)", ".error(\"could not extract color information from icon\")": ".error(\"无法从图标中提取颜色信息\")", ".error(s)": ".error(s)", ".error(a)": ".error(a)", "name:\"Google\"": "name:\"谷歌\"", "name:\"DuckDuckGo\"": "name:\"鸭鸭搜\"", "name:\"Icon Horse\"": "name:\"图标马\"", "name:\"Splitbee\"": "name:\"<PERSON><PERSON>\"", "name:\"The Favicon Finder\"": "name:\"Favicon 查找器\"", "name:\"Favicon Grabber\"": "name:\"Favicon 获取器\"", "name:\"applyStyles\"": "name:\"应用样式\"", "name:\"arrow\"": "name:\"箭头\"", "name:\"computeStyles\"": "name:\"计算样式\"", "name:\"eventListeners\"": "name:\"事件监听器\"", "name:\"flip\"": "name:\"翻转\"", "name:\"hide\"": "name:\"隐藏\"", "name:\"offset\"": "name:\"偏移\"", "name:\"popperOffsets\"": "name:\"弹出偏移\"", "name:\"preventOverflow\"": "name:\"防止溢出\"", "name:\"sameWidth\"": "name:\"同宽\"", "text:\"reference\"": "text:\"参考\"", "text:\"Could not generate favicon, check your settings\"": "text:\"无法生成favicon，请检查设置\"", "text:\"Design\"": "text:\"设计\"", "text:\"Custom icons\"": "text:\"自定义图标\"", "text:\"for domains\"": "text:\"用于域名\"", "text:\"\t\t\"": "text:\"\t\t\"", "text:\"for URI schemas\"": "text:\"用于URI模式\"", "text:\"Advanced\"": "text:\"高级\"", "text:\"Debugging tools\"": "text:\"调试工具\"", "text:\"Only use these tools if you know what you are doing\"": "text:\"仅在知道操作时使用这些工具\"", "text:\"Cached icons\"": "text:\"缓存的图标\"", ".setButtonText(\"Choose\")": ".setButtonText(\"选择\")", ".setButtonText(\"Test\")": ".setButtonText(\"测试\")", ".setButtonText(\"Test Providers\")": ".setButtonText(\"测试提供者\")", ".setButtonText(\"Clear\")": ".setButtonText(\"清除\")", ".setName(\"Icon\")": ".setName(\"图标\")", ".setName(\"Link\")": ".setName(\"链接\")", ".setName(\"\").addButton(t=>{t.setButtonText(\"Test\")": ".setName(\"\").addButton(t=>{t.setButtonText(\"测试\")", ".setName(\"Icon provider\")": ".setName(\"图标提供者\")", ".setName(\"Provider domain\")": ".setName(\"提供者域名\")", ".setName(\"Fallback icon provider\")": ".setName(\"备用图标提供者\")", ".setName(\"Fallback provider domain\")": ".setName(\"备用提供者域名\")", ".setName(\"Not sure which provider to choose?\")": ".setName(\"不确定选择哪个提供者？\")", ".setName(\"Ignored domains\")": ".setName(\"忽略的域名\")", ".setName(\"Show icon when link has alias\")": ".setName(\"当链接有别名时显示图标\")", ".setName(\"Show icon when link has no alias\")": ".setName(\"当链接没有别名时显示图标\")", ".setName(\"Show in Reading mode\")": ".setName(\"在阅读模式下显示\")", ".setName(\"Show in Source mode\")": ".setName(\"在源码模式下显示\")", ".setName(\"Show in live preview\")": ".setName(\"在实时预览中显示\")", ".setName(\"Icon Position\")": ".setName(\"图标位置\")", ".setName(\"Color inversion\")": ".setName(\"颜色反转\")", ".setName(\"Add new\")": ".setName(\"添加新的\")", ".setName(\"Debounce\")": ".setName(\"去抖动\")", ".setName(\"Clear icon cache\")": ".setName(\"清除图标缓存\")", ".setDesc(\"This Provider is selfhosted, please specify your deployment url. Refer to the readme of the provider for deployment instructions.\")": ".setDesc(\"该提供者为自托管，请指定部署URL。请参考提供者的自托管说明文档。\")", ".setDesc(\"This Provider is be selfhosted, please specify your deployment url. Refer to the readme of the provider for deployment instructions.\")": ".setDesc(\"该提供者可自托管，请指定部署URL。请参考提供者的自托管说明文档。\")", ".setDesc(\"Don't show an favicon for these domains(one per line)\")": ".setDesc(\"不要为这些域名显示favicon（每行一个）\")", ".setDesc(\"When link is formatted like: [Obsidian](https://obsidian.md/)\")": ".setDesc(\"当链接格式如：[Obsidian](https://obsidian.md/)时\")", ".setDesc(\"When link is formatted like: https://obsidian.md/\")": ".setDesc(\"当链接格式如：https://obsidian.md/时\")", ".setDesc(\"Favicon colors will be automatically inverted if the icon is detected to be less readable\")": ".setDesc(\"如果检测到图标可读性较差，将自动反转favicon颜色\")", ".setDesc(\"Add custom icon\")": ".setDesc(\"添加自定义图标\")", ".setDesc(\"How fast after editing a link should a icon be displayed(in milliseconds)?\")": ".setDesc(\"编辑链接后多快显示图标（毫秒）？\")", ".setDesc(\"Remove all icons from cache\")": ".setDesc(\"从缓存中移除所有图标\")", ".setTooltip(\"Save\")": ".setTooltip(\"保存\")", ".setTooltip(\"Cancel\")": ".setTooltip(\"取消\")", ".setTooltip(\"add custom icon\")": ".setTooltip(\"添加自定义图标\")", ".setTooltip(\"Edit\")": ".setTooltip(\"编辑\")", ".setTooltip(\"Delete\")": ".setTooltip(\"删除\")", "addOption(\"front\",\"Before the link\")": "addOption(\"front\",\"链接前\")", ".addOption(\"back\",\"After the link\")": ".addOption(\"back\",\"链接后\")"}}