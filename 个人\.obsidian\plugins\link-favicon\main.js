/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
https://github.com/joethei/obisidian-link-favicon
*/

var Ir=Object.create;var Ye=Object.defineProperty;var br=Object.getOwnPropertyDescriptor;var Ur=Object.getOwnPropertyNames;var Pr=Object.getPrototypeOf,Tr=Object.prototype.hasOwnProperty;var lo=o=>Ye(o,"__esModule",{value:!0});var Te=(o,e)=>()=>(o&&(e=o(o=0)),e);var mo=(o,e)=>()=>(e||o((e={exports:{}}).exports,e),e.exports),uo=(o,e)=>{lo(o);for(var t in e)Ye(o,t,{get:e[t],enumerable:!0})},yr=(o,e,t)=>{if(e&&typeof e=="object"||typeof e=="function")for(let r of Ur(e))!Tr.call(o,r)&&r!=="default"&&Ye(o,r,{get:()=>e[r],enumerable:!(t=br(e,r))||t.enumerable});return o},N=o=>yr(lo(Ye(o!=null?Ir(Pr(o)):{},"default",o&&o.__esModule&&"default"in o?{get:()=>o.default,enumerable:!0}:{value:o,enumerable:!0})),o);var v=(o,e,t)=>new Promise((r,n)=>{var s=c=>{try{a(t.next(c))}catch(l){n(l)}},i=c=>{try{a(t.throw(c))}catch(l){n(l)}},a=c=>c.done?r(c.value):Promise.resolve(c.value).then(s,i);a((t=t.apply(o,e)).next())});var bt,L,ke=Te(()=>{bt=N(require("obsidian")),L={google:{name:"谷歌",url:o=>Promise.resolve("https://www.google.com/s2/favicons?domain="+o)},duckduckgo:{name:"鸭鸭搜",url:o=>Promise.resolve("https://icons.duckduckgo.com/ip3/"+o+".ico")},iconhorse:{name:"图标马",url:o=>Promise.resolve("https://icon.horse/icon/"+o)},splitbee:{name:"Splitbee",url:o=>Promise.resolve("https://favicon.splitbee.io/?url="+o)},besticon:{name:"Favicon 查找器",url:(o,e)=>v(void 0,null,function*(){try{let t=e.provider==="besticon"?e.providerDomain:e.fallbackProviderDomain,r=yield(0,bt.requestUrl)({url:t+"/allicons.json?url="+o});return r.json.icons.length===0?Promise.reject("besticon: no icons for domain "+o):Promise.resolve(r.json.icons[0].url)}catch(t){return console.error(t),Promise.reject("besticon: failed to retrieve icon for "+o)}})},favicongrabber:{name:"Favicon 获取器",url:o=>v(void 0,null,function*(){try{let e=yield(0,bt.requestUrl)({url:"https://favicongrabber.com/api/grab/"+o});return e.json.length===0?Promise.resolve(""):Promise.resolve(e.json.icons[0].src)}catch(e){return console.error(e),Promise.reject("favicongrabber: failed to retrieve icon for domain "+o)}})}}});var Fo=mo((op,cn)=>{cn.exports=[{"URI Scheme":"aaa",Template:"",Description:"Diameter Protocol",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC6733]",Notes:"",schema:"aaa"},{"URI Scheme":"aaas",Template:"",Description:"Diameter Protocol with Secure Transport",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC6733]",Notes:"",schema:"aaas"},{"URI Scheme":"about",Template:"",Description:"about",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC6694]",Notes:"",schema:"about"},{"URI Scheme":"acap",Template:"",Description:"application configuration access protocol",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC2244]",Notes:"",schema:"acap"},{"URI Scheme":"acct",Template:"",Description:"acct",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC7565]",Notes:"",schema:"acct"},{"URI Scheme":"acd",Template:"prov/acd",Description:"acd",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Michael_Hedenus]",Notes:"",schema:"acd"},{"URI Scheme":"acr",Template:"prov/acr",Description:"acr",Status:"Provisional","Well-Known URI Support":"-",Reference:"[OMA-OMNA]",Notes:"",schema:"acr"},{"URI Scheme":"adiumxtra",Template:"prov/adiumxtra",Description:"adiumxtra",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"adiumxtra"},{"URI Scheme":"adt",Template:"prov/adt",Description:"adt",Status:"Provisional","Well-Known URI Support":"-",Reference:"[SAP_SE]",Notes:"",schema:"adt"},{"URI Scheme":"afp",Template:"prov/afp",Description:"afp",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"afp"},{"URI Scheme":"afs",Template:"",Description:"Andrew File System global file names",Status:"Provisional","Well-Known URI Support":"-",Reference:"[RFC1738]",Notes:"",schema:"afs"},{"URI Scheme":"aim",Template:"prov/aim",Description:"aim",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"aim"},{"URI Scheme":"amss",Template:"prov/amss",Description:"amss",Status:"Provisional","Well-Known URI Support":"-",Reference:"[RadioDNS_Project]",Notes:"",schema:"amss"},{"URI Scheme":"android",Template:"prov/android",Description:"android",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Adam_Barth][https://developer.android.com/guide/topics/manifest/manifest-intro]",Notes:"",schema:"android"},{"URI Scheme":"appdata",Template:"prov/appdata",Description:"appdata",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"appdata"},{"URI Scheme":"apt",Template:"prov/apt",Description:"apt",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"apt"},{"URI Scheme":"ar",Template:"prov/ar",Description:"ar",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Arweave_Team]",Notes:"",schema:"ar"},{"URI Scheme":"ark",Template:"prov/ark",Description:"ark",Status:"Provisional","Well-Known URI Support":"-",Reference:"[ARK_agency][https://n2t.net/ark:/21206/10015]",Notes:"",schema:"ark"},{"URI Scheme":"at",Template:"prov/at",Description:"at (see [reviewer notes])",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Bluesky_PBLLC][Paul_Frazee]",Notes:"",schema:"at"},{"URI Scheme":"attachment",Template:"prov/attachment",Description:"attachment",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"attachment"},{"URI Scheme":"aw",Template:"prov/aw",Description:"aw",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"aw"},{"URI Scheme":"barion",Template:"prov/barion",Description:"barion",Status:"Provisional","Well-Known URI Support":"-",Reference:"[B\xEDr\xF3_Tam\xE1s]",Notes:"",schema:"barion"},{"URI Scheme":"bb",Template:"historic/bb",Description:"bb",Status:"Historical","Well-Known URI Support":"-",Reference:"[IESG]",Notes:"",schema:"bb"},{"URI Scheme":"beshare",Template:"prov/beshare",Description:"beshare",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"beshare"},{"URI Scheme":"bitcoin",Template:"prov/bitcoin",Description:"bitcoin",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"bitcoin"},{"URI Scheme":"bitcoincash",Template:"prov/bitcoincash",Description:"bitcoincash",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Corentin_Mercier]",Notes:"",schema:"bitcoincash"},{"URI Scheme":"blob",Template:"prov/blob",Description:"blob",Status:"Provisional","Well-Known URI Support":"-",Reference:"[W3C_WebApps_Working_Group][Chris_Rebert]",Notes:"",schema:"blob"},{"URI Scheme":"bolo",Template:"prov/bolo",Description:"bolo",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"bolo"},{"URI Scheme":"brid",Template:"prov/brid",Description:"brid",Status:"Provisional","Well-Known URI Support":"-",Reference:"[J\xFCrgen_Grupp][Michael_Ranft][Sophie_Schenkel]",Notes:"",schema:"brid"},{"URI Scheme":"browserext",Template:"prov/browserext",Description:"browserext",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Mike_Pietraszak]",Notes:"",schema:"browserext"},{"URI Scheme":"cabal",Template:"prov/cabal",Description:"cabal",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Fr\xE9d\xE9ric_Wang][Cabal_Club]",Notes:"",schema:"cabal"},{"URI Scheme":"calculator",Template:"prov/calculator",Description:"calculator",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"calculator"},{"URI Scheme":"callto",Template:"prov/callto",Description:"callto",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Alexey_Melnikov]",Notes:"",schema:"callto"},{"URI Scheme":"cap",Template:"",Description:"Calendar Access Protocol",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC4324]",Notes:"",schema:"cap"},{"URI Scheme":"cast",Template:"prov/cast",Description:"cast",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Adam_Barth][https://developers.google.com/cast/docs/registration]",Notes:"",schema:"cast"},{"URI Scheme":"casts",Template:"prov/casts",Description:"casts",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Adam_Barth][https://developers.google.com/cast/docs/registration]",Notes:"",schema:"casts"},{"URI Scheme":"chrome",Template:"prov/chrome",Description:"chrome",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"chrome"},{"URI Scheme":"chrome-extension",Template:"prov/chrome-extension",Description:"chrome-extension",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"chrome-extension"},{"URI Scheme":"cid",Template:"",Description:"content identifier",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC2392]",Notes:"",schema:"cid"},{"URI Scheme":"coap",Template:"",Description:"coap",Status:"Permanent","Well-Known URI Support":"[RFC7252]",Reference:"[RFC7252]",Notes:"",schema:"coap"},{"URI Scheme":"coap+tcp",Template:"",Description:"coap+tcp (see [reviewer notes])",Status:"Permanent","Well-Known URI Support":"[RFC8323]",Reference:"[RFC8323]",Notes:"",schema:"coap+tcp"},{"URI Scheme":"coap+ws",Template:"",Description:"coap+ws (see [reviewer notes])",Status:"Permanent","Well-Known URI Support":"[RFC8323]",Reference:"[RFC8323]",Notes:"",schema:"coap+ws"},{"URI Scheme":"coaps",Template:"",Description:"coaps",Status:"Permanent","Well-Known URI Support":"[RFC7252]",Reference:"[RFC7252]",Notes:"",schema:"coaps"},{"URI Scheme":"coaps+tcp",Template:"",Description:"coaps+tcp (see [reviewer notes])",Status:"Permanent","Well-Known URI Support":"[RFC8323]",Reference:"[RFC8323]",Notes:"",schema:"coaps+tcp"},{"URI Scheme":"coaps+ws",Template:"",Description:"coaps+ws (see [reviewer notes])",Status:"Permanent","Well-Known URI Support":"[RFC8323]",Reference:"[RFC8323]",Notes:"",schema:"coaps+ws"},{"URI Scheme":"com-eventbrite-attendee",Template:"prov/com-eventbrite-attendee",Description:"com-eventbrite-attendee",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Bob_Van_Zant]",Notes:"",schema:"com-eventbrite-attendee"},{"URI Scheme":"content",Template:"prov/content",Description:"content",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"content"},{"URI Scheme":"content-type",Template:"prov/content-type",Description:"content-type",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Donald_Eastlake]",Notes:"",schema:"content-type"},{"URI Scheme":"crid",Template:"",Description:"TV-Anytime Content Reference Identifier",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC4078]",Notes:"",schema:"crid"},{"URI Scheme":"cstr",Template:"prov/cstr",Description:"cstr",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Wang_Shu]",Notes:"",schema:"cstr"},{"URI Scheme":"cvs",Template:"prov/cvs",Description:"cvs",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"cvs"},{"URI Scheme":"dab",Template:"prov/dab",Description:"dab",Status:"Provisional","Well-Known URI Support":"-",Reference:"[RadioDNS_Project]",Notes:"",schema:"dab"},{"URI Scheme":"dat",Template:"prov/dat",Description:"dat",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Fr\xE9d\xE9ric_Wang][Paul_Frazee]",Notes:"",schema:"dat"},{"URI Scheme":"data",Template:"",Description:"data",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC2397]",Notes:"",schema:"data"},{"URI Scheme":"dav",Template:"",Description:"dav",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC4918]",Notes:"",schema:"dav"},{"URI Scheme":"dhttp",Template:"prov/dhttp",Description:"dhttp (see [reviewer notes])",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Qi_Zhou]",Notes:"",schema:"dhttp"},{"URI Scheme":"diaspora",Template:"prov/diaspora",Description:"diaspora",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dennis_Schubert]",Notes:"",schema:"diaspora"},{"URI Scheme":"dict",Template:"",Description:"dictionary service protocol",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC2229]",Notes:"",schema:"dict"},{"URI Scheme":"did",Template:"prov/did",Description:"did",Status:"Provisional","Well-Known URI Support":"-",Reference:"[W3C_Decentralized_Identifier_Working_Group][Manu_Sporny][Ivan_Herman]",Notes:"",schema:"did"},{"URI Scheme":"dis",Template:"prov/dis",Description:"dis",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Christophe_Meessen]",Notes:"",schema:"dis"},{"URI Scheme":"dlna-playcontainer",Template:"prov/dlna-playcontainer",Description:"dlna-playcontainer",Status:"Provisional","Well-Known URI Support":"-",Reference:"[DLNA]",Notes:"",schema:"dlna-playcontainer"},{"URI Scheme":"dlna-playsingle",Template:"prov/dlna-playsingle",Description:"dlna-playsingle",Status:"Provisional","Well-Known URI Support":"-",Reference:"[DLNA]",Notes:"",schema:"dlna-playsingle"},{"URI Scheme":"dns",Template:"",Description:"Domain Name System",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC4501]",Notes:"",schema:"dns"},{"URI Scheme":"dntp",Template:"prov/dntp",Description:"dntp",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Hans-Dieter_A._Hiep]",Notes:"",schema:"dntp"},{"URI Scheme":"doi",Template:"prov/doi",Description:"doi",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Fr\xE9d\xE9ric_Wang][International_DOI_Foundation]",Notes:"",schema:"doi"},{"URI Scheme":"dpp",Template:"prov/dpp",Description:"dpp",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Gaurav_Jain][Wi-Fi_Alliance]",Notes:"",schema:"dpp"},{"URI Scheme":"drm",Template:"prov/drm",Description:"drm",Status:"Provisional","Well-Known URI Support":"-",Reference:"[RadioDNS_Project]",Notes:"",schema:"drm"},{"URI Scheme":"drop",Template:"historic/drop",Description:"drop",Status:"Historical","Well-Known URI Support":"-",Reference:"[IESG]",Notes:"",schema:"drop"},{"URI Scheme":"dtmi",Template:"prov/dtmi",Description:"dtmi",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"dtmi"},{"URI Scheme":"dtn",Template:"",Description:"DTNRG research and development",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC9171]",Notes:"",schema:"dtn"},{"URI Scheme":"dvb",Template:"",Description:"dvb",Status:"Provisional","Well-Known URI Support":"-",Reference:"[draft-mcroberts-uri-dvb]",Notes:"",schema:"dvb"},{"URI Scheme":"dvx",Template:"prov/dvx",Description:"dvx",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Clemens_Bastian]",Notes:"",schema:"dvx"},{"URI Scheme":"dweb",Template:"prov/dweb",Description:"dweb",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Fr\xE9d\xE9ric_Wang][Protocol_Labs]",Notes:"",schema:"dweb"},{"URI Scheme":"ed2k",Template:"prov/ed2k",Description:"ed2k",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"ed2k"},{"URI Scheme":"eid",Template:"prov/eid",Description:"eid",Status:"Provisional","Well-Known URI Support":"-",Reference:"[eSIM_Group_GSM_Association]",Notes:"",schema:"eid"},{"URI Scheme":"elsi",Template:"prov/elsi",Description:"elsi",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Kimmo_Lindholm]",Notes:"",schema:"elsi"},{"URI Scheme":"embedded",Template:"prov/embedded",Description:"embedded",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Peter_Hoddie]",Notes:"",schema:"embedded"},{"URI Scheme":"ens",Template:"prov/ens",Description:"ens",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Ricky_Bloomfield][Bradley_Nelson]",Notes:"",schema:"ens"},{"URI Scheme":"ethereum",Template:"prov/ethereum",Description:"ethereum",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Fr\xE9d\xE9ric_Wang][ligi]",Notes:"",schema:"ethereum"},{"URI Scheme":"example",Template:"",Description:"example",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC7595]",Notes:"",schema:"example"},{"URI Scheme":"facetime",Template:"prov/facetime",Description:"facetime",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"facetime"},{"URI Scheme":"fax",Template:"",Description:"fax",Status:"Historical","Well-Known URI Support":"-",Reference:"[RFC2806][RFC3966]",Notes:"",schema:"fax"},{"URI Scheme":"feed",Template:"prov/feed",Description:"feed",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"feed"},{"URI Scheme":"feedready",Template:"prov/feedready",Description:"feedready",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Mirko_Nosenzo]",Notes:"",schema:"feedready"},{"URI Scheme":"fido",Template:"prov/fido",Description:"fido",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Adam_Langley]",Notes:"",schema:"fido"},{"URI Scheme":"file",Template:"",Description:"Host-specific file names",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC8089]",Notes:"",schema:"file"},{"URI Scheme":"filesystem",Template:"historic/filesystem",Description:"filesystem",Status:"Historical","Well-Known URI Support":"-",Reference:"[W3C_WebApps_Working_Group][Chris_Rebert]",Notes:"",schema:"filesystem"},{"URI Scheme":"finger",Template:"prov/finger",Description:"finger",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"finger"},{"URI Scheme":"first-run-pen-experience",Template:"prov/first-run-pen-experience",Description:"first-run-pen-experience",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"first-run-pen-experience"},{"URI Scheme":"fish",Template:"prov/fish",Description:"fish",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"fish"},{"URI Scheme":"fm",Template:"prov/fm",Description:"fm",Status:"Provisional","Well-Known URI Support":"-",Reference:"[RadioDNS_Project]",Notes:"",schema:"fm"},{"URI Scheme":"ftp",Template:"",Description:"File Transfer Protocol",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC1738]",Notes:"",schema:"ftp"},{"URI Scheme":"fuchsia-pkg",Template:"prov/fuchsia-pkg",Description:"fuchsia-pkg",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Adam_Barth][https://fuchsia.googlesource.com/fuchsia/]",Notes:"",schema:"fuchsia-pkg"},{"URI Scheme":"geo",Template:"",Description:"Geographic Locations",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC5870]",Notes:"",schema:"geo"},{"URI Scheme":"gg",Template:"prov/gg",Description:"gg",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"gg"},{"URI Scheme":"git",Template:"prov/git",Description:"git",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"git"},{"URI Scheme":"gitoid",Template:"prov/gitoid",Description:"gitoid",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Ed_Warnicke]",Notes:"",schema:"gitoid"},{"URI Scheme":"gizmoproject",Template:"prov/gizmoproject",Description:"gizmoproject",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"gizmoproject"},{"URI Scheme":"go",Template:"",Description:"go",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC3368]",Notes:"",schema:"go"},{"URI Scheme":"gopher",Template:"",Description:"The Gopher Protocol",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC4266]",Notes:"",schema:"gopher"},{"URI Scheme":"graph",Template:"prov/graph",Description:"graph",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Alastair_Green]",Notes:"",schema:"graph"},{"URI Scheme":"grd",Template:"historic/grd",Description:"grd",Status:"Historical","Well-Known URI Support":"-",Reference:"[IESG]",Notes:"",schema:"grd"},{"URI Scheme":"gtalk",Template:"prov/gtalk",Description:"gtalk",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"gtalk"},{"URI Scheme":"h323",Template:"",Description:"H.323",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC3508]",Notes:"",schema:"h323"},{"URI Scheme":"ham",Template:"",Description:"ham",Status:"Provisional","Well-Known URI Support":"-",Reference:"[RFC7046]",Notes:"",schema:"ham"},{"URI Scheme":"hcap",Template:"prov/hcap",Description:"hcap",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"hcap"},{"URI Scheme":"hcp",Template:"prov/hcp",Description:"hcp",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Alexey_Melnikov]",Notes:"",schema:"hcp"},{"URI Scheme":"http",Template:"",Description:"Hypertext Transfer Protocol",Status:"Permanent","Well-Known URI Support":"[RFC8615]",Reference:"[RFC9110, Section 4.2.1]",Notes:"",schema:"http"},{"URI Scheme":"https",Template:"",Description:"Hypertext Transfer Protocol Secure",Status:"Permanent","Well-Known URI Support":"[RFC8615]",Reference:"[RFC9110, Section 4.2.2]",Notes:"",schema:"https"},{"URI Scheme":"hxxp",Template:"prov/hxxp",Description:"hxxp",Status:"Provisional","Well-Known URI Support":"-",Reference:"[draft-salgado-hxxp]",Notes:"",schema:"hxxp"},{"URI Scheme":"hxxps",Template:"prov/hxxps",Description:"hxxps",Status:"Provisional","Well-Known URI Support":"-",Reference:"[draft-salgado-hxxp]",Notes:"",schema:"hxxps"},{"URI Scheme":"hydrazone",Template:"prov/hydrazone",Description:"hydrazone",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Matthias_Merkel][https://tech.hydrazone.pro/uri/specification/hydrazone.txt]",Notes:"",schema:"hydrazone"},{"URI Scheme":"hyper",Template:"prov/hyper",Description:"hyper",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Fr\xE9d\xE9ric_Wang][Paul_Frazee]",Notes:"",schema:"hyper"},{"URI Scheme":"iax",Template:"",Description:"Inter-Asterisk eXchange Version 2",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC5456]",Notes:"",schema:"iax"},{"URI Scheme":"icap",Template:"",Description:"Internet Content Adaptation Protocol",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC3507]",Notes:"",schema:"icap"},{"URI Scheme":"icon",Template:"",Description:"icon",Status:"Provisional","Well-Known URI Support":"-",Reference:"[draft-lafayette-icon-uri-scheme]",Notes:"",schema:"icon"},{"URI Scheme":"im",Template:"",Description:"Instant Messaging",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC3860]",Notes:"",schema:"im"},{"URI Scheme":"imap",Template:"",Description:"internet message access protocol",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC5092]",Notes:"",schema:"imap"},{"URI Scheme":"info",Template:"",Description:'Information Assets with Identifiers in Public Namespaces. [RFC4452] (section 3) defines an "info" registry of public namespaces, which is maintained by NISO and can be accessed from [http://info-uri.info/].',Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC4452]",Notes:"",schema:"info"},{"URI Scheme":"iotdisco",Template:"prov/iotdisco",Description:"iotdisco",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Peter_Waher][https://www.iana.org/assignments/uri-schemes/prov/iotdisco.pdf]",Notes:"",schema:"iotdisco"},{"URI Scheme":"ipfs",Template:"prov/ipfs",Description:"ipfs",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Fr\xE9d\xE9ric_Wang][Protocol_Labs]",Notes:"",schema:"ipfs"},{"URI Scheme":"ipn",Template:"",Description:"ipn",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC9171]",Notes:"",schema:"ipn"},{"URI Scheme":"ipns",Template:"prov/ipns",Description:"ipns",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Fr\xE9d\xE9ric_Wang][Protocol_Labs]",Notes:"",schema:"ipns"},{"URI Scheme":"ipp",Template:"",Description:"Internet Printing Protocol",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC3510]",Notes:"",schema:"ipp"},{"URI Scheme":"ipps",Template:"",Description:"Internet Printing Protocol over HTTPS",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC7472]",Notes:"",schema:"ipps"},{"URI Scheme":"irc",Template:"prov/irc",Description:"irc",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"irc"},{"URI Scheme":"irc6",Template:"prov/irc6",Description:"irc6",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"irc6"},{"URI Scheme":"ircs",Template:"prov/ircs",Description:"ircs",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"ircs"},{"URI Scheme":"iris",Template:"",Description:"Internet Registry Information Service",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC3981]",Notes:"",schema:"iris"},{"URI Scheme":"iris.beep",Template:"",Description:"iris.beep",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC3983]",Notes:"",schema:"iris.beep"},{"URI Scheme":"iris.lwz",Template:"",Description:"iris.lwz",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC4993]",Notes:"",schema:"iris.lwz"},{"URI Scheme":"iris.xpc",Template:"",Description:"iris.xpc",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC4992]",Notes:"",schema:"iris.xpc"},{"URI Scheme":"iris.xpcs",Template:"",Description:"iris.xpcs",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC4992]",Notes:"",schema:"iris.xpcs"},{"URI Scheme":"isostore",Template:"prov/isostore",Description:"isostore",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"isostore"},{"URI Scheme":"itms",Template:"prov/itms",Description:"itms",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"itms"},{"URI Scheme":"jabber",Template:"perm/jabber",Description:"jabber",Status:"Permanent","Well-Known URI Support":"-",Reference:"[Peter_Saint-Andre]",Notes:"",schema:"jabber"},{"URI Scheme":"jar",Template:"prov/jar",Description:"jar",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"jar"},{"URI Scheme":"jms",Template:"",Description:"Java Message Service",Status:"Provisional","Well-Known URI Support":"-",Reference:"[RFC6167]",Notes:"",schema:"jms"},{"URI Scheme":"keyparc",Template:"prov/keyparc",Description:"keyparc",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"keyparc"},{"URI Scheme":"lastfm",Template:"prov/lastfm",Description:"lastfm",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"lastfm"},{"URI Scheme":"lbry",Template:"prov/lbry",Description:"lbry",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Alex_Grintsvayg]",Notes:"",schema:"lbry"},{"URI Scheme":"ldap",Template:"",Description:"Lightweight Directory Access Protocol",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC4516]",Notes:"",schema:"ldap"},{"URI Scheme":"ldaps",Template:"prov/ldaps",Description:"ldaps",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"ldaps"},{"URI Scheme":"leaptofrogans",Template:"",Description:"leaptofrogans",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC8589]",Notes:"",schema:"leaptofrogans"},{"URI Scheme":"lid",Template:"prov/lid",Description:"lid",Status:"Provisional","Well-Known URI Support":"-",Reference:"[IS4]",Notes:"",schema:"lid"},{"URI Scheme":"lorawan",Template:"prov/lorawan",Description:"lorawan",Status:"Provisional","Well-Known URI Support":"-",Reference:"[OMA-DMSE]",Notes:"",schema:"lorawan"},{"URI Scheme":"lpa",Template:"prov/lpa",Description:"lpa",Status:"Provisional","Well-Known URI Support":"-",Reference:"[eSIM_Group_GSM_Association]",Notes:"",schema:"lpa"},{"URI Scheme":"lvlt",Template:"prov/lvlt",Description:"lvlt",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Alexander_Shishenko]",Notes:"",schema:"lvlt"},{"URI Scheme":"magnet",Template:"prov/magnet",Description:"magnet",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"magnet"},{"URI Scheme":"mailserver",Template:"",Description:"Access to data available from mail servers",Status:"Historical","Well-Known URI Support":"-",Reference:"[RFC6196]",Notes:"",schema:"mailserver"},{"URI Scheme":"mailto",Template:"",Description:"Electronic mail address",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC6068]",Notes:"",schema:"mailto"},{"URI Scheme":"maps",Template:"prov/maps",Description:"maps",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"maps"},{"URI Scheme":"market",Template:"prov/market",Description:"market",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"market"},{"URI Scheme":"matrix",Template:"prov/matrix",Description:"matrix",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Hubert_Chathi]",Notes:"",schema:"matrix"},{"URI Scheme":"message",Template:"prov/message",Description:"message",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"message"},{"URI Scheme":"microsoft.windows.camera",Template:"prov/microsoft.windows.camera",Description:"microsoft.windows.camera",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"microsoft.windows.camera"},{"URI Scheme":"microsoft.windows.camera.multipicker",Template:"prov/microsoft.windows.camera.multipicker",Description:"microsoft.windows.camera.multipicker",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"microsoft.windows.camera.multipicker"},{"URI Scheme":"microsoft.windows.camera.picker",Template:"prov/microsoft.windows.camera.picker",Description:"microsoft.windows.camera.picker",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"microsoft.windows.camera.picker"},{"URI Scheme":"mid",Template:"",Description:"message identifier",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC2392]",Notes:"",schema:"mid"},{"URI Scheme":"mms",Template:"prov/mms",Description:"mms",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Alexey_Melnikov]",Notes:"",schema:"mms"},{"URI Scheme":"modem",Template:"",Description:"modem",Status:"Historical","Well-Known URI Support":"-",Reference:"[RFC2806][RFC3966]",Notes:"",schema:"modem"},{"URI Scheme":"mongodb",Template:"prov/mongodb",Description:"mongodb",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Ignacio_Losiggio][Mongo_DB_Inc]",Notes:"",schema:"mongodb"},{"URI Scheme":"moz",Template:"prov/moz",Description:"moz",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Joe_Hildebrand]",Notes:"",schema:"moz"},{"URI Scheme":"ms-access",Template:"prov/ms-access",Description:"ms-access",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-access"},{"URI Scheme":"ms-appinstaller",Template:"prov/ms-appinstaller",Description:"ms-appinstaller",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-appinstaller"},{"URI Scheme":"ms-browser-extension",Template:"prov/ms-browser-extension",Description:"ms-browser-extension",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-browser-extension"},{"URI Scheme":"ms-calculator",Template:"prov/ms-calculator",Description:"ms-calculator",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-calculator"},{"URI Scheme":"ms-drive-to",Template:"prov/ms-drive-to",Description:"ms-drive-to",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-drive-to"},{"URI Scheme":"ms-enrollment",Template:"prov/ms-enrollment",Description:"ms-enrollment",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-enrollment"},{"URI Scheme":"ms-excel",Template:"prov/ms-excel",Description:"ms-excel",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-excel"},{"URI Scheme":"ms-eyecontrolspeech",Template:"prov/ms-eyecontrolspeech",Description:"ms-eyecontrolspeech",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-eyecontrolspeech"},{"URI Scheme":"ms-gamebarservices",Template:"prov/ms-gamebarservices",Description:"ms-gamebarservices",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-gamebarservices"},{"URI Scheme":"ms-gamingoverlay",Template:"prov/ms-gamingoverlay",Description:"ms-gamingoverlay",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-gamingoverlay"},{"URI Scheme":"ms-getoffice",Template:"prov/ms-getoffice",Description:"ms-getoffice",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-getoffice"},{"URI Scheme":"ms-help",Template:"prov/ms-help",Description:"ms-help",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Alexey_Melnikov]",Notes:"",schema:"ms-help"},{"URI Scheme":"ms-infopath",Template:"prov/ms-infopath",Description:"ms-infopath",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-infopath"},{"URI Scheme":"ms-inputapp",Template:"prov/ms-inputapp",Description:"ms-inputapp",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-inputapp"},{"URI Scheme":"ms-launchremotedesktop",Template:"prov/ms-launchremotedesktop",Description:"ms-launchremotedesktop",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-launchremotedesktop"},{"URI Scheme":"ms-lockscreencomponent-config",Template:"prov/ms-lockscreencomponent-config",Description:"ms-lockscreencomponent-config",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-lockscreencomponent-config"},{"URI Scheme":"ms-media-stream-id",Template:"prov/ms-media-stream-id",Description:"ms-media-stream-id",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-media-stream-id"},{"URI Scheme":"ms-meetnow",Template:"prov/ms-meetnow",Description:"ms-meetnow",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-meetnow"},{"URI Scheme":"ms-mixedrealitycapture",Template:"prov/ms-mixedrealitycapture",Description:"ms-mixedrealitycapture",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-mixedrealitycapture"},{"URI Scheme":"ms-mobileplans",Template:"prov/ms-mobileplans",Description:"ms-mobileplans",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-mobileplans"},{"URI Scheme":"ms-newsandinterests",Template:"prov/ms-newsandinterests",Description:"ms-newsandinterests",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-newsandinterests"},{"URI Scheme":"ms-officeapp",Template:"prov/ms-officeapp",Description:"ms-officeapp",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-officeapp"},{"URI Scheme":"ms-people",Template:"prov/ms-people",Description:"ms-people",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-people"},{"URI Scheme":"ms-project",Template:"prov/ms-project",Description:"ms-project",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-project"},{"URI Scheme":"ms-powerpoint",Template:"prov/ms-powerpoint",Description:"ms-powerpoint",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-powerpoint"},{"URI Scheme":"ms-publisher",Template:"prov/ms-publisher",Description:"ms-publisher",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-publisher"},{"URI Scheme":"ms-remotedesktop",Template:"prov/ms-remotedesktop",Description:"ms-remotedesktop",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-remotedesktop"},{"URI Scheme":"ms-remotedesktop-launch",Template:"prov/ms-remotedesktop-launch",Description:"ms-remotedesktop-launch",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-remotedesktop-launch"},{"URI Scheme":"ms-restoretabcompanion",Template:"prov/ms-restoretabcompanion",Description:"ms-restoretabcompanion",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-restoretabcompanion"},{"URI Scheme":"ms-screenclip",Template:"prov/ms-screenclip",Description:"ms-screenclip",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-screenclip"},{"URI Scheme":"ms-screensketch",Template:"prov/ms-screensketch",Description:"ms-screensketch",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-screensketch"},{"URI Scheme":"ms-search",Template:"prov/ms-search",Description:"ms-search",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-search"},{"URI Scheme":"ms-search-repair",Template:"prov/ms-search-repair",Description:"ms-search-repair",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-search-repair"},{"URI Scheme":"ms-secondary-screen-controller",Template:"prov/ms-secondary-screen-controller",Description:"ms-secondary-screen-controller",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-secondary-screen-controller"},{"URI Scheme":"ms-secondary-screen-setup",Template:"prov/ms-secondary-screen-setup",Description:"ms-secondary-screen-setup",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-secondary-screen-setup"},{"URI Scheme":"ms-settings",Template:"prov/ms-settings",Description:"ms-settings",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-settings"},{"URI Scheme":"ms-settings-airplanemode",Template:"prov/ms-settings-airplanemode",Description:"ms-settings-airplanemode",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-settings-airplanemode"},{"URI Scheme":"ms-settings-bluetooth",Template:"prov/ms-settings-bluetooth",Description:"ms-settings-bluetooth",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-settings-bluetooth"},{"URI Scheme":"ms-settings-camera",Template:"prov/ms-settings-camera",Description:"ms-settings-camera",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-settings-camera"},{"URI Scheme":"ms-settings-cellular",Template:"prov/ms-settings-cellular",Description:"ms-settings-cellular",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-settings-cellular"},{"URI Scheme":"ms-settings-cloudstorage",Template:"prov/ms-settings-cloudstorage",Description:"ms-settings-cloudstorage",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-settings-cloudstorage"},{"URI Scheme":"ms-settings-connectabledevices",Template:"prov/ms-settings-connectabledevices",Description:"ms-settings-connectabledevices",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-settings-connectabledevices"},{"URI Scheme":"ms-settings-displays-topology",Template:"prov/ms-settings-displays-topology",Description:"ms-settings-displays-topology",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-settings-displays-topology"},{"URI Scheme":"ms-settings-emailandaccounts",Template:"prov/ms-settings-emailandaccounts",Description:"ms-settings-emailandaccounts",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-settings-emailandaccounts"},{"URI Scheme":"ms-settings-language",Template:"prov/ms-settings-language",Description:"ms-settings-language",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-settings-language"},{"URI Scheme":"ms-settings-location",Template:"prov/ms-settings-location",Description:"ms-settings-location",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-settings-location"},{"URI Scheme":"ms-settings-lock",Template:"prov/ms-settings-lock",Description:"ms-settings-lock",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-settings-lock"},{"URI Scheme":"ms-settings-nfctransactions",Template:"prov/ms-settings-nfctransactions",Description:"ms-settings-nfctransactions",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-settings-nfctransactions"},{"URI Scheme":"ms-settings-notifications",Template:"prov/ms-settings-notifications",Description:"ms-settings-notifications",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-settings-notifications"},{"URI Scheme":"ms-settings-power",Template:"prov/ms-settings-power",Description:"ms-settings-power",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-settings-power"},{"URI Scheme":"ms-settings-privacy",Template:"prov/ms-settings-privacy",Description:"ms-settings-privacy",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-settings-privacy"},{"URI Scheme":"ms-settings-proximity",Template:"prov/ms-settings-proximity",Description:"ms-settings-proximity",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-settings-proximity"},{"URI Scheme":"ms-settings-screenrotation",Template:"prov/ms-settings-screenrotation",Description:"ms-settings-screenrotation",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-settings-screenrotation"},{"URI Scheme":"ms-settings-wifi",Template:"prov/ms-settings-wifi",Description:"ms-settings-wifi",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-settings-wifi"},{"URI Scheme":"ms-settings-workplace",Template:"prov/ms-settings-workplace",Description:"ms-settings-workplace",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-settings-workplace"},{"URI Scheme":"ms-spd",Template:"prov/ms-spd",Description:"ms-spd",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-spd"},{"URI Scheme":"ms-stickers",Template:"prov/ms-stickers",Description:"ms-stickers",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-stickers"},{"URI Scheme":"ms-sttoverlay",Template:"prov/ms-sttoverlay",Description:"ms-sttoverlay",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-sttoverlay"},{"URI Scheme":"ms-transit-to",Template:"prov/ms-transit-to",Description:"ms-transit-to",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-transit-to"},{"URI Scheme":"ms-useractivityset",Template:"prov/ms-useractivityset",Description:"ms-useractivityset",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-useractivityset"},{"URI Scheme":"ms-virtualtouchpad",Template:"prov/ms-virtualtouchpad",Description:"ms-virtualtouchpad",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-virtualtouchpad"},{"URI Scheme":"ms-visio",Template:"prov/ms-visio",Description:"ms-visio",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-visio"},{"URI Scheme":"ms-walk-to",Template:"prov/ms-walk-to",Description:"ms-walk-to",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-walk-to"},{"URI Scheme":"ms-whiteboard",Template:"prov/ms-whiteboard",Description:"ms-whiteboard",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-whiteboard"},{"URI Scheme":"ms-whiteboard-cmd",Template:"prov/ms-whiteboard-cmd",Description:"ms-whiteboard-cmd",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-whiteboard-cmd"},{"URI Scheme":"ms-word",Template:"prov/ms-word",Description:"ms-word",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"ms-word"},{"URI Scheme":"msnim",Template:"prov/msnim",Description:"msnim",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Alexey_Melnikov]",Notes:"",schema:"msnim"},{"URI Scheme":"msrp",Template:"",Description:"Message Session Relay Protocol",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC4975]",Notes:"",schema:"msrp"},{"URI Scheme":"msrps",Template:"",Description:"Message Session Relay Protocol Secure",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC4975][RFC8873]",Notes:"",schema:"msrps"},{"URI Scheme":"mss",Template:"prov/mss",Description:"mss",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Jarmo_Miettinen]",Notes:"",schema:"mss"},{"URI Scheme":"mt",Template:"perm/mt",Description:"Matter protocol on-boarding payloads that are encoded for use in QR Codes and/or NFC Tags",Status:"Permanent","Well-Known URI Support":"-",Reference:"[Connectivity_Standards_Alliance]",Notes:"",schema:"mt"},{"URI Scheme":"mtqp",Template:"",Description:"Message Tracking Query Protocol",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC3887]",Notes:"",schema:"mtqp"},{"URI Scheme":"mumble",Template:"prov/mumble",Description:"mumble",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"mumble"},{"URI Scheme":"mupdate",Template:"",Description:"Mailbox Update (MUPDATE) Protocol",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC3656]",Notes:"",schema:"mupdate"},{"URI Scheme":"mvn",Template:"prov/mvn",Description:"mvn",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"mvn"},{"URI Scheme":"news",Template:"",Description:"USENET news",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC5538]",Notes:"",schema:"news"},{"URI Scheme":"nfs",Template:"",Description:"network file system protocol",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC2224]",Notes:"",schema:"nfs"},{"URI Scheme":"ni",Template:"",Description:"ni",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC6920]",Notes:"",schema:"ni"},{"URI Scheme":"nih",Template:"",Description:"nih",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC6920]",Notes:"",schema:"nih"},{"URI Scheme":"nntp",Template:"",Description:"USENET news using NNTP access",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC5538]",Notes:"",schema:"nntp"},{"URI Scheme":"notes",Template:"prov/notes",Description:"notes",Status:"Provisional","Well-Known URI Support":"-",Reference:"[draft-dconmy-notes-uri-scheme-02]",Notes:"",schema:"notes"},{"URI Scheme":"num",Template:"prov/num",Description:"Namespace Utility Modules",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Elliott_Brown][https://www.numprotocol.com/specification]",Notes:"",schema:"num"},{"URI Scheme":"ocf",Template:"prov/ocf",Description:"ocf",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"ocf"},{"URI Scheme":"oid",Template:"prov/oid",Description:"oid",Status:"Provisional","Well-Known URI Support":"-",Reference:"[draft-larmouth-oid-iri]",Notes:"",schema:"oid"},{"URI Scheme":"onenote",Template:"prov/onenote",Description:"onenote",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"onenote"},{"URI Scheme":"onenote-cmd",Template:"prov/onenote-cmd",Description:"onenote-cmd",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"onenote-cmd"},{"URI Scheme":"opaquelocktoken",Template:"",Description:"opaquelocktokent",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC4918]",Notes:"",schema:"opaquelocktoken"},{"URI Scheme":"openid",Template:"prov/openid",Description:"OpenID Connect",Status:"Provisional","Well-Known URI Support":"-",Reference:"[OpenID_Foundation_Artifact_Binding_Working_Group][OpenID Connect Core 1.0, Section 7.3]",Notes:"",schema:"openid"},{"URI Scheme":"openpgp4fpr",Template:"prov/openpgp4fpr",Description:"openpgp4fpr",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Wiktor_Kwapisiewicz]",Notes:"",schema:"openpgp4fpr"},{"URI Scheme":"otpauth",Template:"prov/otpauth",Description:"otpauth",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Fr\xE9d\xE9ric_Wang][Thomas_Habets]",Notes:"",schema:"otpauth"},{"URI Scheme":"p1",Template:"historic/p1",Description:"p1",Status:"Historical","Well-Known URI Support":"-",Reference:"[IESG]",Notes:"",schema:"p1"},{"URI Scheme":"pack",Template:"historic/pack",Description:"pack",Status:"Historical","Well-Known URI Support":"-",Reference:"[draft-shur-pack-uri-scheme]",Notes:"",schema:"pack"},{"URI Scheme":"palm",Template:"prov/palm",Description:"palm",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"palm"},{"URI Scheme":"paparazzi",Template:"prov/paparazzi",Description:"paparazzi",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"paparazzi"},{"URI Scheme":"payment",Template:"prov/payment",Description:"payment",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Arild_Hegvik][https://bitcoincheque.org]",Notes:"",schema:"payment"},{"URI Scheme":"payto",Template:"prov/payto",Description:"payto",Status:"Provisional","Well-Known URI Support":"-",Reference:"[RFC8905]",Notes:"",schema:"payto"},{"URI Scheme":"pkcs11",Template:"",Description:"PKCS#11",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC7512]",Notes:"",schema:"pkcs11"},{"URI Scheme":"platform",Template:"prov/platform",Description:"platform",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"platform"},{"URI Scheme":"pop",Template:"",Description:"Post Office Protocol v3",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC2384]",Notes:"",schema:"pop"},{"URI Scheme":"pres",Template:"",Description:"Presence",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC3859]",Notes:"",schema:"pres"},{"URI Scheme":"prospero",Template:"",Description:"Prospero Directory Service",Status:"Historical","Well-Known URI Support":"-",Reference:"[RFC4157]",Notes:"",schema:"prospero"},{"URI Scheme":"proxy",Template:"prov/proxy",Description:"proxy",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"proxy"},{"URI Scheme":"pwid",Template:"prov/pwid",Description:"pwid",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Eld_Zierau]",Notes:"",schema:"pwid"},{"URI Scheme":"psyc",Template:"prov/psyc",Description:"psyc",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"psyc"},{"URI Scheme":"pttp",Template:"prov/pttp",Description:"pttp",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Tony_Deng][Tuan_Hoang][Bob_Hinkle][Mark_Chen]",Notes:"",schema:"pttp"},{"URI Scheme":"qb",Template:"prov/qb",Description:"qb",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Jan_Pokorny]",Notes:"",schema:"qb"},{"URI Scheme":"query",Template:"prov/query",Description:"query",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"query"},{"URI Scheme":"quic-transport",Template:"prov/quic-transport",Description:"quic-transport",Status:"Provisional","Well-Known URI Support":"-",Reference:"[draft-vvv-webtransport-quic]",Notes:"",schema:"quic-transport"},{"URI Scheme":"redis",Template:"prov/redis",Description:"redis",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Chris_Rebert]",Notes:"",schema:"redis"},{"URI Scheme":"rediss",Template:"prov/rediss",Description:"rediss",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Chris_Rebert]",Notes:"",schema:"rediss"},{"URI Scheme":"reload",Template:"",Description:"reload",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC6940]",Notes:"",schema:"reload"},{"URI Scheme":"res",Template:"prov/res",Description:"res",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Alexey_Melnikov]",Notes:"",schema:"res"},{"URI Scheme":"resource",Template:"prov/resource",Description:"resource",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"resource"},{"URI Scheme":"rmi",Template:"prov/rmi",Description:"rmi",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"rmi"},{"URI Scheme":"rsync",Template:"",Description:"rsync",Status:"Provisional","Well-Known URI Support":"-",Reference:"[RFC5781]",Notes:"",schema:"rsync"},{"URI Scheme":"rtmfp",Template:"prov/rtmfp",Description:"rtmfp",Status:"Provisional","Well-Known URI Support":"-",Reference:"[RFC7425]",Notes:"",schema:"rtmfp"},{"URI Scheme":"rtmp",Template:"prov/rtmp",Description:"rtmp",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"rtmp"},{"URI Scheme":"rtsp",Template:"",Description:"Real-Time Streaming Protocol (RTSP)",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC2326][RFC7826]",Notes:"",schema:"rtsp"},{"URI Scheme":"rtsps",Template:"",Description:"Real-Time Streaming Protocol (RTSP) over TLS",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC2326][RFC7826]",Notes:"",schema:"rtsps"},{"URI Scheme":"rtspu",Template:"",Description:"Real-Time Streaming Protocol (RTSP) over unreliable datagram transport",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC2326]",Notes:"",schema:"rtspu"},{"URI Scheme":"sarif",Template:"prov/sarif",Description:"sarif",Status:"Provisional","Well-Known URI Support":"-",Reference:"[OASIS_Open][Michael_C_Fanning][David_Keaton]",Notes:"",schema:"sarif"},{"URI Scheme":"secondlife",Template:"prov/secondlife",Description:"query",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"secondlife"},{"URI Scheme":"secret-token",Template:"prov/secret-token",Description:"secret-token",Status:"Provisional","Well-Known URI Support":"-",Reference:"[RFC8959]",Notes:"",schema:"secret-token"},{"URI Scheme":"service",Template:"",Description:"service location",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC2609]",Notes:"",schema:"service"},{"URI Scheme":"session",Template:"",Description:"session",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC6787]",Notes:"",schema:"session"},{"URI Scheme":"sftp",Template:"prov/sftp",Description:"query",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"sftp"},{"URI Scheme":"sgn",Template:"prov/sgn",Description:"sgn",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"sgn"},{"URI Scheme":"shc",Template:"prov/shc",Description:"shc",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Josh_Mandel]",Notes:"",schema:"shc"},{"URI Scheme":"shttp (OBSOLETE)",Template:"",Description:"Secure Hypertext Transfer Protocol",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC2660][status-change-http-experiments-to-historic]",Notes:"",schema:"shttp (OBSOLETE)"},{"URI Scheme":"sieve",Template:"",Description:"ManageSieve Protocol",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC5804]",Notes:"",schema:"sieve"},{"URI Scheme":"simpleledger",Template:"prov/simpleledger",Description:"simpleledger",Status:"Provisional","Well-Known URI Support":"-",Reference:"[James_Cramer]",Notes:"",schema:"simpleledger"},{"URI Scheme":"simplex",Template:"prov/simplex",Description:"simplex",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Evgeny_Poberezkin]",Notes:"",schema:"simplex"},{"URI Scheme":"sip",Template:"",Description:"session initiation protocol",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC3261]",Notes:"",schema:"sip"},{"URI Scheme":"sips",Template:"",Description:"secure session initiation protocol",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC3261]",Notes:"",schema:"sips"},{"URI Scheme":"skype",Template:"prov/skype",Description:"skype",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Alexey_Melnikov]",Notes:"",schema:"skype"},{"URI Scheme":"smb",Template:"prov/smb",Description:"smb",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"smb"},{"URI Scheme":"smp",Template:"prov/smp",Description:"smp",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Evgeny_Poberezkin]",Notes:"",schema:"smp"},{"URI Scheme":"sms",Template:"",Description:"Short Message Service",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC5724]",Notes:"",schema:"sms"},{"URI Scheme":"smtp",Template:"prov/smtp",Description:"smtp",Status:"Provisional","Well-Known URI Support":"-",Reference:"[draft-melnikov-smime-msa-to-mda]",Notes:"",schema:"smtp"},{"URI Scheme":"snews",Template:"",Description:"NNTP over SSL/TLS",Status:"Historical","Well-Known URI Support":"-",Reference:"[RFC5538]",Notes:"",schema:"snews"},{"URI Scheme":"snmp",Template:"",Description:"Simple Network Management Protocol",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC4088]",Notes:"",schema:"snmp"},{"URI Scheme":"soap.beep",Template:"",Description:"soap.beep",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC4227]",Notes:"",schema:"soap.beep"},{"URI Scheme":"soap.beeps",Template:"",Description:"soap.beeps",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC4227]",Notes:"",schema:"soap.beeps"},{"URI Scheme":"soldat",Template:"prov/soldat",Description:"soldat",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"soldat"},{"URI Scheme":"spiffe",Template:"prov/spiffe",Description:"spiffe",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Evan_Gilman]",Notes:"",schema:"spiffe"},{"URI Scheme":"spotify",Template:"prov/spotify",Description:"spotify",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"spotify"},{"URI Scheme":"ssb",Template:"prov/ssb",Description:"ssb",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Fr\xE9d\xE9ric_Wang][Secure_Scuttlebutt_Consortium]",Notes:"",schema:"ssb"},{"URI Scheme":"ssh",Template:"prov/ssh",Description:"ssh",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"ssh"},{"URI Scheme":"starknet",Template:"prov/starknet",Description:"starknet",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Abraham_Makovetsky]",Notes:"",schema:"starknet"},{"URI Scheme":"steam",Template:"prov/steam",Description:"steam",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"steam"},{"URI Scheme":"stun",Template:"",Description:"stun",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC7064]",Notes:"",schema:"stun"},{"URI Scheme":"stuns",Template:"",Description:"stuns",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC7064]",Notes:"",schema:"stuns"},{"URI Scheme":"submit",Template:"prov/submit",Description:"submit",Status:"Provisional","Well-Known URI Support":"-",Reference:"[draft-melnikov-smime-msa-to-mda]",Notes:"",schema:"submit"},{"URI Scheme":"svn",Template:"prov/svn",Description:"svn",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"svn"},{"URI Scheme":"swh",Template:"prov/swh",Description:"swh",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Software_Heritage][Stefano_Zacchiroli]",Notes:"",schema:"swh"},{"URI Scheme":"swid",Template:"prov/swid",Description:"swid (see [reviewer notes])",Status:"Provisional","Well-Known URI Support":"-",Reference:"[RFC9393, Section 5.1]",Notes:"",schema:"swid"},{"URI Scheme":"swidpath",Template:"prov/swidpath",Description:"swidpath (see [reviewer notes])",Status:"Provisional","Well-Known URI Support":"-",Reference:"[RFC9393, Section 5.2]",Notes:"",schema:"swidpath"},{"URI Scheme":"tag",Template:"",Description:"tag",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC4151]",Notes:"",schema:"tag"},{"URI Scheme":"taler",Template:"prov/taler",Description:"taler",Status:"Provisional","Well-Known URI Support":"-",Reference:"[draft-grothoff-taler-01]",Notes:"",schema:"taler"},{"URI Scheme":"teamspeak",Template:"prov/teamspeak",Description:"teamspeak",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"teamspeak"},{"URI Scheme":"tel",Template:"",Description:"telephone",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC3966][RFC5341]",Notes:"",schema:"tel"},{"URI Scheme":"teliaeid",Template:"prov/teliaeid",Description:"teliaeid",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Peter_Lewandowski]",Notes:"",schema:"teliaeid"},{"URI Scheme":"telnet",Template:"",Description:"Reference to interactive sessions",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC4248]",Notes:"",schema:"telnet"},{"URI Scheme":"tftp",Template:"",Description:"Trivial File Transfer Protocol",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC3617]",Notes:"",schema:"tftp"},{"URI Scheme":"things",Template:"prov/things",Description:"things",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"things"},{"URI Scheme":"thismessage",Template:"perm/thismessage",Description:"multipart/related relative reference resolution",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC2557]",Notes:"",schema:"thismessage"},{"URI Scheme":"tip",Template:"",Description:"Transaction Internet Protocol",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC2371]",Notes:"",schema:"tip"},{"URI Scheme":"tn3270",Template:"",Description:"Interactive 3270 emulation sessions",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC6270]",Notes:"",schema:"tn3270"},{"URI Scheme":"tool",Template:"prov/tool",Description:"tool",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Matthias_Merkel]",Notes:"",schema:"tool"},{"URI Scheme":"turn",Template:"",Description:"turn",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC7065]",Notes:"",schema:"turn"},{"URI Scheme":"turns",Template:"",Description:"turns",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC7065]",Notes:"",schema:"turns"},{"URI Scheme":"tv",Template:"",Description:"TV Broadcasts",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC2838]",Notes:"",schema:"tv"},{"URI Scheme":"udp",Template:"prov/udp",Description:"udp",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"udp"},{"URI Scheme":"unreal",Template:"prov/unreal",Description:"unreal",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"unreal"},{"URI Scheme":"upt",Template:"historic/upt",Description:"upt",Status:"Historical","Well-Known URI Support":"-",Reference:"[IESG]",Notes:"",schema:"upt"},{"URI Scheme":"urn",Template:"",Description:"Uniform Resource Names",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC8141][IANA registry urn-namespaces]",Notes:"",schema:"urn"},{"URI Scheme":"ut2004",Template:"prov/ut2004",Description:"ut2004",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"ut2004"},{"URI Scheme":"uuid-in-package",Template:"prov/uuid-in-package",Description:"uuid-in-package",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Kunihiko_Sakamoto]",Notes:"",schema:"uuid-in-package"},{"URI Scheme":"v-event",Template:"prov/v-event",Description:"v-event",Status:"Provisional","Well-Known URI Support":"-",Reference:"[draft-menderico-v-event-uri]",Notes:"",schema:"v-event"},{"URI Scheme":"vemmi",Template:"",Description:"versatile multimedia interface",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC2122]",Notes:"",schema:"vemmi"},{"URI Scheme":"ventrilo",Template:"prov/ventrilo",Description:"ventrilo",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"ventrilo"},{"URI Scheme":"ves",Template:"prov/ves",Description:"ves",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Jim_Zubov]",Notes:"",schema:"ves"},{"URI Scheme":"videotex",Template:"historic/videotex",Description:"videotex",Status:"Historical","Well-Known URI Support":"-",Reference:"[draft-mavrakis-videotex-url-spec][RFC2122][RFC3986]",Notes:"",schema:"videotex"},{"URI Scheme":"vnc",Template:"",Description:"Remote Framebuffer Protocol",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC7869]",Notes:"",schema:"vnc"},{"URI Scheme":"view-source",Template:"prov/view-source",Description:"view-source",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Mykyta_Yevstifeyev]",Notes:"",schema:"view-source"},{"URI Scheme":"vscode",Template:"prov/vscode",Description:"vscode",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"vscode"},{"URI Scheme":"vscode-insiders",Template:"prov/vscode-insiders",Description:"vscode-insiders",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"vscode-insiders"},{"URI Scheme":"vsls",Template:"prov/vsls",Description:"vsls",Status:"Provisional","Well-Known URI Support":"-",Reference:"[urischemeowners_at_microsoft.com]",Notes:"",schema:"vsls"},{"URI Scheme":"w3",Template:"prov/w3",Description:"w3 (see [reviewer notes])",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Qi_Zhou]",Notes:"",schema:"w3"},{"URI Scheme":"wais",Template:"",Description:"Wide Area Information Servers",Status:"Historical","Well-Known URI Support":"-",Reference:"[RFC4156]",Notes:"",schema:"wais"},{"URI Scheme":"web3",Template:"prov/web3",Description:"web3",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Qi_Zhou]",Notes:"",schema:"web3"},{"URI Scheme":"wcr",Template:"prov/wcr",Description:"wcr",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Jason_Dzubak]",Notes:"",schema:"wcr"},{"URI Scheme":"webcal",Template:"prov/webcal",Description:"webcal",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"webcal"},{"URI Scheme":"web+ap",Template:"prov/web+ap",Description:"web+ap",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Soni_L.]",Notes:"",schema:"web+ap"},{"URI Scheme":"wifi",Template:"prov/wifi",Description:"wifi",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Wi-Fi_Alliance][Jun_Tian]",Notes:"",schema:"wifi"},{"URI Scheme":"wpid",Template:"prov/wpid",Description:"wpid",Status:"Historical","Well-Known URI Support":"-",Reference:"[Eld_Zierau]",Notes:"",schema:"wpid"},{"URI Scheme":"ws",Template:"",Description:"WebSocket connections",Status:"Permanent","Well-Known URI Support":"[RFC8307]",Reference:"[RFC6455]",Notes:"",schema:"ws"},{"URI Scheme":"wss",Template:"",Description:"Encrypted WebSocket connections",Status:"Permanent","Well-Known URI Support":"[RFC8307]",Reference:"[RFC6455]",Notes:"",schema:"wss"},{"URI Scheme":"wtai",Template:"prov/wtai",Description:"wtai",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"wtai"},{"URI Scheme":"wyciwyg",Template:"prov/wyciwyg",Description:"wyciwyg",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"wyciwyg"},{"URI Scheme":"xcon",Template:"",Description:"xcon",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC6501]",Notes:"",schema:"xcon"},{"URI Scheme":"xcon-userid",Template:"",Description:"xcon-userid",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC6501]",Notes:"",schema:"xcon-userid"},{"URI Scheme":"xfire",Template:"prov/xfire",Description:"xfire",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"xfire"},{"URI Scheme":"xmlrpc.beep",Template:"",Description:"xmlrpc.beep",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC3529]",Notes:"",schema:"xmlrpc.beep"},{"URI Scheme":"xmlrpc.beeps",Template:"",Description:"xmlrpc.beeps",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC3529]",Notes:"",schema:"xmlrpc.beeps"},{"URI Scheme":"xmpp",Template:"",Description:"Extensible Messaging and Presence Protocol",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC5122]",Notes:"",schema:"xmpp"},{"URI Scheme":"xri",Template:"prov/xri",Description:"xri",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"xri"},{"URI Scheme":"ymsgr",Template:"prov/ymsgr",Description:"ymsgr",Status:"Provisional","Well-Known URI Support":"-",Reference:"[Dave_Thaler]",Notes:"",schema:"ymsgr"},{"URI Scheme":"z39.50",Template:"",Description:"Z39.50 information access",Status:"Historical","Well-Known URI Support":"-",Reference:"[RFC1738][RFC2056]",Notes:"",schema:"z39.50"},{"URI Scheme":"z39.50r",Template:"",Description:"Z39.50 Retrieval",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC2056]",Notes:"",schema:"z39.50r"},{"URI Scheme":"z39.50s",Template:"",Description:"Z39.50 Session",Status:"Permanent","Well-Known URI Support":"-",Reference:"[RFC2056]",Notes:"",schema:"z39.50s"}]});var Lt=mo((Le,Ot)=>{(function(o,e){typeof Le=="object"&&typeof Ot=="object"?Ot.exports=e():typeof define=="function"&&define.amd?define([],e):typeof Le=="object"?Le.ls=e():o.ls=e()})(Le,function(){return(()=>{"use strict";var o={d:(u,f)=>{for(var m in f)o.o(f,m)&&!o.o(u,m)&&Object.defineProperty(u,m,{enumerable:!0,get:f[m]})},o:(u,f)=>Object.prototype.hasOwnProperty.call(u,f)},e={};o.d(e,{default:()=>p});let t=(...u)=>{},r=u=>u!==null&&u.constructor.name==="Object",n,s=()=>{if(n!==void 0)return n;n=!0;try{localStorage||(n=!1)}catch(u){n=!1}return l(),n},i=String.fromCharCode(0),a=(u,f,m=!0)=>m?[...JSON.stringify(u)].map(h=>String.fromCharCode(h.charCodeAt(0)+f)).join(""):JSON.parse([...u].map(h=>String.fromCharCode(h.charCodeAt(0)-f)).join("")),c={ttl:null,encrypt:!1,encrypter:a,decrypter:(u,f)=>a(u,f,!1),secret:75},l=(u=!1)=>{if(!s())return!1;Object.keys(localStorage).forEach(f=>{let m=localStorage.getItem(f);if(!m)return;let h;try{h=JSON.parse(m)}catch(g){return}r(h)&&i in h&&(Date.now()>h.ttl||u)&&localStorage.removeItem(f)})},p={config:c,set:(u,f,m={})=>{if(!s())return!1;let h=Object.assign(Object.assign(Object.assign({},c),m),{encrypt:m.encrypt!==!1&&(m.encrypt||c.encrypt),ttl:m.ttl===null?null:m.ttl||c.ttl});try{let g=h.ttl&&!isNaN(h.ttl)&&h.ttl>0,S=g?{[i]:f,ttl:Date.now()+1e3*h.ttl}:f;h.encrypt&&(g?S[i]=(h.encrypter||t)(S[i],h.secret):S=(h.encrypter||t)(S,h.secret)),localStorage.setItem(u,JSON.stringify(S))}catch(g){return!1}},get:(u,f={})=>{if(!s())return null;let m=localStorage.getItem(u);if(!m)return null;let h=Object.assign(Object.assign(Object.assign({},c),f),{encrypt:f.encrypt!==!1&&(f.encrypt||c.encrypt),ttl:f.ttl===null?null:f.ttl||c.ttl}),g=JSON.parse(m),S=r(g)&&i in g;if(h.decrypt||h.encrypt)try{S?g[i]=(h.decrypter||t)(g[i],h.secret):g=(h.decrypter||t)(g,h.secret)}catch(R){}return S?Date.now()>g.ttl?(localStorage.removeItem(u),null):g[i]:g},flush:l,clear:()=>{if(!s())return!1;localStorage.clear()},remove:u=>{if(!s())return!1;localStorage.removeItem(u)}};return e.default})()})});function pt(){let o=at.StateEffect.define(),e=at.StateField.define({create(){return ct.Decoration.none},update(t,r){return r.effects.reduce((n,s)=>s.is(o)?s.value:n,t.map(r.changes))},provide:t=>ct.EditorView.decorations.from(t)});return{update:o,field:e}}var at,ct,Vt=Te(()=>{at=N(require("@codemirror/state")),ct=N(require("@codemirror/view"))});var ir,Zt,ar=Te(()=>{ir=N(require("@codemirror/view")),Zt=class extends ir.WidgetType{constructor(e,t,r,n,s){super();this.plugin=e,this.icon=t,this.fallbackIcon=r,this.qualifier=n,this.token=s}eq(e){return e===this}toDOM(){if(!this.icon||this.icon==="")return console.log("空图标："+this.qualifier),activeDocument.createElement("span");if(typeof this.icon!="string")return this.icon.cloneNode(!0);if(!this.icon.startsWith("http")){let t=activeDocument.createElement("span");return t.textContent=this.icon,t}let e=activeDocument.createElement("span");return this.plugin.iconAdder.getImageEl(this.icon,this.qualifier).then(t=>{e.append(t)}).catch(t=>{console.error(t)}),e}ignoreEvent(){return!0}}});var vt,cr,Yt,pr=Te(()=>{vt=N(require("@codemirror/view")),cr=N(require("obsidian"));ke();ar();Qt();Yt=class{constructor(e,t){this.decoCache=Object.create(null);this.editor=e,this.plugin=t,this.debouncedUpdate=(0,cr.debounce)(this.updateAsyncDecorations,this.plugin.settings.debounce,!0)}computeAsyncDecorations(e){return v(this,null,function*(){let t=[];for(let r of e){let n=this.decoCache[r.value];if(!n){let s=L[this.plugin.settings.provider],i=L[this.plugin.settings.fallbackProvider],a=yield this.plugin.getIcon(r.value,s),c=yield this.plugin.getIcon(r.value,i),l=this.plugin.iconAdder.constructURL(r.value);if(l){let p=l.protocol.contains("http")?l.hostname:l.protocol;n=this.decoCache[r.value]=vt.Decoration.widget({widget:new Zt(this.plugin,a,c,p,r)})}}t.push(n.range(r.from,r.from))}return vt.Decoration.set(t,!0)})}updateAsyncDecorations(e){return v(this,null,function*(){let t=yield this.computeAsyncDecorations(e);(t||this.editor.state.field(ze.field).size)&&this.editor.dispatch({effects:ze.update.of(t||vt.Decoration.none)})})}}});function lr(o,e){return os(o,e,"[","]")}function os(o,e,t,r){if(!o.includes(t))return 0;let n=e,s=1;for(;s>0;){let i=o[--n];if(i===void 0)break;i==t?s--:i==r&&s++}return n}var mr=Te(()=>{});var fr={};uo(fr,{asyncDecoBuilderExt:()=>ns,iconDecorations:()=>ze});function rs(o){return ur.ViewPlugin.fromClass(class{constructor(e){this.decoManager=new Yt(e,o),this.buildAsyncDecorations(e)}update(e){let t=e.startState.field(qe.editorLivePreviewField)!=e.state.field(qe.editorLivePreviewField);(e.docChanged||e.viewportChanged||t)&&this.buildAsyncDecorations(e.view)}destroy(){}buildAsyncDecorations(e){let t=[];if(e.state.field(qe.editorLivePreviewField)&&!o.settings.enableLivePreview){this.decoManager.debouncedUpdate(t);return}if(!e.state.field(qe.editorLivePreviewField)&&!o.settings.enableSource){this.decoManager.debouncedUpdate(t);return}for(let{from:r,to:n}of e.visibleRanges)(0,gt.syntaxTree)(e.state).iterate({from:r,to:n,enter:i=>{let a=i.type.prop(gt.tokenClassNodeProp);if(a){let l=new Set(a.split(" ")).has("url"),p=e.state.sliceDoc(i.from,i.to);if(l&&p.includes(":")){if(p=p.replace(/[<>]/g,""),e.state.doc.sliceString(i.from-1,i.from)!=="("){if(!o.settings.showLink)return;o.settings.iconPosition==="front"&&t.push({from:i.from,to:i.to,value:p}),o.settings.iconPosition==="back"&&t.push({from:i.to,to:i.to+1,value:p});return}if(!o.settings.showAliased)return;let f=e.state.doc.lineAt(i.from),m=f.to-i.to,h=f.length-m,g=f.text.lastIndexOf("]",h),S=lr(f.text,g);if(S===-1)return;let R=f.from+S;if(e.state.sliceDoc(R,i.to).contains("|nofavicon"))return;o.settings.iconPosition==="front"&&t.push({from:R,to:i.to,value:p}),o.settings.iconPosition==="back"&&t.push({from:i.to,to:i.to+1,value:p})}}}});this.decoManager.debouncedUpdate(t)}})}function ns(o){return[ze.field,rs(o)]}var ur,gt,qe,ze,Qt=Te(()=>{ur=N(require("@codemirror/view")),gt=N(require("@codemirror/language"));pr();mr();qe=N(require("obsidian"));Vt();ze=pt()});uo(exports,{default:()=>eo});var hr=N(require("obsidian"));var y=N(require("obsidian"));ke();var ae=N(require("obsidian"));var as=N(require("obsidian")),le=o=>{var e;return o?(e=o.app.plugins.plugins["obsidian-icon-shortcodes"])===null||e===void 0?void 0:e.api:window.IconSCAPIv0},Qe=o=>le(o)!==void 0;var Eo=N(require("obsidian"));var _="top",E="bottom",k="right",C="left",et="auto",me=[_,E,k,C],ne="start",ve="end",fo="clippingParents",tt="viewport",ye="popper",ho="reference",Ut=me.reduce(function(o,e){return o.concat([e+"-"+ne,e+"-"+ve])},[]),ot=[].concat(me,[et]).reduce(function(o,e){return o.concat([e,e+"-"+ne,e+"-"+ve])},[]),Dr="beforeRead",xr="read",Nr="afterRead",_r="beforeMain",Wr="main",Cr="afterMain",Kr="beforeWrite",kr="write",Er="afterWrite",vo=[Dr,xr,Nr,_r,Wr,Cr,Kr,kr,Er];function F(o){return o?(o.nodeName||"").toLowerCase():null}function K(o){if(o==null)return window;if(o.toString()!=="[object Window]"){var e=o.ownerDocument;return e&&e.defaultView||window}return o}function oe(o){var e=K(o).Element;return o instanceof e||o instanceof Element}function A(o){var e=K(o).HTMLElement;return o instanceof e||o instanceof HTMLElement}function rt(o){if(typeof ShadowRoot=="undefined")return!1;var e=K(o).ShadowRoot;return o instanceof e||o instanceof ShadowRoot}function Ar(o){var e=o.state;Object.keys(e.elements).forEach(function(t){var r=e.styles[t]||{},n=e.attributes[t]||{},s=e.elements[t];!A(s)||!F(s)||(Object.assign(s.style,r),Object.keys(n).forEach(function(i){var a=n[i];a===!1?s.removeAttribute(i):s.setAttribute(i,a===!0?"":a)}))})}function Fr(o){var e=o.state,t={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(e.elements.popper.style,t.popper),e.styles=t,e.elements.arrow&&Object.assign(e.elements.arrow.style,t.arrow),function(){Object.keys(e.elements).forEach(function(r){var n=e.elements[r],s=e.attributes[r]||{},i=Object.keys(e.styles.hasOwnProperty(r)?e.styles[r]:t[r]),a=i.reduce(function(c,l){return c[l]="",c},{});!A(n)||!F(n)||(Object.assign(n.style,a),Object.keys(s).forEach(function(c){n.removeAttribute(c)}))})}}var go={name:"应用样式",enabled:!0,phase:"write",fn:Ar,effect:Fr,requires:["computeStyles"]};function M(o){return o.split("-")[0]}var Z=Math.max,ge=Math.min,se=Math.round;function $(o,e){e===void 0&&(e=!1);var t=o.getBoundingClientRect(),r=1,n=1;if(A(o)&&e){var s=o.offsetHeight,i=o.offsetWidth;i>0&&(r=se(t.width)/i||1),s>0&&(n=se(t.height)/s||1)}return{width:t.width/r,height:t.height/n,top:t.top/n,right:t.right/r,bottom:t.bottom/n,left:t.left/r,x:t.left/r,y:t.top/n}}function Se(o){var e=$(o),t=o.offsetWidth,r=o.offsetHeight;return Math.abs(e.width-t)<=1&&(t=e.width),Math.abs(e.height-r)<=1&&(r=e.height),{x:o.offsetLeft,y:o.offsetTop,width:t,height:r}}function Ee(o,e){var t=e.getRootNode&&e.getRootNode();if(o.contains(e))return!0;if(t&&rt(t)){var r=e;do{if(r&&o.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function B(o){return K(o).getComputedStyle(o)}function Pt(o){return["table","td","th"].indexOf(F(o))>=0}function H(o){return((oe(o)?o.ownerDocument:o.document)||window.document).documentElement}function ie(o){return F(o)==="html"?o:o.assignedSlot||o.parentNode||(rt(o)?o.host:null)||H(o)}function So(o){return!A(o)||B(o).position==="fixed"?null:o.offsetParent}function Mr(o){var e=navigator.userAgent.toLowerCase().indexOf("firefox")!==-1,t=navigator.userAgent.indexOf("Trident")!==-1;if(t&&A(o)){var r=B(o);if(r.position==="fixed")return null}for(var n=ie(o);A(n)&&["html","body"].indexOf(F(n))<0;){var s=B(n);if(s.transform!=="none"||s.perspective!=="none"||s.contain==="paint"||["transform","perspective"].indexOf(s.willChange)!==-1||e&&s.willChange==="filter"||e&&s.filter&&s.filter!=="none")return n;n=n.parentNode}return null}function Y(o){for(var e=K(o),t=So(o);t&&Pt(t)&&B(t).position==="static";)t=So(t);return t&&(F(t)==="html"||F(t)==="body"&&B(t).position==="static")?e:t||Mr(o)||e}function Re(o){return["top","bottom"].indexOf(o)>=0?"x":"y"}function we(o,e,t){return Z(o,ge(e,t))}function Ro(o,e,t){var r=we(o,e,t);return r>t?t:r}function Ae(){return{top:0,right:0,bottom:0,left:0}}function Fe(o){return Object.assign({},Ae(),o)}function Me(o,e){return e.reduce(function(t,r){return t[r]=o,t},{})}var jr=function(e,t){return e=typeof e=="function"?e(Object.assign({},t.rects,{placement:t.placement})):e,Fe(typeof e!="number"?e:Me(e,me))};function Or(o){var e,t=o.state,r=o.name,n=o.options,s=t.elements.arrow,i=t.modifiersData.popperOffsets,a=M(t.placement),c=Re(a),l=[C,k].indexOf(a)>=0,p=l?"height":"width";if(!(!s||!i)){var u=jr(n.padding,t),f=Se(s),m=c==="y"?_:C,h=c==="y"?E:k,g=t.rects.reference[p]+t.rects.reference[c]-i[c]-t.rects.popper[p],S=i[c]-t.rects.reference[c],R=Y(s),b=R?c==="y"?R.clientHeight||0:R.clientWidth||0:0,x=g/2-S/2,w=u[m],P=b-f[p]-u[h],I=b/2-f[p]/2+x,U=we(w,I,P),W=c;t.modifiersData[r]=(e={},e[W]=U,e.centerOffset=U-I,e)}}function Lr(o){var e=o.state,t=o.options,r=t.element,n=r===void 0?"[data-popper-arrow]":r;n!=null&&(typeof n=="string"&&(n=e.elements.popper.querySelector(n),!n)||!Ee(e.elements.popper,n)||(e.elements.arrow=n))}var wo={name:"箭头",enabled:!0,phase:"main",fn:Or,effect:Lr,requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function X(o){return o.split("-")[1]}var Hr={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Br(o){var e=o.x,t=o.y,r=window,n=r.devicePixelRatio||1;return{x:se(e*n)/n||0,y:se(t*n)/n||0}}function Io(o){var e,t=o.popper,r=o.popperRect,n=o.placement,s=o.variation,i=o.offsets,a=o.position,c=o.gpuAcceleration,l=o.adaptive,p=o.roundOffsets,u=o.isFixed,f=i.x,m=f===void 0?0:f,h=i.y,g=h===void 0?0:h,S=typeof p=="function"?p({x:m,y:g}):{x:m,y:g};m=S.x,g=S.y;var R=i.hasOwnProperty("x"),b=i.hasOwnProperty("y"),x=C,w=_,P=window;if(l){var I=Y(t),U="clientHeight",W="clientWidth";if(I===K(t)&&(I=H(t),B(I).position!=="static"&&a==="absolute"&&(U="scrollHeight",W="scrollWidth")),I=I,n===_||(n===C||n===k)&&s===ve){w=E;var j=u&&P.visualViewport?P.visualViewport.height:I[U];g-=j-r.height,g*=c?1:-1}if(n===C||(n===_||n===E)&&s===ve){x=k;var O=u&&P.visualViewport?P.visualViewport.width:I[W];m-=O-r.width,m*=c?1:-1}}var T=Object.assign({position:a},l&&Hr),V=p===!0?Br({x:m,y:g}):{x:m,y:g};if(m=V.x,g=V.y,c){var z;return Object.assign({},T,(z={},z[w]=b?"0":"",z[x]=R?"0":"",z.transform=(P.devicePixelRatio||1)<=1?"translate("+m+"px, "+g+"px)":"translate3d("+m+"px, "+g+"px, 0)",z))}return Object.assign({},T,(e={},e[w]=b?g+"px":"",e[x]=R?m+"px":"",e.transform="",e))}function Vr(o){var e=o.state,t=o.options,r=t.gpuAcceleration,n=r===void 0?!0:r,s=t.adaptive,i=s===void 0?!0:s,a=t.roundOffsets,c=a===void 0?!0:a;if(!1)var l;var p={placement:M(e.placement),variation:X(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:n,isFixed:e.options.strategy==="fixed"};e.modifiersData.popperOffsets!=null&&(e.styles.popper=Object.assign({},e.styles.popper,Io(Object.assign({},p,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:i,roundOffsets:c})))),e.modifiersData.arrow!=null&&(e.styles.arrow=Object.assign({},e.styles.arrow,Io(Object.assign({},p,{offsets:e.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:c})))),e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-placement":e.placement})}var bo={name:"计算样式",enabled:!0,phase:"beforeWrite",fn:Vr,data:{}};var nt={passive:!0};function zr(o){var e=o.state,t=o.instance,r=o.options,n=r.scroll,s=n===void 0?!0:n,i=r.resize,a=i===void 0?!0:i,c=K(e.elements.popper),l=[].concat(e.scrollParents.reference,e.scrollParents.popper);return s&&l.forEach(function(p){p.addEventListener("scroll",t.update,nt)}),a&&c.addEventListener("resize",t.update,nt),function(){s&&l.forEach(function(p){p.removeEventListener("scroll",t.update,nt)}),a&&c.removeEventListener("resize",t.update,nt)}}var Uo={name:"事件监听器",enabled:!0,phase:"write",fn:function(){},effect:zr,data:{}};var qr={left:"right",right:"left",bottom:"top",top:"bottom"};function De(o){return o.replace(/left|right|bottom|top/g,function(e){return qr[e]})}var Gr={start:"end",end:"start"};function st(o){return o.replace(/start|end/g,function(e){return Gr[e]})}function Ie(o){var e=K(o),t=e.pageXOffset,r=e.pageYOffset;return{scrollLeft:t,scrollTop:r}}function be(o){return $(H(o)).left+Ie(o).scrollLeft}function Tt(o){var e=K(o),t=H(o),r=e.visualViewport,n=t.clientWidth,s=t.clientHeight,i=0,a=0;return r&&(n=r.width,s=r.height,/^((?!chrome|android).)*safari/i.test(navigator.userAgent)||(i=r.offsetLeft,a=r.offsetTop)),{width:n,height:s,x:i+be(o),y:a}}function yt(o){var e,t=H(o),r=Ie(o),n=(e=o.ownerDocument)==null?void 0:e.body,s=Z(t.scrollWidth,t.clientWidth,n?n.scrollWidth:0,n?n.clientWidth:0),i=Z(t.scrollHeight,t.clientHeight,n?n.scrollHeight:0,n?n.clientHeight:0),a=-r.scrollLeft+be(o),c=-r.scrollTop;return B(n||t).direction==="rtl"&&(a+=Z(t.clientWidth,n?n.clientWidth:0)-s),{width:s,height:i,x:a,y:c}}function Ue(o){var e=B(o),t=e.overflow,r=e.overflowX,n=e.overflowY;return/auto|scroll|overlay|hidden/.test(t+n+r)}function it(o){return["html","body","#document"].indexOf(F(o))>=0?o.ownerDocument.body:A(o)&&Ue(o)?o:it(ie(o))}function ue(o,e){var t;e===void 0&&(e=[]);var r=it(o),n=r===((t=o.ownerDocument)==null?void 0:t.body),s=K(r),i=n?[s].concat(s.visualViewport||[],Ue(r)?r:[]):r,a=e.concat(i);return n?a:a.concat(ue(ie(i)))}function xe(o){return Object.assign({},o,{left:o.x,top:o.y,right:o.x+o.width,bottom:o.y+o.height})}function $r(o){var e=$(o);return e.top=e.top+o.clientTop,e.left=e.left+o.clientLeft,e.bottom=e.top+o.clientHeight,e.right=e.left+o.clientWidth,e.width=o.clientWidth,e.height=o.clientHeight,e.x=e.left,e.y=e.top,e}function Po(o,e){return e===tt?xe(Tt(o)):oe(e)?$r(e):xe(yt(H(o)))}function Xr(o){var e=ue(ie(o)),t=["absolute","fixed"].indexOf(B(o).position)>=0,r=t&&A(o)?Y(o):o;return oe(r)?e.filter(function(n){return oe(n)&&Ee(n,r)&&F(n)!=="body"}):[]}function Dt(o,e,t){var r=e==="clippingParents"?Xr(o):[].concat(e),n=[].concat(r,[t]),s=n[0],i=n.reduce(function(a,c){var l=Po(o,c);return a.top=Z(l.top,a.top),a.right=ge(l.right,a.right),a.bottom=ge(l.bottom,a.bottom),a.left=Z(l.left,a.left),a},Po(o,s));return i.width=i.right-i.left,i.height=i.bottom-i.top,i.x=i.left,i.y=i.top,i}function je(o){var e=o.reference,t=o.element,r=o.placement,n=r?M(r):null,s=r?X(r):null,i=e.x+e.width/2-t.width/2,a=e.y+e.height/2-t.height/2,c;switch(n){case _:c={x:i,y:e.y-t.height};break;case E:c={x:i,y:e.y+e.height};break;case k:c={x:e.x+e.width,y:a};break;case C:c={x:e.x-t.width,y:a};break;default:c={x:e.x,y:e.y}}var l=n?Re(n):null;if(l!=null){var p=l==="y"?"height":"width";switch(s){case ne:c[l]=c[l]-(e[p]/2-t[p]/2);break;case ve:c[l]=c[l]+(e[p]/2-t[p]/2);break;default:}}return c}function Q(o,e){e===void 0&&(e={});var t=e,r=t.placement,n=r===void 0?o.placement:r,s=t.boundary,i=s===void 0?fo:s,a=t.rootBoundary,c=a===void 0?tt:a,l=t.elementContext,p=l===void 0?ye:l,u=t.altBoundary,f=u===void 0?!1:u,m=t.padding,h=m===void 0?0:m,g=Fe(typeof h!="number"?h:Me(h,me)),S=p===ye?ho:ye,R=o.rects.popper,b=o.elements[f?S:p],x=Dt(oe(b)?b:b.contextElement||H(o.elements.popper),i,c),w=$(o.elements.reference),P=je({reference:w,element:R,strategy:"absolute",placement:n}),I=xe(Object.assign({},R,P)),U=p===ye?I:w,W={top:x.top-U.top+g.top,bottom:U.bottom-x.bottom+g.bottom,left:x.left-U.left+g.left,right:U.right-x.right+g.right},j=o.modifiersData.offset;if(p===ye&&j){var O=j[n];Object.keys(W).forEach(function(T){var V=[k,E].indexOf(T)>=0?1:-1,z=[_,E].indexOf(T)>=0?"y":"x";W[T]+=O[z]*V})}return W}function xt(o,e){e===void 0&&(e={});var t=e,r=t.placement,n=t.boundary,s=t.rootBoundary,i=t.padding,a=t.flipVariations,c=t.allowedAutoPlacements,l=c===void 0?ot:c,p=X(r),u=p?a?Ut:Ut.filter(function(h){return X(h)===p}):me,f=u.filter(function(h){return l.indexOf(h)>=0});f.length===0&&(f=u);var m=f.reduce(function(h,g){return h[g]=Q(o,{placement:g,boundary:n,rootBoundary:s,padding:i})[M(g)],h},{});return Object.keys(m).sort(function(h,g){return m[h]-m[g]})}function Jr(o){if(M(o)===et)return[];var e=De(o);return[st(o),e,st(e)]}function Zr(o){var e=o.state,t=o.options,r=o.name;if(!e.modifiersData[r]._skip){for(var n=t.mainAxis,s=n===void 0?!0:n,i=t.altAxis,a=i===void 0?!0:i,c=t.fallbackPlacements,l=t.padding,p=t.boundary,u=t.rootBoundary,f=t.altBoundary,m=t.flipVariations,h=m===void 0?!0:m,g=t.allowedAutoPlacements,S=e.options.placement,R=M(S),b=R===S,x=c||(b||!h?[De(S)]:Jr(S)),w=[S].concat(x).reduce(function(Pe,pe){return Pe.concat(M(pe)===et?xt(e,{placement:pe,boundary:p,rootBoundary:u,padding:l,flipVariations:h,allowedAutoPlacements:g}):pe)},[]),P=e.rects.reference,I=e.rects.popper,U=new Map,W=!0,j=w[0],O=0;O<w.length;O++){var T=w[O],V=M(T),z=X(T)===ne,_e=[_,E].indexOf(V)>=0,We=_e?"width":"height",q=Q(e,{placement:T,boundary:p,rootBoundary:u,altBoundary:f,padding:l}),J=_e?z?k:C:z?E:_;P[We]>I[We]&&(J=De(J));var Ge=De(J),fe=[];if(s&&fe.push(q[V]<=0),a&&fe.push(q[J]<=0,q[Ge]<=0),fe.every(function(Pe){return Pe})){j=T,W=!1;break}U.set(T,fe)}if(W)for(var $e=h?3:1,St=function(pe){var Ke=w.find(function(Je){var he=U.get(Je);if(he)return he.slice(0,pe).every(function(Rt){return Rt})});if(Ke)return j=Ke,"break"},Ce=$e;Ce>0;Ce--){var Xe=St(Ce);if(Xe==="break")break}e.placement!==j&&(e.modifiersData[r]._skip=!0,e.placement=j,e.reset=!0)}}var To={name:"翻转",enabled:!0,phase:"main",fn:Zr,requiresIfExists:["offset"],data:{_skip:!1}};function yo(o,e,t){return t===void 0&&(t={x:0,y:0}),{top:o.top-e.height-t.y,right:o.right-e.width+t.x,bottom:o.bottom-e.height+t.y,left:o.left-e.width-t.x}}function Do(o){return[_,k,E,C].some(function(e){return o[e]>=0})}function Yr(o){var e=o.state,t=o.name,r=e.rects.reference,n=e.rects.popper,s=e.modifiersData.preventOverflow,i=Q(e,{elementContext:"参考"}),a=Q(e,{altBoundary:!0}),c=yo(i,r),l=yo(a,n,s),p=Do(c),u=Do(l);e.modifiersData[t]={referenceClippingOffsets:c,popperEscapeOffsets:l,isReferenceHidden:p,hasPopperEscaped:u},e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-reference-hidden":p,"data-popper-escaped":u})}var xo={name:"隐藏",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:Yr};function Qr(o,e,t){var r=M(o),n=[C,_].indexOf(r)>=0?-1:1,s=typeof t=="function"?t(Object.assign({},e,{placement:o})):t,i=s[0],a=s[1];return i=i||0,a=(a||0)*n,[C,k].indexOf(r)>=0?{x:a,y:i}:{x:i,y:a}}function en(o){var e=o.state,t=o.options,r=o.name,n=t.offset,s=n===void 0?[0,0]:n,i=ot.reduce(function(p,u){return p[u]=Qr(u,e.rects,s),p},{}),a=i[e.placement],c=a.x,l=a.y;e.modifiersData.popperOffsets!=null&&(e.modifiersData.popperOffsets.x+=c,e.modifiersData.popperOffsets.y+=l),e.modifiersData[r]=i}var No={name:"偏移",enabled:!0,phase:"main",requires:["popperOffsets"],fn:en};function tn(o){var e=o.state,t=o.name;e.modifiersData[t]=je({reference:e.rects.reference,element:e.rects.popper,strategy:"absolute",placement:e.placement})}var _o={name:"弹出偏移",enabled:!0,phase:"read",fn:tn,data:{}};function Nt(o){return o==="x"?"y":"x"}function on(o){var e=o.state,t=o.options,r=o.name,n=t.mainAxis,s=n===void 0?!0:n,i=t.altAxis,a=i===void 0?!1:i,c=t.boundary,l=t.rootBoundary,p=t.altBoundary,u=t.padding,f=t.tether,m=f===void 0?!0:f,h=t.tetherOffset,g=h===void 0?0:h,S=Q(e,{boundary:c,rootBoundary:l,padding:u,altBoundary:p}),R=M(e.placement),b=X(e.placement),x=!b,w=Re(R),P=Nt(w),I=e.modifiersData.popperOffsets,U=e.rects.reference,W=e.rects.popper,j=typeof g=="function"?g(Object.assign({},e.rects,{placement:e.placement})):g,O=typeof j=="number"?{mainAxis:j,altAxis:j}:Object.assign({mainAxis:0,altAxis:0},j),T=e.modifiersData.offset?e.modifiersData.offset[e.placement]:null,V={x:0,y:0};if(!!I){if(s){var z,_e=w==="y"?_:C,We=w==="y"?E:k,q=w==="y"?"height":"width",J=I[w],Ge=J+S[_e],fe=J-S[We],$e=m?-W[q]/2:0,St=b===ne?U[q]:W[q],Ce=b===ne?-W[q]:-U[q],Xe=e.elements.arrow,Pe=m&&Xe?Se(Xe):{width:0,height:0},pe=e.modifiersData["arrow#persistent"]?e.modifiersData["arrow#persistent"].padding:Ae(),Ke=pe[_e],Je=pe[We],he=we(0,U[q],Pe[q]),Rt=x?U[q]/2-$e-he-Ke-O.mainAxis:St-he-Ke-O.mainAxis,dr=x?-U[q]/2+$e+he+Je+O.mainAxis:Ce+he+Je+O.mainAxis,wt=e.elements.arrow&&Y(e.elements.arrow),vr=wt?w==="y"?wt.clientTop||0:wt.clientLeft||0:0,to=(z=T==null?void 0:T[w])!=null?z:0,gr=J+Rt-to-vr,Sr=J+dr-to,oo=we(m?ge(Ge,gr):Ge,J,m?Z(fe,Sr):fe);I[w]=oo,V[w]=oo-J}if(a){var ro,Rr=w==="x"?_:C,wr=w==="x"?E:k,de=I[P],Ze=P==="y"?"height":"width",no=de+S[Rr],so=de-S[wr],It=[_,C].indexOf(R)!==-1,io=(ro=T==null?void 0:T[P])!=null?ro:0,ao=It?no:de-U[Ze]-W[Ze]-io+O.altAxis,co=It?de+U[Ze]+W[Ze]-io-O.altAxis:so,po=m&&It?Ro(ao,de,co):we(m?ao:no,de,m?co:so);I[P]=po,V[P]=po-de}e.modifiersData[r]=V}}var Wo={name:"防止溢出",enabled:!0,phase:"main",fn:on,requiresIfExists:["offset"]};function _t(o){return{scrollLeft:o.scrollLeft,scrollTop:o.scrollTop}}function Wt(o){return o===K(o)||!A(o)?Ie(o):_t(o)}function rn(o){var e=o.getBoundingClientRect(),t=se(e.width)/o.offsetWidth||1,r=se(e.height)/o.offsetHeight||1;return t!==1||r!==1}function Ct(o,e,t){t===void 0&&(t=!1);var r=A(e),n=A(e)&&rn(e),s=H(e),i=$(o,n),a={scrollLeft:0,scrollTop:0},c={x:0,y:0};return(r||!r&&!t)&&((F(e)!=="body"||Ue(s))&&(a=Wt(e)),A(e)?(c=$(e,!0),c.x+=e.clientLeft,c.y+=e.clientTop):s&&(c.x=be(s))),{x:i.left+a.scrollLeft-c.x,y:i.top+a.scrollTop-c.y,width:i.width,height:i.height}}function nn(o){var e=new Map,t=new Set,r=[];o.forEach(function(s){e.set(s.name,s)});function n(s){t.add(s.name);var i=[].concat(s.requires||[],s.requiresIfExists||[]);i.forEach(function(a){if(!t.has(a)){var c=e.get(a);c&&n(c)}}),r.push(s)}return o.forEach(function(s){t.has(s.name)||n(s)}),r}function Kt(o){var e=nn(o);return vo.reduce(function(t,r){return t.concat(e.filter(function(n){return n.phase===r}))},[])}function kt(o){var e;return function(){return e||(e=new Promise(function(t){Promise.resolve().then(function(){e=void 0,t(o())})})),e}}function Et(o){var e=o.reduce(function(t,r){var n=t[r.name];return t[r.name]=n?Object.assign({},n,r,{options:Object.assign({},n.options,r.options),data:Object.assign({},n.data,r.data)}):r,t},{});return Object.keys(e).map(function(t){return e[t]})}var Co={placement:"bottom",modifiers:[],strategy:"absolute"};function Ko(){for(var o=arguments.length,e=new Array(o),t=0;t<o;t++)e[t]=arguments[t];return!e.some(function(r){return!(r&&typeof r.getBoundingClientRect=="function")})}function ko(o){o===void 0&&(o={});var e=o,t=e.defaultModifiers,r=t===void 0?[]:t,n=e.defaultOptions,s=n===void 0?Co:n;return function(a,c,l){l===void 0&&(l=s);var p={placement:"bottom",orderedModifiers:[],options:Object.assign({},Co,s),modifiersData:{},elements:{reference:a,popper:c},attributes:{},styles:{}},u=[],f=!1,m={state:p,setOptions:function(R){var b=typeof R=="function"?R(p.options):R;g(),p.options=Object.assign({},s,p.options,b),p.scrollParents={reference:oe(a)?ue(a):a.contextElement?ue(a.contextElement):[],popper:ue(c)};var x=Kt(Et([].concat(r,p.options.modifiers)));if(p.orderedModifiers=x.filter(function(T){return T.enabled}),!1){var w;if(getBasePlacement(p.options.placement)===auto)var P;var I,U,W,j,O}return h(),m.update()},forceUpdate:function(){if(!f){var R=p.elements,b=R.reference,x=R.popper;if(!!Ko(b,x)){p.rects={reference:Ct(b,Y(x),p.options.strategy==="fixed"),popper:Se(x)},p.reset=!1,p.placement=p.options.placement,p.orderedModifiers.forEach(function(T){return p.modifiersData[T.name]=Object.assign({},T.data)});for(var w=0,P=0;P<p.orderedModifiers.length;P++){if(p.reset===!0){p.reset=!1,P=-1;continue}var I=p.orderedModifiers[P],U=I.fn,W=I.options,j=W===void 0?{}:W,O=I.name;typeof U=="function"&&(p=U({state:p,options:j,name:O,instance:m})||p)}}}},update:kt(function(){return new Promise(function(S){m.forceUpdate(),S(p)})}),destroy:function(){g(),f=!0}};if(!Ko(a,c))return m;m.setOptions(l).then(function(S){!f&&l.onFirstUpdate&&l.onFirstUpdate(S)});function h(){p.orderedModifiers.forEach(function(S){var R=S.name,b=S.options,x=b===void 0?{}:b,w=S.effect;if(typeof w=="function"){var P=w({state:p,name:R,instance:m,options:x}),I=function(){};u.push(P||I)}})}function g(){u.forEach(function(S){return S()}),u=[]}return m}}var sn=[Uo,_o,bo,go,No,To,Wo,wo,xo],At=ko({defaultModifiers:sn});var an=(o,e)=>(o%e+e)%e,Ao=class{constructor(e,t,r){this.owner=e,this.containerEl=t,t.on("click",".suggestion-item",this.onSuggestionClick.bind(this)),t.on("mousemove",".suggestion-item",this.onSuggestionMouseover.bind(this)),r.register([],"ArrowUp",n=>{if(!n.isComposing)return this.setSelectedItem(this.selectedItem-1,!0),!1}),r.register([],"ArrowDown",n=>{if(!n.isComposing)return this.setSelectedItem(this.selectedItem+1,!0),!1}),r.register([],"Enter",n=>{if(!n.isComposing)return this.useSelectedItem(n),!1})}onSuggestionClick(e,t){e.preventDefault();let r=this.suggestions.indexOf(t);this.setSelectedItem(r,!1),this.useSelectedItem(e)}onSuggestionMouseover(e,t){let r=this.suggestions.indexOf(t);this.setSelectedItem(r,!1)}setSuggestions(e){this.containerEl.empty();let t=[];e.forEach(r=>{let n=this.containerEl.createDiv("suggestion-item");this.owner.renderSuggestion(r,n),t.push(n)}),this.values=e,this.suggestions=t,this.setSelectedItem(0,!1)}useSelectedItem(e){let t=this.values[this.selectedItem];t&&this.owner.selectSuggestion(t,e)}setSelectedItem(e,t){let r=an(e,this.suggestions.length),n=this.suggestions[this.selectedItem],s=this.suggestions[r];n==null||n.removeClass("is-selected"),s==null||s.addClass("is-selected"),this.selectedItem=r,t&&s.scrollIntoView(!1)}},Ft=class{constructor(e,t){this.app=e,this.inputEl=t,this.scope=new Eo.Scope,this.suggestEl=createDiv("suggestion-container");let r=this.suggestEl.createDiv("suggestion");this.suggest=new Ao(this,r,this.scope),this.scope.register([],"Escape",this.close.bind(this)),this.inputEl.addEventListener("input",this.onInputChanged.bind(this)),this.inputEl.addEventListener("focus",this.onInputChanged.bind(this)),this.inputEl.addEventListener("blur",this.close.bind(this)),this.suggestEl.on("mousedown",".suggestion-container",n=>{n.preventDefault()})}onInputChanged(){let e=this.inputEl.value,t=this.getSuggestions(e);if(!t){this.close();return}t.length>0?(this.suggest.setSuggestions(t),this.open(this.app.dom.appContainerEl,this.inputEl)):this.close()}open(e,t){this.app.keymap.pushScope(this.scope),e.appendChild(this.suggestEl),this.popper=At(t,this.suggestEl,{placement:"bottom-start",modifiers:[{name:"同宽",enabled:!0,fn:({state:r,instance:n})=>{let s=`${r.rects.reference.width}px`;r.styles.popper.width!==s&&(r.styles.popper.width=s,n.update())},phase:"beforeWrite",requires:["computeStyles"]}]})}close(){this.app.keymap.popScope(this.scope),this.suggest.setSuggestions([]),this.popper&&this.popper.destroy(),this.suggestEl.detach()}};var Mt=class extends Ft{constructor(e,t,r,n){super(e,t);this.content=r,this.descriptions=n}getSuggestions(e){let t=e.toLowerCase(),r=[...this.descriptions].filter(n=>n.name.toLowerCase().contains(t)||n.description.toLowerCase().contains(t));return Object.values(r).map(n=>n.name)}renderSuggestion(e,t){t.createSpan().setText(e+"   ");let r=[...this.descriptions].filter(n=>e===n.name)[0].description;r!==e&&t.createEl("small").setText(r)}selectSuggestion(e){this.inputEl.value=e,this.inputEl.trigger("input"),this.close()}};var Ne=class extends ae.Modal{constructor(e,t,r){super(e.app);this.name="Domain";this.plugin=e,r&&(this.name=r),t&&(this.domain=t.domain,this.icon=t.icon)}displayPreview(e){return v(this,null,function*(){if(Qe(this.plugin)&&this.icon){e.empty();let t=e.createDiv("preview");t.addClass("link-favicon-preview");let n=le(this.plugin).getIcon(this.icon,!1);n!==null&&t.append(n)}})}display(){return v(this,null,function*(){let{contentEl:e}=this;e.empty();let t,r=new ae.Setting(e).setName(this.name);if(this.name!=="Domain"){let a=Fo();a=a.filter(p=>!p.schema.contains("http"));let c=Object.values(a).map(p=>p.schema),l=Object.values(a).map(p=>({name:p.schema,description:p.Description}));r.addSearch(p=>{new Mt(this.plugin.app,p.inputEl,new Set(c),new Set(l)),p.setValue(this.domain).onChange(u=>{this.domain=u})})}else r.addText(a=>{a.setValue(this.domain).onChange(c=>{this.domain=c})});let n=le(this.plugin);n&&(n.version.compare(">=","0.6.1")?new ae.Setting(e).setName("图标").addButton(a=>{a.setButtonText("选择").onClick(()=>v(this,null,function*(){let c=yield n.getIconFromUser();c&&(this.icon=c.id,t&&(yield this.displayPreview(t)))}))}):new ae.Setting(e).setName("图标").addText(a=>{a.setValue(this.icon).onChange(c=>v(this,null,function*(){this.icon=c,t&&(yield this.displayPreview(t))}))})),t=e.createDiv("preview"),yield this.displayPreview(t);let s=e.createDiv(),i=new ae.Setting(s);i.addButton(a=>(a.setTooltip("保存").setIcon("checkmark").onClick(()=>v(this,null,function*(){this.icon&&this.domain?(this.saved=!0,this.close()):new ae.Notice("请同时提供"+this.name+"和图标")})),a)),i.addExtraButton(a=>(a.setIcon("cross").setTooltip("取消").onClick(()=>{this.saved=!1,this.close()}),a))})}onOpen(){return v(this,null,function*(){yield this.display()})}};var Oe=N(require("obsidian"));ke();var jt=class extends Oe.Modal{constructor(e){super(e.app);this.plugin=e}display(){return v(this,null,function*(){let{contentEl:e}=this;if(e.empty(),e.addClass("link-favicon-scrollable-content"),new Oe.Setting(e).setName("链接").addText(t=>{t.setValue(this.link).onChange(r=>{this.link=r}),t.inputEl.addEventListener("keydown",r=>{r.key==="Enter"&&this.display()})}),new Oe.Setting(e).setName("").addButton(t=>{t.setButtonText("测试").onClick(()=>{this.display()})}),this.link){this.link.startsWith("http")||(this.link="http://"+this.link);try{let t=new URL(this.link);for(let r of Object.values(L))e.createEl("h3",{text:r.name}),e.createEl("img",{cls:"provider-preview"}).setAttribute("src",yield r.url(t.hostname,this.plugin.settings))}catch(t){e.createSpan({text:"无法生成favicon，请检查设置"})}}})}onOpen(){return v(this,null,function*(){yield this.display()})}};var Mo=N(Lt()),jo={provider:"duckduckgo",fallbackProvider:"google",providerDomain:"",fallbackProviderDomain:"",ignored:"",overwritten:[],protocol:[],showAliased:!0,showLink:!0,enableReading:!0,enableSource:!0,enableLivePreview:!0,debounce:500,iconPosition:"front",colorInversion:!0},Ht=class extends y.PluginSettingTab{constructor(e,t){super(e,t);this.plugin=t}display(){let{containerEl:e}=this;if(e.empty(),new y.Setting(e).setName("图标提供者").addDropdown(t=>{for(let r in L)L.hasOwnProperty(r)&&t.addOption(r,L[r].name);t.setValue(this.plugin.settings.provider).onChange(r=>v(this,null,function*(){this.plugin.settings.provider=r,yield this.plugin.saveSettings(),this.display()}))}),Array.of("besticon").includes(this.plugin.settings.provider)&&new y.Setting(e).setName("提供者域名").setDesc("该提供者为自托管，请指定部署URL。请参考提供者的自托管说明文档。").addText(t=>t.setValue(this.plugin.settings.providerDomain).onChange(r=>v(this,null,function*(){this.plugin.settings.providerDomain=r,yield this.plugin.saveSettings()}))),new y.Setting(e).setName("备用图标提供者").addDropdown(t=>{for(let r in L)L.hasOwnProperty(r)&&t.addOption(r,L[r].name);t.setValue(this.plugin.settings.fallbackProvider).onChange(r=>v(this,null,function*(){this.plugin.settings.fallbackProvider=r,yield this.plugin.saveSettings(),this.display()}))}),Array.of("besticon").includes(this.plugin.settings.fallbackProvider)&&new y.Setting(e).setName("备用提供者域名").setDesc("该提供者可自托管，请指定部署URL。请参考提供者的自托管说明文档。").addText(t=>t.setValue(this.plugin.settings.fallbackProviderDomain).onChange(r=>v(this,null,function*(){this.plugin.settings.fallbackProviderDomain=r,yield this.plugin.saveSettings()}))),new y.Setting(e).setName("不确定选择哪个提供者？").addButton(t=>t.setButtonText("测试提供者").onClick(()=>{new jt(this.plugin).open()})),new y.Setting(e).setName("忽略的域名").setDesc("不要为这些域名显示favicon（每行一个）").addTextArea(t=>{t.setValue(this.plugin.settings.ignored).onChange(r=>v(this,null,function*(){this.plugin.settings.ignored=r,yield this.plugin.saveSettings()})),t.inputEl.setAttr("rows",8)}),e.createEl("h2",{text:"设计"}),new y.Setting(e).setName("当链接有别名时显示图标").setDesc("当链接格式如：[Obsidian](https://obsidian.md/)时").addToggle(t=>{t.setValue(this.plugin.settings.showAliased).onChange(r=>v(this,null,function*(){this.plugin.settings.showAliased=r,yield this.plugin.saveSettings()}))}),new y.Setting(e).setName("当链接没有别名时显示图标").setDesc("当链接格式如：https://obsidian.md/时").addToggle(t=>{t.setValue(this.plugin.settings.showLink).onChange(r=>v(this,null,function*(){this.plugin.settings.showLink=r,yield this.plugin.saveSettings()}))}),e.createEl("hr"),new y.Setting(e).setName("在阅读模式下显示").addToggle(t=>{t.setValue(this.plugin.settings.enableReading).onChange(r=>v(this,null,function*(){this.plugin.settings.enableReading=r,yield this.plugin.saveSettings()}))}),new y.Setting(e).setName("在源码模式下显示").addToggle(t=>{t.setValue(this.plugin.settings.enableSource).onChange(r=>v(this,null,function*(){this.plugin.settings.enableSource=r,yield this.plugin.saveSettings()}))}),new y.Setting(e).setName("在实时预览中显示").addToggle(t=>{t.setValue(this.plugin.settings.enableLivePreview).onChange(r=>v(this,null,function*(){this.plugin.settings.enableLivePreview=r,yield this.plugin.saveSettings()}))}),new y.Setting(e).setName("图标位置").addDropdown(t=>{t.addOption("front","链接前").addOption("back","链接后").setValue(this.plugin.settings.iconPosition).onChange(r=>v(this,null,function*(){this.plugin.settings.iconPosition=r,yield this.plugin.saveSettings()}))}),new y.Setting(e).setName("颜色反转").setDesc("如果检测到图标可读性较差，将自动反转favicon颜色").addToggle(t=>{t.setValue(this.plugin.settings.colorInversion).onChange(r=>v(this,null,function*(){this.plugin.settings.colorInversion=r,yield this.plugin.saveSettings()}))}),Qe(this.plugin)){let t=le(this.plugin);e.createEl("h2",{text:"自定义图标"}),e.createEl("h3",{text:"用于域名"}),new y.Setting(e).setName("添加新的").setDesc("添加自定义图标").addButton(l=>l.setTooltip("添加自定义图标").setIcon("plus-with-circle").onClick(()=>v(this,null,function*(){let p=new Ne(this.plugin);p.onClose=()=>v(this,null,function*(){p.saved&&(this.plugin.settings.overwritten.push({domain:p.domain,icon:p.icon}),yield this.plugin.saveSettings(),this.display())}),p.open()})));let n=e.createDiv("overwritten").createDiv("overwritten");for(let l of this.plugin.settings.overwritten){let p=new y.Setting(n),u=new DocumentFragment;u.createEl("p",{text:"		"+l.icon}).prepend(t.getIcon(l.icon)),p.setName(l.domain).setDesc(u).addExtraButton(f=>{f.setIcon("pencil").setTooltip("编辑").onClick(()=>{let m=new Ne(this.plugin,l);m.onClose=()=>v(this,null,function*(){if(m.saved){let h=this.plugin.settings.overwritten.filter(g=>g.domain!==m.domain);h.push({domain:m.domain,icon:m.icon}),this.plugin.settings.overwritten=h,yield this.plugin.saveSettings(),this.display()}}),m.open()})}).addExtraButton(f=>{f.setIcon("trash").setTooltip("删除").onClick(()=>v(this,null,function*(){this.plugin.settings.overwritten=this.plugin.settings.overwritten.filter(m=>l.domain!==m.domain),yield this.plugin.saveSettings(),this.display()}))})}e.createEl("h3",{text:"用于URI模式"}),new y.Setting(e).setName("添加新的").setDesc("添加自定义图标").addButton(l=>l.setTooltip("添加自定义图标").setIcon("plus-with-circle").onClick(()=>v(this,null,function*(){let p=new Ne(this.plugin,null,"URI Schema");p.onClose=()=>v(this,null,function*(){p.saved&&(this.plugin.settings.protocol.push({domain:p.domain,icon:p.icon}),yield this.plugin.saveSettings(),this.display())}),p.open()})));let i=e.createDiv("overwritten").createDiv("overwritten");for(let l of this.plugin.settings.protocol){let p=new y.Setting(i),u=new DocumentFragment;u.createEl("p",{text:"		"+l.icon}).prepend(t.getIcon(l.icon)),p.setName(l.domain).setDesc(u).addExtraButton(f=>{f.setIcon("pencil").setTooltip("编辑").onClick(()=>{let m=new Ne(this.plugin,l,"URI Schema");m.onClose=()=>v(this,null,function*(){if(m.saved){let h=this.plugin.settings.protocol.filter(g=>g.domain!==m.domain);h.push({domain:m.domain,icon:m.icon}),this.plugin.settings.protocol=h,yield this.plugin.saveSettings(),this.display()}}),m.open()})}).addExtraButton(f=>{f.setIcon("trash").setTooltip("删除").onClick(()=>v(this,null,function*(){this.plugin.settings.protocol=this.plugin.settings.protocol.filter(m=>m.domain!==l.domain),yield this.plugin.saveSettings(),this.display()}))})}let a=e.createEl("details");a.createEl("summary",{text:"高级"});let c=a.createDiv("advanced");new y.Setting(c).setName("去抖动").setDesc("编辑链接后多快显示图标（毫秒）？").addSlider(l=>{l.setLimits(1,2500,1).setDynamicTooltip().setValue(this.plugin.settings.debounce).onChange(p=>v(this,null,function*(){this.plugin.settings.debounce=p,yield this.plugin.saveSettings()}))})}if(localStorage.getItem("debug-plugin")==="1"){e.createEl("h1",{text:"调试工具"}),e.createEl("p",{text:"仅在知道操作时使用这些工具"});let t=e.createEl("details");t.createEl("summary",{text:"缓存的图标"});let r=t.createDiv("cached");Object.keys(localStorage).forEach(n=>{n.startsWith("lf-")&&(r.createEl("p",{text:n}),r.createEl("img",{attr:{src:Mo.default.get(n)}}))}),new y.Setting(e).setName("清除图标缓存").setDesc("从缓存中移除所有图标").addButton(n=>{n.setButtonText("清除").onClick(()=>{Object.keys(localStorage).forEach(s=>{s.startsWith("lf-")&&localStorage.removeItem(s)}),new y.Notice("缓存已清除"),this.display()})})}}};var Oo=N(require("obsidian"));ke();var Bt=class{constructor(e){this.processor=(e,t)=>v(this,null,function*(){if(!this.plugin.settings.enableReading||t.sourcePath.contains("no-favicon"))return;let r=L[this.plugin.settings.provider],n=L[this.plugin.settings.fallbackProvider];if(t.frontmatter){let i=L[t.frontmatter["favicon-provider"]],a=L[t.frontmatter["fallback-favicon-provider"]];i&&(r=i),a&&(n=a)}if(!r||!n){console.error("链接favicon: 配置错误的提供者"),new Oo.Notice("链接favicon: 配置错误的提供者，请检查设置");return}setTimeout(()=>v(this,null,function*(){var a;let i=e.querySelectorAll("a.external-link:not([data-favicon])");for(let c=0;c<i.length;c++){let l=i.item(c);if(l.dataset.disabled=String(this.isDisabled(l)),!this.isDisabled(l)){if((a=l.textContent)==null?void 0:a.includes("|nofavicon")){l.href=l.href.replace("%7Cnofavicon",""),l.ariaLabel=l.ariaLabel.replace("%7Cnofavicon",""),l.textContent=l.textContent.replace("|nofavicon","");continue}l.dataset.favicon="true";let p=yield this.plugin.getIcon(l.href,r),u=yield this.plugin.getIcon(l.href,n),f=this.plugin.iconAdder.constructURL(l.href);if(!f)return;try{yield this.plugin.iconAdder.addFavicon(l,p,u,f)}catch(m){console.error(m)}}}}),50)});this.isDisabled=e=>!!(e.getAttribute("data-no-favicon")||e.getAttribute("data-favicon")||!this.plugin.settings.showLink&&e.textContent===e.getAttribute("href")||!this.plugin.settings.showAliased&&e.textContent!==e.getAttribute("href"));this.plugin=e}};var Ho=N(require("@codemirror/view"));Vt();var lt=N(require("@codemirror/view")),Lo=N(require("obsidian"));var zt=class{constructor(e,t){this.decoCache=Object.create(null);this.editor=e,this.plugin=t,this.debouncedUpdate=(0,Lo.debounce)(this.updateAsyncDecorations,this.plugin.settings.debounce,!0)}computeAsyncDecorations(e){return v(this,null,function*(){let t=[];for(let r of e){let n=this.decoCache[r.value];n||(n=this.decoCache[r.value]=lt.Decoration.replace({})),t.push(n.range(r.from,r.to))}return lt.Decoration.set(t,!0)})}updateAsyncDecorations(e){return v(this,null,function*(){let t=yield this.computeAsyncDecorations(e);(t||this.editor.state.field(mt.field).size)&&this.editor.dispatch({effects:mt.update.of(t||lt.Decoration.none)})})}};var Bo=N(require("obsidian")),mt=pt();function pn(o){return Ho.ViewPlugin.fromClass(class{constructor(e){this.view=e;this.decoManager=new zt(e,o)}update(e){(e.docChanged||e.viewportChanged||e.selectionSet)&&this.buildAsyncDecorations(e.view)}buildAsyncDecorations(e){let t=[];if(!e.state.field(Bo.editorLivePreviewField)){this.decoManager.debouncedUpdate(t);return}for(let{from:r,to:n}of e.visibleRanges){let s=e.state.sliceDoc(r,n);for(let i of s.matchAll(/\|nofavicon/g)){let a=i.index;if(!a)continue;let c=a+i[0].length,l=!1;for(let p of e.state.selection.ranges)(p.from<=a&&p.to>=c||p.from>=a&&p.to<=c)&&(l=!0);l||t.push({from:a,to:c,value:""})}}this.decoManager.debouncedUpdate(t)}})}function Vo(o){return[mt.field,pn(o)]}var Xt=N(Lt()),dt=N(require("obsidian"));function ut(o){return ut=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ut(o)}var ln=/^\s+/,mn=/\s+$/;function d(o,e){if(o=o||"",e=e||{},o instanceof d)return o;if(!(this instanceof d))return new d(o,e);var t=un(o);this._originalInput=o,this._r=t.r,this._g=t.g,this._b=t.b,this._a=t.a,this._roundA=Math.round(100*this._a)/100,this._format=e.format||t.format,this._gradientType=e.gradientType,this._r<1&&(this._r=Math.round(this._r)),this._g<1&&(this._g=Math.round(this._g)),this._b<1&&(this._b=Math.round(this._b)),this._ok=t.ok}d.prototype={isDark:function(){return this.getBrightness()<128},isLight:function(){return!this.isDark()},isValid:function(){return this._ok},getOriginalInput:function(){return this._originalInput},getFormat:function(){return this._format},getAlpha:function(){return this._a},getBrightness:function(){var e=this.toRgb();return(e.r*299+e.g*587+e.b*114)/1e3},getLuminance:function(){var e=this.toRgb(),t,r,n,s,i,a;return t=e.r/255,r=e.g/255,n=e.b/255,t<=.03928?s=t/12.92:s=Math.pow((t+.055)/1.055,2.4),r<=.03928?i=r/12.92:i=Math.pow((r+.055)/1.055,2.4),n<=.03928?a=n/12.92:a=Math.pow((n+.055)/1.055,2.4),.2126*s+.7152*i+.0722*a},setAlpha:function(e){return this._a=Jo(e),this._roundA=Math.round(100*this._a)/100,this},toHsv:function(){var e=qo(this._r,this._g,this._b);return{h:e.h*360,s:e.s,v:e.v,a:this._a}},toHsvString:function(){var e=qo(this._r,this._g,this._b),t=Math.round(e.h*360),r=Math.round(e.s*100),n=Math.round(e.v*100);return this._a==1?"hsv("+t+", "+r+"%, "+n+"%)":"hsva("+t+", "+r+"%, "+n+"%, "+this._roundA+")"},toHsl:function(){var e=zo(this._r,this._g,this._b);return{h:e.h*360,s:e.s,l:e.l,a:this._a}},toHslString:function(){var e=zo(this._r,this._g,this._b),t=Math.round(e.h*360),r=Math.round(e.s*100),n=Math.round(e.l*100);return this._a==1?"hsl("+t+", "+r+"%, "+n+"%)":"hsla("+t+", "+r+"%, "+n+"%, "+this._roundA+")"},toHex:function(e){return Go(this._r,this._g,this._b,e)},toHexString:function(e){return"#"+this.toHex(e)},toHex8:function(e){return vn(this._r,this._g,this._b,this._a,e)},toHex8String:function(e){return"#"+this.toHex8(e)},toRgb:function(){return{r:Math.round(this._r),g:Math.round(this._g),b:Math.round(this._b),a:this._a}},toRgbString:function(){return this._a==1?"rgb("+Math.round(this._r)+", "+Math.round(this._g)+", "+Math.round(this._b)+")":"rgba("+Math.round(this._r)+", "+Math.round(this._g)+", "+Math.round(this._b)+", "+this._roundA+")"},toPercentageRgb:function(){return{r:Math.round(D(this._r,255)*100)+"%",g:Math.round(D(this._g,255)*100)+"%",b:Math.round(D(this._b,255)*100)+"%",a:this._a}},toPercentageRgbString:function(){return this._a==1?"rgb("+Math.round(D(this._r,255)*100)+"%, "+Math.round(D(this._g,255)*100)+"%, "+Math.round(D(this._b,255)*100)+"%)":"rgba("+Math.round(D(this._r,255)*100)+"%, "+Math.round(D(this._g,255)*100)+"%, "+Math.round(D(this._b,255)*100)+"%, "+this._roundA+")"},toName:function(){return this._a===0?"transparent":this._a<1?!1:xn[Go(this._r,this._g,this._b,!0)]||!1},toFilter:function(e){var t="#"+$o(this._r,this._g,this._b,this._a),r=t,n=this._gradientType?"GradientType = 1, ":"";if(e){var s=d(e);r="#"+$o(s._r,s._g,s._b,s._a)}return"progid:DXImageTransform.Microsoft.gradient("+n+"startColorstr="+t+",endColorstr="+r+")"},toString:function(e){var t=!!e;e=e||this._format;var r=!1,n=this._a<1&&this._a>=0,s=!t&&n&&(e==="hex"||e==="hex6"||e==="hex3"||e==="hex4"||e==="hex8"||e==="name");return s?e==="name"&&this._a===0?this.toName():this.toRgbString():(e==="rgb"&&(r=this.toRgbString()),e==="prgb"&&(r=this.toPercentageRgbString()),(e==="hex"||e==="hex6")&&(r=this.toHexString()),e==="hex3"&&(r=this.toHexString(!0)),e==="hex4"&&(r=this.toHex8String(!0)),e==="hex8"&&(r=this.toHex8String()),e==="name"&&(r=this.toName()),e==="hsl"&&(r=this.toHslString()),e==="hsv"&&(r=this.toHsvString()),r||this.toHexString())},clone:function(){return d(this.toString())},_applyModification:function(e,t){var r=e.apply(null,[this].concat([].slice.call(t)));return this._r=r._r,this._g=r._g,this._b=r._b,this.setAlpha(r._a),this},lighten:function(){return this._applyModification(wn,arguments)},brighten:function(){return this._applyModification(In,arguments)},darken:function(){return this._applyModification(bn,arguments)},desaturate:function(){return this._applyModification(gn,arguments)},saturate:function(){return this._applyModification(Sn,arguments)},greyscale:function(){return this._applyModification(Rn,arguments)},spin:function(){return this._applyModification(Un,arguments)},_applyCombination:function(e,t){return e.apply(null,[this].concat([].slice.call(t)))},analogous:function(){return this._applyCombination(yn,arguments)},complement:function(){return this._applyCombination(Pn,arguments)},monochromatic:function(){return this._applyCombination(Dn,arguments)},splitcomplement:function(){return this._applyCombination(Tn,arguments)},triad:function(){return this._applyCombination(Xo,[3])},tetrad:function(){return this._applyCombination(Xo,[4])}};d.fromRatio=function(o,e){if(ut(o)=="object"){var t={};for(var r in o)o.hasOwnProperty(r)&&(r==="a"?t[r]=o[r]:t[r]=He(o[r]));o=t}return d(o,e)};function un(o){var e={r:0,g:0,b:0},t=1,r=null,n=null,s=null,i=!1,a=!1;return typeof o=="string"&&(o=Cn(o)),ut(o)=="object"&&(ce(o.r)&&ce(o.g)&&ce(o.b)?(e=fn(o.r,o.g,o.b),i=!0,a=String(o.r).substr(-1)==="%"?"prgb":"rgb"):ce(o.h)&&ce(o.s)&&ce(o.v)?(r=He(o.s),n=He(o.v),e=dn(o.h,r,n),i=!0,a="hsv"):ce(o.h)&&ce(o.s)&&ce(o.l)&&(r=He(o.s),s=He(o.l),e=hn(o.h,r,s),i=!0,a="hsl"),o.hasOwnProperty("a")&&(t=o.a)),t=Jo(t),{ok:i,format:o.format||a,r:Math.min(255,Math.max(e.r,0)),g:Math.min(255,Math.max(e.g,0)),b:Math.min(255,Math.max(e.b,0)),a:t}}function fn(o,e,t){return{r:D(o,255)*255,g:D(e,255)*255,b:D(t,255)*255}}function zo(o,e,t){o=D(o,255),e=D(e,255),t=D(t,255);var r=Math.max(o,e,t),n=Math.min(o,e,t),s,i,a=(r+n)/2;if(r==n)s=i=0;else{var c=r-n;switch(i=a>.5?c/(2-r-n):c/(r+n),r){case o:s=(e-t)/c+(e<t?6:0);break;case e:s=(t-o)/c+2;break;case t:s=(o-e)/c+4;break}s/=6}return{h:s,s:i,l:a}}function hn(o,e,t){var r,n,s;o=D(o,360),e=D(e,100),t=D(t,100);function i(l,p,u){return u<0&&(u+=1),u>1&&(u-=1),u<1/6?l+(p-l)*6*u:u<1/2?p:u<2/3?l+(p-l)*(2/3-u)*6:l}if(e===0)r=n=s=t;else{var a=t<.5?t*(1+e):t+e-t*e,c=2*t-a;r=i(c,a,o+1/3),n=i(c,a,o),s=i(c,a,o-1/3)}return{r:r*255,g:n*255,b:s*255}}function qo(o,e,t){o=D(o,255),e=D(e,255),t=D(t,255);var r=Math.max(o,e,t),n=Math.min(o,e,t),s,i,a=r,c=r-n;if(i=r===0?0:c/r,r==n)s=0;else{switch(r){case o:s=(e-t)/c+(e<t?6:0);break;case e:s=(t-o)/c+2;break;case t:s=(o-e)/c+4;break}s/=6}return{h:s,s:i,v:a}}function dn(o,e,t){o=D(o,360)*6,e=D(e,100),t=D(t,100);var r=Math.floor(o),n=o-r,s=t*(1-e),i=t*(1-n*e),a=t*(1-(1-n)*e),c=r%6,l=[t,i,s,s,a,t][c],p=[a,t,t,i,s,s][c],u=[s,s,a,t,t,i][c];return{r:l*255,g:p*255,b:u*255}}function Go(o,e,t,r){var n=[ee(Math.round(o).toString(16)),ee(Math.round(e).toString(16)),ee(Math.round(t).toString(16))];return r&&n[0].charAt(0)==n[0].charAt(1)&&n[1].charAt(0)==n[1].charAt(1)&&n[2].charAt(0)==n[2].charAt(1)?n[0].charAt(0)+n[1].charAt(0)+n[2].charAt(0):n.join("")}function vn(o,e,t,r,n){var s=[ee(Math.round(o).toString(16)),ee(Math.round(e).toString(16)),ee(Math.round(t).toString(16)),ee(Zo(r))];return n&&s[0].charAt(0)==s[0].charAt(1)&&s[1].charAt(0)==s[1].charAt(1)&&s[2].charAt(0)==s[2].charAt(1)&&s[3].charAt(0)==s[3].charAt(1)?s[0].charAt(0)+s[1].charAt(0)+s[2].charAt(0)+s[3].charAt(0):s.join("")}function $o(o,e,t,r){var n=[ee(Zo(r)),ee(Math.round(o).toString(16)),ee(Math.round(e).toString(16)),ee(Math.round(t).toString(16))];return n.join("")}d.equals=function(o,e){return!o||!e?!1:d(o).toRgbString()==d(e).toRgbString()};d.random=function(){return d.fromRatio({r:Math.random(),g:Math.random(),b:Math.random()})};function gn(o,e){e=e===0?0:e||10;var t=d(o).toHsl();return t.s-=e/100,t.s=ft(t.s),d(t)}function Sn(o,e){e=e===0?0:e||10;var t=d(o).toHsl();return t.s+=e/100,t.s=ft(t.s),d(t)}function Rn(o){return d(o).desaturate(100)}function wn(o,e){e=e===0?0:e||10;var t=d(o).toHsl();return t.l+=e/100,t.l=ft(t.l),d(t)}function In(o,e){e=e===0?0:e||10;var t=d(o).toRgb();return t.r=Math.max(0,Math.min(255,t.r-Math.round(255*-(e/100)))),t.g=Math.max(0,Math.min(255,t.g-Math.round(255*-(e/100)))),t.b=Math.max(0,Math.min(255,t.b-Math.round(255*-(e/100)))),d(t)}function bn(o,e){e=e===0?0:e||10;var t=d(o).toHsl();return t.l-=e/100,t.l=ft(t.l),d(t)}function Un(o,e){var t=d(o).toHsl(),r=(t.h+e)%360;return t.h=r<0?360+r:r,d(t)}function Pn(o){var e=d(o).toHsl();return e.h=(e.h+180)%360,d(e)}function Xo(o,e){if(isNaN(e)||e<=0)throw new Error("Argument to polyad must be a positive number");for(var t=d(o).toHsl(),r=[d(o)],n=360/e,s=1;s<e;s++)r.push(d({h:(t.h+s*n)%360,s:t.s,l:t.l}));return r}function Tn(o){var e=d(o).toHsl(),t=e.h;return[d(o),d({h:(t+72)%360,s:e.s,l:e.l}),d({h:(t+216)%360,s:e.s,l:e.l})]}function yn(o,e,t){e=e||6,t=t||30;var r=d(o).toHsl(),n=360/t,s=[d(o)];for(r.h=(r.h-(n*e>>1)+720)%360;--e;)r.h=(r.h+n)%360,s.push(d(r));return s}function Dn(o,e){e=e||6;for(var t=d(o).toHsv(),r=t.h,n=t.s,s=t.v,i=[],a=1/e;e--;)i.push(d({h:r,s:n,v:s})),s=(s+a)%1;return i}d.mix=function(o,e,t){t=t===0?0:t||50;var r=d(o).toRgb(),n=d(e).toRgb(),s=t/100,i={r:(n.r-r.r)*s+r.r,g:(n.g-r.g)*s+r.g,b:(n.b-r.b)*s+r.b,a:(n.a-r.a)*s+r.a};return d(i)};d.readability=function(o,e){var t=d(o),r=d(e);return(Math.max(t.getLuminance(),r.getLuminance())+.05)/(Math.min(t.getLuminance(),r.getLuminance())+.05)};d.isReadable=function(o,e,t){var r=d.readability(o,e),n,s;switch(s=!1,n=Kn(t),n.level+n.size){case"AAsmall":case"AAAlarge":s=r>=4.5;break;case"AAlarge":s=r>=3;break;case"AAAsmall":s=r>=7;break}return s};d.mostReadable=function(o,e,t){var r=null,n=0,s,i,a,c;t=t||{},i=t.includeFallbackColors,a=t.level,c=t.size;for(var l=0;l<e.length;l++)s=d.readability(o,e[l]),s>n&&(n=s,r=d(e[l]));return d.isReadable(o,r,{level:a,size:c})||!i?r:(t.includeFallbackColors=!1,d.mostReadable(o,["#fff","#000"],t))};var qt=d.names={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"0ff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"00f",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",burntsienna:"ea7e5d",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"0ff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"f0f",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"},xn=d.hexNames=Nn(qt);function Nn(o){var e={};for(var t in o)o.hasOwnProperty(t)&&(e[o[t]]=t);return e}function Jo(o){return o=parseFloat(o),(isNaN(o)||o<0||o>1)&&(o=1),o}function D(o,e){_n(o)&&(o="100%");var t=Wn(o);return o=Math.min(e,Math.max(0,parseFloat(o))),t&&(o=parseInt(o*e,10)/100),Math.abs(o-e)<1e-6?1:o%e/parseFloat(e)}function ft(o){return Math.min(1,Math.max(0,o))}function G(o){return parseInt(o,16)}function _n(o){return typeof o=="string"&&o.indexOf(".")!=-1&&parseFloat(o)===1}function Wn(o){return typeof o=="string"&&o.indexOf("%")!=-1}function ee(o){return o.length==1?"0"+o:""+o}function He(o){return o<=1&&(o=o*100+"%"),o}function Zo(o){return Math.round(parseFloat(o)*255).toString(16)}function Yo(o){return G(o)/255}var te=function(){var o="[-\\+]?\\d+%?",e="[-\\+]?\\d*\\.\\d+%?",t="(?:"+e+")|(?:"+o+")",r="[\\s|\\(]+("+t+")[,|\\s]+("+t+")[,|\\s]+("+t+")\\s*\\)?",n="[\\s|\\(]+("+t+")[,|\\s]+("+t+")[,|\\s]+("+t+")[,|\\s]+("+t+")\\s*\\)?";return{CSS_UNIT:new RegExp(t),rgb:new RegExp("rgb"+r),rgba:new RegExp("rgba"+n),hsl:new RegExp("hsl"+r),hsla:new RegExp("hsla"+n),hsv:new RegExp("hsv"+r),hsva:new RegExp("hsva"+n),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/}}();function ce(o){return!!te.CSS_UNIT.exec(o)}function Cn(o){o=o.replace(ln,"").replace(mn,"").toLowerCase();var e=!1;if(qt[o])o=qt[o],e=!0;else if(o=="transparent")return{r:0,g:0,b:0,a:0,format:"name"};var t;return(t=te.rgb.exec(o))?{r:t[1],g:t[2],b:t[3]}:(t=te.rgba.exec(o))?{r:t[1],g:t[2],b:t[3],a:t[4]}:(t=te.hsl.exec(o))?{h:t[1],s:t[2],l:t[3]}:(t=te.hsla.exec(o))?{h:t[1],s:t[2],l:t[3],a:t[4]}:(t=te.hsv.exec(o))?{h:t[1],s:t[2],v:t[3]}:(t=te.hsva.exec(o))?{h:t[1],s:t[2],v:t[3],a:t[4]}:(t=te.hex8.exec(o))?{r:G(t[1]),g:G(t[2]),b:G(t[3]),a:Yo(t[4]),format:e?"name":"hex8"}:(t=te.hex6.exec(o))?{r:G(t[1]),g:G(t[2]),b:G(t[3]),format:e?"name":"hex"}:(t=te.hex4.exec(o))?{r:G(t[1]+""+t[1]),g:G(t[2]+""+t[2]),b:G(t[3]+""+t[3]),a:Yo(t[4]+""+t[4]),format:e?"name":"hex8"}:(t=te.hex3.exec(o))?{r:G(t[1]+""+t[1]),g:G(t[2]+""+t[2]),b:G(t[3]+""+t[3]),format:e?"name":"hex"}:!1}function Kn(o){var e,t;return o=o||{level:"AA",size:"small"},e=(o.level||"AA").toUpperCase(),t=(o.size||"small").toLowerCase(),e!=="AA"&&e!=="AAA"&&(e="AA"),t!=="small"&&t!=="large"&&(t="small"),{level:e,size:t}}function kn(o){var e=o.toString(16);return e.length===1?"0"+e:e}function Qo(o){return"#"+o.map(kn).join("")}function En(o){var e=(o[0]*299+o[1]*587+o[2]*114)/1e3;return e<128}function An(o){return o?Fn(o)?o:[o]:[]}function Fn(o){return Array.isArray(o[0])}function Gt(o,e,t){for(var r=0;r<t.length;r++)if(Mn(o,e,t[r]))return!0;return!1}function Mn(o,e,t){switch(t.length){case 3:if(jn(o,e,t))return!0;break;case 4:if(On(o,e,t))return!0;break;case 5:if(Ln(o,e,t))return!0;break;default:return!1}}function jn(o,e,t){return o[e+3]!==255||o[e]===t[0]&&o[e+1]===t[1]&&o[e+2]===t[2]}function On(o,e,t){return o[e+3]&&t[3]?o[e]===t[0]&&o[e+1]===t[1]&&o[e+2]===t[2]&&o[e+3]===t[3]:o[e+3]===t[3]}function ht(o,e,t){return o>=e-t&&o<=e+t}function Ln(o,e,t){var r=t[0],n=t[1],s=t[2],i=t[3],a=t[4],c=o[e+3],l=ht(c,i,a);return i?!!(!c&&l||ht(o[e],r,a)&&ht(o[e+1],n,a)&&ht(o[e+2],s,a)&&l):l}var Hn=24;function Bn(o,e,t){for(var r={},n=t.dominantDivider||Hn,s=t.ignoredColor,i=t.step,a=[0,0,0,0,0],c=0;c<e;c+=i){var l=o[c],p=o[c+1],u=o[c+2],f=o[c+3];if(!(s&&Gt(o,c,s))){var m=Math.round(l/n)+","+Math.round(p/n)+","+Math.round(u/n);r[m]?r[m]=[r[m][0]+l*f,r[m][1]+p*f,r[m][2]+u*f,r[m][3]+f,r[m][4]+1]:r[m]=[l*f,p*f,u*f,f,1],a[4]<r[m][4]&&(a=r[m])}}var h=a[0],g=a[1],S=a[2],R=a[3],b=a[4];return R?[Math.round(h/R),Math.round(g/R),Math.round(S/R),Math.round(R/b)]:t.defaultColor}function Vn(o,e,t){for(var r=0,n=0,s=0,i=0,a=0,c=t.ignoredColor,l=t.step,p=0;p<e;p+=l){var u=o[p+3],f=o[p]*u,m=o[p+1]*u,h=o[p+2]*u;c&&Gt(o,p,c)||(r+=f,n+=m,s+=h,i+=u,a++)}return i?[Math.round(r/i),Math.round(n/i),Math.round(s/i),Math.round(i/a)]:t.defaultColor}function zn(o,e,t){for(var r=0,n=0,s=0,i=0,a=0,c=t.ignoredColor,l=t.step,p=0;p<e;p+=l){var u=o[p],f=o[p+1],m=o[p+2],h=o[p+3];c&&Gt(o,p,c)||(r+=u*u*h,n+=f*f*h,s+=m*m*h,i+=h,a++)}return i?[Math.round(Math.sqrt(r/i)),Math.round(Math.sqrt(n/i)),Math.round(Math.sqrt(s/i)),Math.round(i/a)]:t.defaultColor}function er(o){return Be(o,"defaultColor",[0,0,0,0])}function Be(o,e,t){return o[e]===void 0?t:o[e]}var tr=10,$t=100;function qn(o){return o.search(/\.svg(\?|$)/i)!==-1}function Gn(o){if(rr(o)){var e=o.naturalWidth,t=o.naturalHeight;return!o.naturalWidth&&qn(o.src)&&(e=t=$t),{width:e,height:t}}return Xn(o)?{width:o.videoWidth,height:o.videoHeight}:{width:o.width,height:o.height}}function or(o){return Jn(o)?"canvas":$n(o)?"offscreencanvas":Zn(o)?"imagebitmap":o.src}function rr(o){return typeof HTMLImageElement!="undefined"&&o instanceof HTMLImageElement}var nr=typeof OffscreenCanvas!="undefined";function $n(o){return nr&&o instanceof OffscreenCanvas}function Xn(o){return typeof HTMLVideoElement!="undefined"&&o instanceof HTMLVideoElement}function Jn(o){return typeof HTMLCanvasElement!="undefined"&&o instanceof HTMLCanvasElement}function Zn(o){return typeof ImageBitmap!="undefined"&&o instanceof ImageBitmap}function Yn(o,e){var t=Be(e,"left",0),r=Be(e,"top",0),n=Be(e,"width",o.width),s=Be(e,"height",o.height),i=n,a=s;if(e.mode==="precision")return{srcLeft:t,srcTop:r,srcWidth:n,srcHeight:s,destWidth:i,destHeight:a};var c;return n>s?(c=n/s,i=$t,a=Math.round(i/c)):(c=s/n,a=$t,i=Math.round(a/c)),(i>n||a>s||i<tr||a<tr)&&(i=n,a=s),{srcLeft:t,srcTop:r,srcWidth:n,srcHeight:s,destWidth:i,destHeight:a}}var Qn=typeof window=="undefined";function es(){return Qn?nr?new OffscreenCanvas(1,1):null:document.createElement("canvas")}var ts="FastAverageColor: ";function re(o){return Error(ts+o)}function Ve(o,e){e||console.error(o)}var sr=function(){function o(){this.canvas=null,this.ctx=null}return o.prototype.getColorAsync=function(e,t){if(!e)return Promise.reject(re("call .getColorAsync() without resource"));if(typeof e=="string"){if(typeof Image=="undefined")return Promise.reject(re("resource as string is not supported in this environment"));var r=new Image;return r.crossOrigin=t&&t.crossOrigin||"",r.src=e,this.bindImageEvents(r,t)}else{if(rr(e)&&!e.complete)return this.bindImageEvents(e,t);var n=this.getColor(e,t);return n.error?Promise.reject(n.error):Promise.resolve(n)}},o.prototype.getColor=function(e,t){t=t||{};var r=er(t);if(!e){var n=re("call .getColor() without resource");return Ve(n,t.silent),this.prepareResult(r,n)}var s=Gn(e),i=Yn(s,t);if(!i.srcWidth||!i.srcHeight||!i.destWidth||!i.destHeight){var n=re('incorrect sizes for resource "'.concat(or(e),'"'));return Ve(n,t.silent),this.prepareResult(r,n)}if(!this.canvas&&(this.canvas=es(),!this.canvas)){var n=re("OffscreenCanvas is not supported in this browser");return Ve(n,t.silent),this.prepareResult(r,n)}if(!this.ctx){if(this.ctx=this.canvas.getContext("2d",{willReadFrequently:!0}),!this.ctx){var n=re("Canvas Context 2D is not supported in this browser");return Ve(n,t.silent),this.prepareResult(r)}this.ctx.imageSmoothingEnabled=!1}this.canvas.width=i.destWidth,this.canvas.height=i.destHeight;try{this.ctx.clearRect(0,0,i.destWidth,i.destHeight),this.ctx.drawImage(e,i.srcLeft,i.srcTop,i.srcWidth,i.srcHeight,0,0,i.destWidth,i.destHeight);var a=this.ctx.getImageData(0,0,i.destWidth,i.destHeight).data;return this.prepareResult(this.getColorFromArray4(a,t))}catch(c){var n=re("security error (CORS) for resource ".concat(or(e),`.
Details: https://developer.mozilla.org/en/docs/Web/HTML/CORS_enabled_image`));return Ve(n,t.silent),!t.silent&&console.error(c),this.prepareResult(r,n)}},o.prototype.getColorFromArray4=function(e,t){t=t||{};var r=4,n=e.length,s=er(t);if(n<r)return s;var i=n-n%r,a=(t.step||1)*r,c;switch(t.algorithm||"sqrt"){case"simple":c=Vn;break;case"sqrt":c=zn;break;case"dominant":c=Bn;break;default:throw re("".concat(t.algorithm," is unknown algorithm"))}return c(e,i,{defaultColor:s,ignoredColor:An(t.ignoredColor),step:a,dominantDivider:t.dominantDivider})},o.prototype.prepareResult=function(e,t){var r=e.slice(0,3),n=[e[0],e[1],e[2],e[3]/255],s=En(e);return{value:[e[0],e[1],e[2],e[3]],rgb:"rgb("+r.join(",")+")",rgba:"rgba("+n.join(",")+")",hex:Qo(r),hexa:Qo(e),isDark:s,isLight:!s,error:t}},o.prototype.destroy=function(){this.canvas&&(this.canvas.width=1,this.canvas.height=1,this.canvas=null),this.ctx=null},o.prototype.bindImageEvents=function(e,t){var r=this;return new Promise(function(n,s){var i=function(){l();var p=r.getColor(e,t);p.error?s(p.error):n(p)},a=function(){l(),s(re('Error loading image "'.concat(e.src,'"')))},c=function(){l(),s(re('Image "'.concat(e.src,'" loading aborted')))},l=function(){e.removeEventListener("load",i),e.removeEventListener("error",a),e.removeEventListener("abort",c)};e.addEventListener("load",i),e.addEventListener("error",a),e.addEventListener("abort",c)})},o}();var Jt=class{constructor(e){this.fac=new sr;this.plugin=e}destruct(){this.fac.destroy()}constructURL(e){try{return new URL(e)}catch(t){return e.startsWith("http")?void 0:this.constructURL("http://"+e)}}addFavicon(e,t,r,n){return v(this,null,function*(){if((!t||t==="")&&(!r||r==="")){console.log("没有图标："+n.href);return}if(!t||t===""){yield this.useDownloadedIcon(r,e,n);return}if(typeof t=="string"){if(!t.startsWith("http")){this.addIcon(e,t);return}let s=yield this.getImageEl(t,n);this.addIcon(e,s);return}this.addIcon(e,t)})}getImageEl(e,t){return v(this,null,function*(){if(typeof t=="string"){let r=this.constructURL(t);return r?this.getImageElFromUrl(e,r):Promise.reject("could not get Object for "+e+" "+t)}else return this.getImageElFromUrl(e,t)})}getImageElFromUrl(e,t){return v(this,null,function*(){let r=activeDocument.createElement("img");return r.addClass("link-favicon"),r.dataset.host=t.hostname,r.src=yield this.getEncodedIcon(e,t.hostname),yield this.setColorAttributes(r),r.style.height="0.8em",r.style.display="inline-block",r})}useDownloadedIcon(e,t,r){return v(this,null,function*(){if(!(!e||e==="")&&typeof e=="string"){let n=activeDocument.createElement("img");n.addClass("link-favicon"),n.src=yield this.getEncodedIcon(e,r.hostname),yield this.setColorAttributes(n),this.addIcon(t,n)}})}addIcon(e,t){!t||t==="undefined"||(this.plugin.settings.iconPosition==="front"&&e.prepend(t),this.plugin.settings.iconPosition==="back"&&e.append(t))}getEncodedIcon(e,t){return v(this,null,function*(){if(e==="")return"";let r=e.split("."),n=r[r.length-1];n||(n="png");let s="lf-"+t+"."+n,i=Xt.default.get(s);if(i)return i;let a=yield this.downloadIcon(e);return Xt.default.set(s,a,{ttl:30*24*60*60}),a})}downloadIcon(e){return v(this,null,function*(){let t=yield(0,dt.requestUrl)({url:e});return t.status!==200?Promise.reject("server returned status code"+t.status+" for "+e):"data:image/png;base64,"+(0,dt.arrayBufferToBase64)(t.arrayBuffer)})}setColorAttributes(e){return v(this,null,function*(){let t=activeDocument.getElementsByClassName("theme-dark")[0],r=activeDocument.getElementsByClassName("theme-light")[0],n;if(t!==void 0)try{n=activeWindow.getComputedStyle(t).getPropertyValue("--background-primary")}catch(s){n="000000"}else try{n=activeWindow.getComputedStyle(r).getPropertyValue("--background-primary")}catch(s){n="FFFFFF"}try{let s=yield this.fac.getColorAsync(e);e.dataset.averageColorHex=s.hex,e.dataset.isDark=String(s.isDark),e.dataset.isLight=String(s.isLight);let i=d(n);e.dataset.colorInversion=String(this.plugin.settings.colorInversion),e.dataset.readable=d.readability(s.hex,i).toString(),e.dataset.isReadableAA=String(d.isReadable(s.hex,i)),e.dataset.isReadableAAA=String(d.isReadable(s.hex,i,{level:"AAA"}))}catch(s){console.error("无法从图标中提取颜色信息"),console.error(e),console.error(s)}})}};var eo=class extends hr.Plugin{getOverwrittenFavicon(e){return v(this,null,function*(){let t=le(this);if(!t)return Promise.reject("No IconAPI loaded");if(e.length===0)return Promise.reject("No icons");let r=e[0].icon;if(t.version.satisfies("^0.9.0")){let s=yield t.getSVGIcon(r);return s||Promise.reject()}let n=yield t.getIcon(r);return n||Promise.reject()})}getCustomDomainIcon(e){return v(this,null,function*(){let t=this.settings.overwritten.filter(r=>e.match(r.domain));return this.getOverwrittenFavicon(t).then(r=>r).catch(r=>{})})}getCustomSchemeIcon(e){return v(this,null,function*(){let t=this.settings.protocol.filter(r=>e.substr(0,e.length-1).match(r.domain));return this.getOverwrittenFavicon(t).then(r=>r).catch(r=>{})})}getIcon(e,t){return v(this,null,function*(){let r;try{r=new URL(e)}catch(a){return Promise.reject()}let n=yield this.getCustomSchemeIcon(r.protocol);if(n)return typeof n!="string"&&(n.addClass("link-favicon"),n.dataset.target=r.href,n.dataset.protocol=r.protocol),n;if(this.settings.ignored.split(`
`).filter(a=>a.length>0).some(a=>r.hostname.match(new RegExp(a))))return Promise.reject();let i=yield this.getCustomDomainIcon(r.hostname);if(i)return typeof i!="string"&&(i.addClass("link-favicon"),i.dataset.target=r.href,i.dataset.host=r.hostname),i;try{return yield t.url(r.hostname,this.settings)}catch(a){return console.error(a),Promise.reject()}return""})}isUsingLivePreviewEnabledEditor(){return!app.vault.getConfig("legacyEditor")}onload(){return v(this,null,function*(){console.log("启用插件：链接favicon"),yield this.loadSettings(),this.iconAdder=new Jt(this);let e=this.app.vault.configDir+"/favicons/";if((yield this.app.vault.adapter.exists(e))&&(yield this.app.vault.adapter.rmdir(e,!0)),this.registerEvent(this.app.workspace.on("css-change",()=>{this.app.workspace.updateOptions()})),this.addSettingTab(new Ht(this.app,this)),this.isUsingLivePreviewEnabledEditor()){let r=(Qt(),fr).asyncDecoBuilderExt,n=require("@codemirror/state").Prec;this.registerEditorExtension(n.lowest(r(this))),this.registerEditorExtension(n.lowest(Vo(this)))}let t=new Bt(this);this.registerMarkdownPostProcessor(t.processor),this.app.workspace.updateOptions()})}onunload(){this.iconAdder.destruct(),console.log("禁用插件：链接favicon")}loadSettings(){return v(this,null,function*(){this.settings=Object.assign({},jo,yield this.loadData())})}saveSettings(){return v(this,null,function*(){yield this.saveData(this.settings),this.app.workspace.updateOptions()})}};
/*! Fast Average Color | © 2023 Denis Seleznev | MIT License | https://github.com/fast-average-color/fast-average-color */

/* nosourcemap */