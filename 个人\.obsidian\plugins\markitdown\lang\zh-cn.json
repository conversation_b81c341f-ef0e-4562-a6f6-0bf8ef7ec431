{"manifest": {"translationVersion": 1743604428148, "pluginVersion": "1.0.2"}, "description": {"original": "Convert PDFs, Office documents, images, and other file formats to Markdown using Microsoft's Markitdown tool", "translation": "Convert PDFs, Office documents, images, and other file formats to Markdown using Microsoft's Markitdown tool"}, "dict": {"Notice(\"Could not determine vault path. This plugin requires a local vault.\")": "Notice(\"Could not determine vault path. This plugin requires a local vault.\")", "Notice(\"Converting file...\")": "Notice(\"Converting file...\")", "Notice(`File converted and saved to ${l}`)": "Notice(`File converted and saved to ${l}`)", "Notice(`Error: ${n.message}`)": "Notice(`Error: ${n.message}`)", "Notice(\"Please select a file first\")": "Notice(\"Please select a file first\")", "Notice(\"Please select at least one file type\")": "Notice(\"Please select at least one file type\")", "Notice(\"No matching files found in the selected folder\")": "Notice(\"No matching files found in the selected folder\")", "Notice(`Converting ${m.length} files...`)": "Notice(`Converting ${m.length} files...`)", "Notice(`Conversion complete: ${y} successful, ${w} failed`)": "Notice(`Conversion complete: ${y} successful, ${w} failed`)", "Notice(`Error: ${g.message}`)": "Notice(`Error: ${g.message}`)", "Notice(\"Please select a folder first\")": "Notice(\"Please select a folder first\")", "Notice(\"Markitdown installed successfully!\")": "Notice(\"Markitdown installed successfully!\")", "Notice(\"Failed to install Markitdown. Please check the console for errors.\")": "Notice(\"Failed to install Markitdown. Please check the console for errors.\")", "Notice(`Error: ${a.message}`)": "Notice(`Error: ${a.message}`)", ".error(\"Failed to check Python installation\",t)": ".error(\"Failed to check Python installation\",t)", ".error(\"Failed to install Markitdown\",t)": ".error(\"Failed to install Markitdown\",t)", ".error(\"Error during conversion:\",n)": ".error(\"Error during conversion:\",n)", ".error(`Error converting ${d.name}:`,k)": ".error(`Error converting ${d.name}:`,k)", ".error(\"Error during folder conversion:\",g)": ".error(\"Error during folder conversion:\",g)", ".error(\"Error installing Markitdown:\",f)": ".error(\"Error installing Markitdown:\",f)", ".error(\"Error installing Markitdown:\",a)": ".error(\"Error installing Markitdown:\",a)", "name:\"Convert file to Markdown with Markitdown\"": "name:\"Convert file to <PERSON><PERSON> with Mark<PERSON><PERSON>\"", "name:\"Convert folder contents to Markdown with Markitdown\"": "name:\"Convert folder contents to <PERSON><PERSON> with Mark<PERSON><PERSON>\"", "name:\"PDF Files\"": "name:\"PDF Files\"", "name:\"Word Documents\"": "name:\"Word Documents\"", "name:\"PowerPoint Presentations\"": "name:\"PowerPoint Presentations\"", "name:\"Excel Spreadsheets\"": "name:\"Excel Spreadsheets\"", "name:\"Web Pages\"": "name:\"Web Pages\"", "name:\"Text Files\"": "name:\"Text Files\"", "name:\"Data Files\"": "name:\"Data Files\"", "name:\"Images\"": "name:\"Images\"", "name:\"Audio Files\"": "name:\"Audio Files\"", "name:\"Archives\"": "name:\"Archives\"", "text:\"Convert File to Markdown\"": "text:\"Convert File to Markdown\"", "text:\"Markitdown is not installed. Please install it in the settings tab.\"": "text:\"Markitdown is not installed. Please install it in the settings tab.\"", "text:\"Go to Settings\"": "text:\"Go to Settings\"", "text:\"Select a file to convert:\"": "text:\"Select a file to convert:\"", "text:\"Convert\"": "text:\"Convert\"", "text:\"Convert Folder Contents to Markdown\"": "text:\"Convert Folder Contents to Markdown\"", "text:\"Select a folder to process:\"": "text:\"Select a folder to process:\"", "text:\"Select file types to convert:\"": "text:\"Select file types to convert:\"", "text:\"Markitdown Setup\"": "text:\"Markitdown Setup\"", "text:\"Python is not installed or not found at the specified path. Please install Python and configure the path in settings.\"": "text:\"Python is not installed or not found at the specified path. Please install Python and configure the path in settings.\"", "text:\"Markitdown is not installed. Would you like to install it now?\"": "text:\"Markitdown is not installed. Would you like to install it now?\"", "text:\"This will install the Markitdown Python package using pip.\"": "text:\"This will install the Markitdown Python package using pip.\"", "text:\"Cancel\"": "text:\"Cancel\"", "text:\"Install Markitdown\"": "text:\"Install Markitdown\"", "text:\"Failed to install Markitdown. Please check the console for errors.\"": "text:\"Failed to install Markitdown. Please check the console for errors.\"", "text:`Error: ${f.message}`": "text:`Error: ${f.message}`", "text:\"Markitdown Settings\"": "text:\"<PERSON><PERSON><PERSON> Settings\"", "text:\"Status\"": "text:\"Status\"", ".setText(\"Installing...\")": ".setText(\"Installing...\")", ".setText(\"Try Again\")": ".setText(\"Try Again\")", ".setText(`Python: ${this.plugin.pythonInstalled?\"Installed\":\"Not installed\"}`)": ".setText(`Python: ${this.plugin.pythonInstalled?\"Installed\":\"Not installed\"}`)", ".setText(`Markitdown: ${this.plugin.markitdownInstalled?\"Installed\":\"Not installed\"}`)": ".setText(`Markitdown: ${this.plugin.markitdownInstalled?\"Installed\":\"Not installed\"}`)", ".setName(\"Python Path\")": ".setName(\"Python Path\")", ".setName(\"Enable Markitdown Plugins\")": ".setName(\"Enable Markitdown Plugins\")", ".setName(\"Azure Document Intelligence Endpoint\")": ".setName(\"Azure Document Intelligence Endpoint\")", ".setName(\"Output Folder\")": ".setName(\"Output Folder\")", ".setDesc(\"Path to Python executable (e.g., python, python3, or full path)\")": ".setDesc(\"Path to Python executable (e.g., python, python3, or full path)\")", ".setDesc(\"Enable third-party plugins for Markitdown\")": ".setDesc(\"Enable third-party plugins for Markitdown\")", ".setDesc(\"Optional: Use Azure Document Intelligence for better conversion (requires API key setup)\")": ".setDesc(\"Optional: Use Azure Document Intelligence for better conversion (requires API key setup)\")", ".setDesc('Folder path for converted files (relative to vault root, leave empty for default \"markitdown-output\")')": ".setDesc('Folder path for converted files (relative to vault root, leave empty for default \"markitdown-output\")')", ".setPlaceholder(\"python\")": ".setPlaceholder(\"python\")", ".setPlaceholder(\"https://your-resource.cognitiveservices.azure.com/\")": ".setPlaceholder(\"https://your-resource.cognitiveservices.azure.com/\")", ".setPlaceholder(\"markitdown-output\")": ".setPlaceholder(\"markitdown-output\")"}}