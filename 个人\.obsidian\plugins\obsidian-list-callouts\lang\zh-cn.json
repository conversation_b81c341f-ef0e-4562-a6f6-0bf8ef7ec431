{"manifest": {"translationVersion": -639075600000, "pluginVersion": "1.2.8"}, "description": {"original": "Create simple callouts in lists.", "translation": "在列表中创建简单标注。"}, "dict": {"text: \" Sed eu nisl rhoncus, consectetur mi quis, scelerisque enim.\"": "text: \" 示例list callouts.\"", "text: \"Note: Using +, *, -, >, or # as the callout character can disrupt reading mode.\"": "text: \"注意：使用+、*、-、>或#作为标注字符可能会中断阅读模式。\"", "setText(\"Create a new Callout\"": "setText(\"创建一个新的Callout\"", "setText(\"Create additional list callout styles.\"": "setText(\"创建其他列表callout样式。\"", "setButtonText(\"Set Icon\"": "setButtonText(\"设置图标\"", "setButtonText(\"Delete\"": "setButtonText(\"删除\"", "setButtonText(\"Create\"": "setButtonText(\"新建\"", "appendText(\"See the Style Settings plugin for additional configuration options.\"": "appendText(\"有关其他配置选项，请参阅Style Settings插件。(Fendi 2024-09-24 汉化)\"", ".setButtonText(\"Clear Icon\")": ".setButtonText(\"清除图标\")", "text:\"Note: Using +, *, -, >, or # as the callout character can disrupt reading mode.\"": "text:\"注意: 使用 +, *, -, >, 或 # 字符会扰乱阅读模式。\"", "text:\" Sed eu nisl rhoncus, consectetur mi quis, scelerisque enim.\"": "text:\" 列表示例：读好书，不求甚解。    ----陶渊明\""}}