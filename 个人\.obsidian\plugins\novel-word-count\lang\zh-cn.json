{"manifest": {"translationVersion": 1734531760348, "pluginVersion": "4.2.0"}, "description": {"original": "Displays a word count (and more!) for each file, folder and vault in the File Explorer pane.", "translation": "在文件资源管理器中，显示每个文件、文件夹和库的字数（以及更多统计信息！）"}, "dict": {".error(`Unable to parse canvas file [${file.name}]: ${ex}`)": ".error(`无法解析画布文件 [${file.name}]: ${ex}`)", "name: \"1st data type to show\"": "name: \"显示的第一个数据类型\"", "name: \"2nd data type to show\"": "name: \"显示的第二个数据类型\"", "name: \"3rd data type to show\"": "name: \"显示的第三个数据类型\"", "name: \"Show next data type (1st position)\"": "name: \"显示下一个数据类型（第一个位置）\"", "name: \"Toggle abbreviation on Notes\"": "name: \"切换笔记的缩写\"", "name: `Show ${COUNT_TYPE_DISPLAY_STRINGS[countType]} (1st position)`": "name: `显示 ${COUNT_TYPE_DISPLAY_STRINGS[countType]} （第一个位置）`", "text: \"Notes\"": "text: \"笔记\"", "text: \"You can display up to three data types side by side.\"": "text: \"您可以并排显示最多三种数据类型。\"", "text: \"Enjoying this plugin? Want more features?\"": "text: \"喜欢这个插件吗？想要更多功能？\"", ".setButtonText(\"Done\")": ".setButtonText(\"完成\")", ".setName(\"Abbreviate descriptions\")": ".setName(\"缩写描述\")", ".setName(\"Alignment\")": ".setName(\"对齐方式\")", ".setName(\"Folders: Same data as Notes\")": ".setName(\"文件夹数据：是否与笔记数据相同\")", ".setName(\"Show advanced options\")": ".setName(\"显示高级选项\")", ".setName(\"Include file/folder names\")": ".setName(\"包括文件/文件夹名称\")", ".setName(\"Exclude comments\")": ".setName(\"排除注释\")", ".setName(\"Exclude code blocks\")": ".setName(\"排除代码块\")", ".setName(\"Exclude non-visible portions of links\")": ".setName(\"排除链接中不可见的部分\")", ".setName(\"Exclude footnotes\")": ".setName(\"排除脚注\")", ".setName(\"Character count method\")": ".setName(\"字符计数方法\")", ".setName(\"Page count method\")": ".setName(\"页数计算方法\")", ".setName(\"Words per minute\")": ".setName(\"字数/分钟\")", ".setName(\"Words per page\")": ".setName(\"每页字数\")", ".setName(\"Characters per page\")": ".setName(\"每页字符数\")", ".setName(\"Debug mode\")": ".setName(\"调试模式\")", ".setDesc(\"Use advanced formatting\")": ".setDesc(\"使用高级格式\")", ".setDesc(\"E.g. show '120w' instead of '120 words'\")": ".setDesc(\"例如，显示 ‘120w’，而不是 ‘120 个单词’\")", ".setDesc(\"Language compatibility and fine-tuning\")": ".setDesc(\"语言兼容性和微调\")", ".setDesc(\n        \"Exclude %%Obsidian%% and <!--HTML--> comments from counts. May affect performance on large vaults.\"\n      )": ".setDesc(\n        \"排除 %%Obsidian%% 和 <!--HTML--> 注释的统计。这可能会影响大型库的性能。\"      )", ".setDesc(\n        \"Exclude ```code blocks``` (e.g. DataView snippets) from all counts. May affect performance on large vaults.\"\n      )": ".setDesc(\n        \"排除 ```代码块```（如 DataView 片段）的所有统计。这可能会影响大型库的性能。\"      )", ".setDesc(\n        \"Exclude footnotes[^1] from counts. May affect performance on large vaults.\"\n      )": ".setDesc(\n        \"排除脚注[^1]的统计。这可能会影响大型库的性能。\"      )", ".setDesc(\"For language compatibility\")": ".setDesc(\"为语言兼容性\")", ".setDesc(\n        \"Used to calculate Reading Time. 265 is the average speed of an English-speaking adult.\"\n      )": ".setDesc(\n        \"用于计算阅读时间。265 是英语使用者的平均阅读速度。\"      )", ".setDesc(\n        \"Used to calculate Reading Time. 500 is the average speed for CJK texts.\"\n      )": ".setDesc(\n        \"用于计算阅读时间。500 是中日韩文本的平均阅读速度。\"      )", ".setDesc(\n          \"Used for page count. 300 is standard in English language publishing.\"\n        )": ".setDesc(\n          \"用于页数计算。300 是英语出版业的标准。\"        )", ".setDesc(\"Log debugging information to the developer console\")": ".setDesc(\"将调试信息记录到开发者控制台\")", "alt='Buy Me a Coffee at ko-fi.com'": "alt='请给我买杯咖啡（赞助）at ko-fi.com'", ".setName(\"Data type separator\")": ".setName(\"据类型分隔符\")", ".setName(\"Root: Same data as Notes\")": ".setName(\"根节点数据：是否与笔记数据相同\")", ".setName(\"CJK characters per minute\")": ".setName(\"中日韩字符数（CJK）/分钟\")", ".setName(\"Include whitespace characters in page count\")": ".setName(\"将空格字符计入页数\")", ".setDesc(\n      \"Show data inline with file/folder names, right-aligned, or underneath\"\n    )": ".setDesc(\n      \"将数据与文件/文件夹名称并列显示，或右对齐显示，或显示在下方\"    )", ".setDesc(\n        \"Only count paths matching the indicated term(s). Case-sensitive, comma-separated. Defaults to all files. Any term starting with ! will be excluded instead of included.\"\n      )": ".setDesc(\n        \"仅统计符合指示术语的路径。区分大小写，以逗号分隔。默认统计所有文件。以英文 ! 开头的术语将被排除\"      )", ".setDesc(\n        \"For external links, exclude the URI from all counts. For internal links with aliases, only count the alias. May affect performance on large vaults.\"\n      )": ".setDesc(\n        \"对于外部链接，排除所有 URI 的统计。对于带别名的内部链接，只统计别名。这可能会影响大型库的性能。\"      )", ".setDesc(\n          `Used for page count. ${this.plugin.settings.charsPerPageIncludesWhitespace ? \"2400 is common in Danish.\" : \"1500 is common in German (Normseite).\"}`\n        )": ".setDesc(\n          `用于页数计算。${this.plugin.settings.charsPerPageIncludesWhitespace ?  \"2400 是丹麦常用的标准。\" : \"1500 是德语 (Normseite) 常用的标准。\"}`        )", ".setDesc(\n        `[${COUNT_TYPE_DISPLAY_STRINGS[config.countType]}] can't be formatted.`\n      )": ".setDesc(\n        `无法格式化 [${COUNT_TYPE_DISPLAY_STRINGS[config.countType]}] `\n      )", ".setDesc(\n        `[${COUNT_TYPE_DISPLAY_STRINGS[config.countType]}] Custom suffix`\n      )": ".setDesc(\n        ` 自定义后缀 [${COUNT_TYPE_DISPLAY_STRINGS[config.countType]}] `\n      )", ".setDesc(\n      `[${COUNT_TYPE_DISPLAY_STRINGS[\"frontmatterKey\" /* FrontmatterKey */]}] Key name`\n    )": ".setDesc(\n      `属性名称 [${COUNT_TYPE_DISPLAY_STRINGS[\"frontmatterKey\" /* FrontmatterKey */]}] `\n    )", "      drop.addOption(\"inline\" /* Inline */, \"Inline\").addOption(\"right\" /* Right */, \"Right-aligned\").addOption(\"below\" /* Below */, \"Below\").setValue(this.plugin.settings.alignment).onChange(async (value) => {": "      drop.addOption(\"inline\" /* Inline */, \"并列\").addOption(\"right\" /* Right */, \"右对齐\").addOption(\"below\" /* Below */, \"下方\").setValue(this.plugin.settings.alignment).onChange(async (value) => {", "name: \"Recount all notes / Reset session\"": "name: \"重新计算所有笔记/重置会话\"", ".setButtonText(\"Recount\")": ".setButtonText(\"重新计算\")", ".setName(\"Date format\")": ".setName(\"日期格式\")", ".setName(\"Recount all documents\")": ".setName(\"重新分析所有文档\")", ".setDesc(\"MomentJS date format to use for date strings\")": ".setDesc(\"用于日期字符串的 MomentJS 日期格式\")", ".setDesc(\n      \"If changes have occurred outside of Obsidian, you may need to trigger a manual recount\"\n    )": ".setDesc(\n      \"如果在 Obsidian 之外发生了更改，您可能需要进行手动分析\"    )", ".setDesc(\"[Track Session] Session count type\")": ".setDesc(\"[跟踪会话] 会话计数类型\")", ".setPlaceholder(\"\").setValue(this.plugin.settings.includeDirectories).onChange((0, import_obsidian4.debounce)(includePathsChanged.bind(this, txt), 1e3));\n      });\n      new import_obsidian4.Setting(containerEl).setName(\"Exclude comments\")": ".setPlaceholder(\"\").setValue(this.plugin.settings.includeDirectories).onChange((0, import_obsidian4.debounce)(includePathsChanged.bind(this, txt), 1e3));\n      });\n      new import_obsidian4.Setting(containerEl).setName(\"排除注释\")", "        drop.addOption(\"inline\" /* Inline */, \"Inline\").addOption(\"right\" /* Right */, \"Right-aligned\").addOption(\"below\" /* Below */, \"Below\").setValue(this.plugin.settings.folderAlignment).onChange(async (value) => {": "        drop.addOption(\"inline\" /* Inline */, \"并列\").addOption(\"right\" /* Right */, \"右对齐\").addOption(\"below\" /* Below */, \"下方\").setValue(this.plugin.settings.folderAlignment).onChange(async (value) => {", "        drop.addOption(\"AllCharacters\" /* StringLength */, \"All characters\").addOption(": "        drop.addOption(\"AllCharacters\" /* StringLength */, \"所有字符\").addOption(", "        drop.addOption(\"ByWords\" /* ByWords */, \"Words per page\").addOption(\"ByChars\" /* ByChars */, \"Characters per page\").setValue(this.plugin.settings.pageCountType).onChange(async (value) => {": "        drop.addOption(\"ByWords\" /* ByWords */, \"每页字数\").addOption(\"ByChars\" /* ByChars */, \"每页字符数\").setValue(this.plugin.settings.pageCountType).onChange(async (value) => {", "  [\"none\" /* None */]: \"None\",": "  [\"none\" /* None */]: \"无\",", "  [\"word\" /* Word */]: \"Word Count\",": "  [\"word\" /* Word */]: \"字数统计\",", "  [\"page\" /* Page */]: \"Page Count\",": "  [\"page\" /* Page */]: \"页数统计\",", "  [\"pagedecimal\" /* PageDecimal */]: \"Page Count (decimal)\",": "  [\"pagedecimal\" /* PageDecimal */]: \"页数统计（小数）\",", "  [\"readtime\" /* ReadTime */]: \"Reading Time\",": "  [\"readtime\" /* ReadTime */]: \"阅读时间\",", "  [\"percentgoal\" /* PercentGoal */]: \"% of Word Goal\",": "  [\"percentgoal\" /* PercentGoal */]: \"占目标字数的百分比\",", "  [\"note\" /* Note */]: \"Note Count\",": "  [\"note\" /* Note */]: \"笔记数量统计\",", "  [\"character\" /* Character */]: \"Character Count\",": "  [\"character\" /* Character */]: \"字符统计\",", "  [\"link\" /* Link */]: \"Link Count\",": "  [\"link\" /* Link */]: \"链接统计\",", "  [\"embed\" /* Embed */]: \"Embed Count\",": "  [\"embed\" /* Embed */]: \"嵌入统计\",", "  [\"alias\" /* Alias */]: \"First Alias\",": "  [\"alias\" /* <PERSON><PERSON> */]: \"首个别名\",", "  [\"created\" /* Created */]: \"Created Date\",": "  [\"created\" /* Created */]: \"创建日期\",", "  [\"modified\" /* Modified */]: \"Last Updated Date\",": "  [\"modified\" /* Modified */]: \"最后更新日期\",", "  [\"filesize\" /* FileSize */]: \"File Size\",": "  [\"filesize\" /* FileSize */]: \"文件大小\",", "  [\"frontmatterKey\" /* FrontmatterKey */]: \"Frontmatter Key\",": "  [\"frontmatterKey\" /* FrontmatterKey */]: \"笔记属性\",", "  [\"tracksession\" /* TrackSession */]: \"Track Session\"": "  [\"tracksession\" /* TrackSession */]: \"跟踪会话\"", "  [\"none\" /* None */]: \"Hidden.\",": "  [\"none\" /* None */]: \"隐藏\",", "  [\"word\" /* Word */]: \"Total words.\",": "  [\"word\" /* Word */]: \"总字数\",", "  [\"page\" /* Page */]: \"Total pages, rounded up.\",": "  [\"page\" /* Page */]: \"页数，四舍五入到整数\",", "  [\"pagedecimal\" /* PageDecimal */]: \"Total pages, precise to 2 digits after the decimal.\",": "  [\"pagedecimal\" /* PageDecimal */]: \"总页数，精确到小数点后两位\",", "  [\"readtime\" /* ReadTime */]: \"Estimated time to read the note.\",": "  [\"readtime\" /* ReadTime */]: \"E阅读笔记的预计时间\",", "  [\"percentgoal\" /* PercentGoal */]: \"Set a word goal by adding the 'word-goal' property to a note.\",": "  [\"percentgoal\" /* PercentGoal */]: \"通过在笔记中添加 'word-goal' 属性来设置字数目标\",", "  [\"note\" /* Note */]: \"Total notes.\",": "  [\"note\" /* Note */]: \"总笔记数\",", "  [\"character\" /* Character */]: \"Total characters (letters, symbols, numbers, and spaces).\",": "  [\"character\" /* Character */]: \"字符总数（包含字母、符号、数字和空格）.\",", "  [\"link\" /* Link */]: \"Total links to other notes.\",": "  [\"link\" /* Link */]: \"与其他笔记的链接总数\",", "  [\"embed\" /* Embed */]: \"Total embedded images, files, and notes.\",": "  [\"embed\" /* Embed */]: \"嵌入图片、文件和笔记总数\",", "  [\"alias\" /* Alias */]: \"The first alias property of each note.\",": "  [\"alias\" /* <PERSON><PERSON> */]: \"每个笔记的第一个别名属性\",", "  [\"created\" /* Created */]: \"Creation date. (On folders: earliest creation date of any note.)\",": "  [\"created\" /* Created */]: \"创建日期（文件夹数据中显示为某一笔记的最早创建日期）\",", "  [\"modified\" /* Modified */]: \"Date of last edit. (On folders: latest edit date of any note.)\",": "  [\"modified\" /* Modified */]: \"最后编辑日期（文件夹数据中显示为某一笔记的最新编辑日期）)\",", "  [\"filesize\" /* FileSize */]: \"Total size on hard drive.\",": "  [\"filesize\" /* FileSize */]: \"硬盘占用大小\",", "  [\"frontmatterKey\" /* FrontmatterKey */]: \"Key in the frontmatter block.\",": "  [\"frontmatterKey\" /* FrontmatterKey */]: \"属性区域的元数据\",", "  [\"tracksession\" /* TrackSession */]: \"Track progress since last Obsidian startup, plugin init, settings change, or recount\"": "  [\"tracksession\" /* TrackSession */]: \"跟踪自上次 Obsidian 启动、插件启动、设置更改或重新计数以来的进度\"", "          \"Exclude whitespace\"": "          \"排除空格\""}}