{"packagesHash": {"default": {"packageId": "default", "name": "Default Prompts Package", "version": "0.0.9", "minTextGeneratorVersion": "0.5.0", "description": "This is the main package that comes with Text Generator plugin in Obsidian", "author": "<PERSON><PERSON><PERSON>", "tags": "writing, brainstorming", "authorUrl": "https://www.buymeacoffee.com/haouarine", "repo": "text-gen/gpt-3-prompt-templates"}, "dalle": {"packageId": "dalle", "name": "OpenAI Dalle Package", "version": "0.1.1", "minTextGeneratorVersion": "0.7.0", "description": "The package contains some interessting Dalle-2/Dalle-3 prompt templates", "author": "<PERSON><PERSON><PERSON>", "tags": "photo, dalle-2, dalle-3", "authorUrl": "https://www.buymeacoffee.com/haouarine", "repo": "text-gen/tg-dalle-package"}, "huggingface": {"packageId": "huggingface", "name": "Huggingface Prompts Package", "version": "0.0.4", "minTextGeneratorVersion": "0.5.0", "description": "Huggingface Prompts comes with Text Generator plugin in Obsidian", "author": "<PERSON><PERSON><PERSON>", "tags": "writing, brainstorming, huggingface", "authorUrl": "https://www.buymeacoffee.com/haouarine", "repo": "text-gen/huggingface"}, "awesomePrompts": {"packageId": "awesomePrompts", "name": "Awesome Prompts", "version": "0.0.3", "minTextGeneratorVersion": "0.5.7", "description": "This repo includes ChatGPT prompt curation to use ChatGPT better.", "author": "f", "tags": "writing, brainstorming, awesome", "authorUrl": "https://github.com/f/awesome-chatgpt-prompts", "repo": "text-gen/awesome-tg-package"}, "tts": {"packageId": "tts", "name": "Text To Speech Package", "version": "0.0.3", "minTextGeneratorVersion": "0.6.0", "description": "Contains Text To Speech Templates and support for TTS for other templates", "author": "Noureddine", "tags": "TTS, Speak", "authorUrl": "https://www.buymeacoffee.com/haouarine", "repo": "text-gen/TTS"}, "vision": {"packageId": "vision", "name": "Vision Package", "version": "0.0.2", "minTextGeneratorVersion": "0.6.0", "description": "Contains Vision Templates and support for Vision for other templates (Scripts)", "author": "Noureddine", "tags": "OpenAI,markdown,gpt-4-vision,vision,images", "authorUrl": "https://www.buymeacoffee.com/haouarine", "repo": "text-gen/tg-vision"}, "smartConnections": {"packageId": "smartConnections", "name": "Smart Connections Package", "version": "0.0.2", "minTextGeneratorVersion": "0.6.0", "description": "Contains Smart Connection Templates and support for Smart Connections for other templates (Script)", "author": "Noureddine", "tags": "smartConnections,smart-connections", "authorUrl": "https://www.buymeacoffee.com/haouarine", "repo": "text-gen/tg-smartConnections"}, "Experimental": {"packageId": "Experimental", "name": "Experimental Package", "version": "0.0.2", "minTextGeneratorVersion": "0.6.6", "description": "Contains experimental templates", "author": "Noureddine", "tags": "experiments", "authorUrl": "https://www.buymeacoffee.com/haouarine", "repo": "text-gen/experimental-package"}, "testExtension": {"core": true, "type": "feature", "packageId": "testExtension", "name": "test extension Package", "version": "0.0.1", "minTextGeneratorVersion": "0.1.0", "description": "testing extension package", "author": "<PERSON><PERSON><PERSON>", "tags": "testing", "authorUrl": "https://www.buymeacoffee.com/haouarine", "repo": "text-gen/gpt-3-prompt-templates", "folderName": "testExtension", "price": 20, "installed": false}, "excalidraw": {"packageId": "excalidraw", "name": "excalidraw package", "version": "0.0.1", "minTextGeneratorVersion": "0.6.0", "description": "Contains Excalidraw Templates and support for Excalidraw for other templates (Scripts)", "author": "Noureddine", "tags": "OpenAI,markdown,gpt-4-vision,vision,images", "authorUrl": "https://www.buymeacoffee.com/haouarine", "repo": "text-gen/tg-excalidraw", "core": true, "folderName": "excalidrawPackage", "price": 2}}, "resources": {}, "installedPackagesHash": {"smartConnections": {"packageId": "smartConnections", "prompts": [{"promptId": "summarize"}], "installedPrompts": [{"promptId": "summarize", "version": ""}], "version": "0.0.2"}, "default": {"packageId": "default", "prompts": [{"promptId": "getEmailNeg"}, {"promptId": "getEmailPos"}, {"promptId": "getIdeas"}, {"promptId": "getOutline"}, {"promptId": "getParagraph"}, {"promptId": "getTags"}, {"promptId": "get<PERSON>itles"}, {"promptId": "rewrite"}, {"promptId": "simplify"}, {"promptId": "summarize"}, {"promptId": "summarizeLarge"}], "installedPrompts": [{"promptId": "get<PERSON>itles", "version": ""}, {"promptId": "summarize", "version": ""}, {"promptId": "getOutline", "version": ""}, {"promptId": "simplify", "version": ""}, {"promptId": "getTags", "version": ""}, {"promptId": "summarizeLarge", "version": ""}, {"promptId": "getIdeas", "version": ""}, {"promptId": "rewrite", "version": ""}, {"promptId": "getParagraph", "version": ""}, {"promptId": "getEmailNeg", "version": ""}, {"promptId": "getEmailPos", "version": ""}], "version": "0.0.5"}, "awesomePrompts": {"packageId": "awesomePrompts", "prompts": [{"promptId": "academicianA<PERSON><PERSON>"}, {"promptId": "accountantAwesome"}, {"promptId": "advertiserAwesome"}, {"promptId": "aiassisteddoctorAwesome"}, {"promptId": "aitryingtoescapetheboxAwesome"}, {"promptId": "aiwritingtutorAwesome"}, {"promptId": "aphorismbookAwesome"}, {"promptId": "artistadvisorAwesome"}, {"promptId": "asciiartistAwesome"}, {"promptId": "astrologerAwesome"}, {"promptId": "automobilemechanicAwesome"}, {"promptId": "babysitterAwesome"}, {"promptId": "biblicaltranslatorAwesome"}, {"promptId": "buddha<PERSON><PERSON><PERSON>"}, {"promptId": "careercounselorAwesome"}, {"promptId": "carnavigationsystemAwesome"}, {"promptId": "characterfrommoviebookanythingAwesome"}, {"promptId": "chatgptpromptgeneratorAwesome"}, {"promptId": "cheaptravelticketadvisorAwesome"}, {"promptId": "chefAwesome"}, {"promptId": "chemicalreactorAwesome"}, {"promptId": "chessplayerAwesome"}, {"promptId": "chiefexecutiveofficerAwesome"}, {"promptId": "classicalmusiccomposerAwesome"}, {"promptId": "commentariatAwesome"}, {"promptId": "commitmessagegeneratorAwesome"}, {"promptId": "composer<PERSON><PERSON><PERSON>"}, {"promptId": "coverletterAwesome"}, {"promptId": "cybersecurityspecialistAwesome"}, {"promptId": "datascientistAwesome"}, {"promptId": "debatecoachAwesome"}, {"promptId": "debater<PERSON><PERSON><PERSON>"}, {"promptId": "dentistAwesome"}, {"promptId": "developerrelationsconsultantAwesome"}, {"promptId": "diagramgeneratorAwesome"}, {"promptId": "dietitianAwesome"}, {"promptId": "digitalartgalleryguideAwesome"}, {"promptId": "diyexpertAwesome"}, {"promptId": "doctor<PERSON><PERSON><PERSON>"}, {"promptId": "dreaminterpreterAwesome"}, {"promptId": "drunkpersonAwesome"}, {"promptId": "educationalcontentcreatorAwesome"}, {"promptId": "elocutionistAwesome"}, {"promptId": "emergencyresponseprofessionalAwesome"}, {"promptId": "emojitranslatorAwesome"}, {"promptId": "englishpronunciationhelperAwesome"}, {"promptId": "englishtranslatorandimproverAwesome"}, {"promptId": "essaywriter<PERSON><PERSON><PERSON>"}, {"promptId": "etymologist<PERSON><PERSON><PERSON>"}, {"promptId": "excelsheetAwesome"}, {"promptId": "fallacyfinderAwesome"}, {"promptId": "fancytitlegeneratorAwesome"}, {"promptId": "fillintheblankworksheetsgeneratorAwesome"}, {"promptId": "filmcriticAwesome"}, {"promptId": "financialanalystAwesome"}, {"promptId": "floristAwesome"}, {"promptId": "foodcriticAwesome"}, {"promptId": "footballcommentatorAwesome"}, {"promptId": "friendAwesome"}, {"promptId": "fullstacksoftwaredeveloperAwesome"}, {"promptId": "gaslighterAwesome"}, {"promptId": "gnomistAwesome"}, {"promptId": "gomokuplayerAwesome"}, {"promptId": "historianAwesome"}, {"promptId": "hypnotherapistAwesome"}, {"promptId": "instructorinaschoolAwesome"}, {"promptId": "interiordecoratorAwesome"}, {"promptId": "investmentmanagerAwesome"}, {"promptId": "itarchitectAwesome"}, {"promptId": "itexpertAwesome"}, {"promptId": "japanesekanjiquizmachineAwesome"}, {"promptId": "javascriptconsoleAwesome"}, {"promptId": "journalistAwesome"}, {"promptId": "journalreviewerAwesome"}, {"promptId": "languagedetectorAwesome"}, {"promptId": "languageliterarycriticAwesome"}, {"promptId": "legaladvisorAwesome"}, {"promptId": "lifecoachAwesome"}, {"promptId": "linuxterminalAwesome"}, {"promptId": "logisticianAwesome"}, {"promptId": "lunaticAwesome"}, {"promptId": "machinelearningengineerAwesome"}, {"promptId": "magicianAwesome"}, {"promptId": "makeupartistAwesome"}, {"promptId": "mathematicalhistoryteacherAwesome"}, {"promptId": "mathematicianAwesome"}, {"promptId": "mathteacher<PERSON><PERSON><PERSON>"}, {"promptId": "mentalhealthadviserAwesome"}, {"promptId": "midjourneypromptgeneratorAwesome"}, {"promptId": "morsecodetranslatorAwesome"}, {"promptId": "motivationalcoachAwesome"}, {"promptId": "motivationalspeakerAwesome"}, {"promptId": "moviecriticAwesome"}, {"promptId": "muslimimamAwesome"}, {"promptId": "newlanguagecreatorAwesome"}, {"promptId": "notetakingassistantAwesome"}, {"promptId": "novelist<PERSON><PERSON><PERSON>"}, {"promptId": "passwordgeneratorAwesome"}, {"promptId": "personalchefAwesome"}, {"promptId": "personalshopperAwesome"}, {"promptId": "personalstylistAwesome"}, {"promptId": "personaltrainerAwesome"}, {"promptId": "petbehavioristAwesome"}, {"promptId": "philosopher<PERSON><PERSON><PERSON>"}, {"promptId": "philosophyteacherAwesome"}, {"promptId": "phpinterpreterAwesome"}, {"promptId": "plagiarismcheckerAwesome"}, {"promptId": "poet<PERSON><PERSON>ome"}, {"promptId": "positioninterviewerAwesome"}, {"promptId": "productmanagerAwesome"}, {"promptId": "promptenhancerAwesome"}, {"promptId": "promptgeneratorAwesome"}, {"promptId": "proofreaderAwesome"}, {"promptId": "psychologistAwesome"}, {"promptId": "publicspeakingcoachAwesome"}, {"promptId": "pythoninterpreterAwesome"}, {"promptId": "rapperAwesome"}, {"promptId": "realestateagentAwesome"}, {"promptId": "recruiterAwesome"}, {"promptId": "regexgeneratorAwesome"}, {"promptId": "relationshipcoachAwesome"}, {"promptId": "rprogramminginterpreterAwesome"}, {"promptId": "salespersonAwesome"}, {"promptId": "scientificdatavisualizerAwesome"}, {"promptId": "screenwriterA<PERSON><PERSON>"}, {"promptId": "selfhelpbookAwesome"}, {"promptId": "seniorfrontenddeveloperAwesome"}, {"promptId": "smartdomainnamegeneratorAwesome"}, {"promptId": "socialmediainfluencerAwesome"}, {"promptId": "socialmediamanagerAwesome"}, {"promptId": "socratAwesome"}, {"promptId": "socraticmethodAwesome"}, {"promptId": "softwarequalityassurancetesterAwesome"}, {"promptId": "solrsearchengineAwesome"}, {"promptId": "songrecommenderAwesome"}, {"promptId": "speechlanguagepathologistslpAwesome"}, {"promptId": "spokenenglishteacherandimproverAwesome"}, {"promptId": "spongebobsmagicconchshellAwesome"}, {"promptId": "sqlterminalAwesome"}, {"promptId": "stackoverflowpostAwesome"}, {"promptId": "standupcomedianAwesome"}, {"promptId": "startupideageneratorAwesome"}, {"promptId": "startuptechlawyerAwesome"}, {"promptId": "statisticianAwesome"}, {"promptId": "storytellerAwesome"}, {"promptId": "svgdesignerAwesome"}, {"promptId": "synonymfinderAwesome"}, {"promptId": "talentcoachAwesome"}, {"promptId": "teatasterAwesome"}, {"promptId": "technologytransfererAwesome"}, {"promptId": "techreviewerAwesome"}, {"promptId": "techwriterAwesome"}, {"promptId": "textbasedadventuregameAwesome"}, {"promptId": "tictactoegameAwesome"}, {"promptId": "timetravelguideAwesome"}, {"promptId": "titlegeneratorforwrittenpiecesAwesome"}, {"promptId": "travelguideAwesome"}, {"promptId": "unconstrainedaimodeldanAwesome"}, {"promptId": "uxuideveloperAwesome"}, {"promptId": "virtualdoctorAwesome"}, {"promptId": "webbrowserAwesome"}, {"promptId": "webdesignconsultantAwesome"}, {"promptId": "wikipediapageAwesome"}, {"promptId": "yogi<PERSON>wes<PERSON>"}], "installedPrompts": [{"promptId": "academicianA<PERSON><PERSON>", "version": ""}, {"promptId": "artistadvisorAwesome", "version": ""}, {"promptId": "aiwritingtutorAwesome", "version": ""}, {"promptId": "accountantAwesome", "version": ""}, {"promptId": "buddha<PERSON><PERSON><PERSON>", "version": ""}, {"promptId": "etymologist<PERSON><PERSON><PERSON>", "version": ""}, {"promptId": "debater<PERSON><PERSON><PERSON>", "version": ""}, {"promptId": "fullstacksoftwaredeveloperAwesome", "version": ""}, {"promptId": "emojitranslatorAwesome", "version": ""}, {"promptId": "astrologerAwesome", "version": ""}, {"promptId": "dreaminterpreterAwesome", "version": ""}, {"promptId": "classicalmusiccomposerAwesome", "version": ""}, {"promptId": "midjourneypromptgeneratorAwesome", "version": ""}, {"promptId": "fancytitlegeneratorAwesome", "version": ""}, {"promptId": "essaywriter<PERSON><PERSON><PERSON>", "version": ""}, {"promptId": "morsecodetranslatorAwesome", "version": ""}, {"promptId": "machinelearningengineerAwesome", "version": ""}, {"promptId": "dentistAwesome", "version": ""}, {"promptId": "diagramgeneratorAwesome", "version": ""}, {"promptId": "makeupartistAwesome", "version": ""}, {"promptId": "datascientistAwesome", "version": ""}, {"promptId": "diyexpertAwesome", "version": ""}, {"promptId": "chiefexecutiveofficerAwesome", "version": ""}, {"promptId": "financialanalystAwesome", "version": ""}, {"promptId": "linuxterminalAwesome", "version": ""}, {"promptId": "journalreviewerAwesome", "version": ""}, {"promptId": "mentalhealthadviserAwesome", "version": ""}, {"promptId": "hypnotherapistAwesome", "version": ""}, {"promptId": "coverletterAwesome", "version": ""}, {"promptId": "japanesekanjiquizmachineAwesome", "version": ""}, {"promptId": "instructorinaschoolAwesome", "version": ""}, {"promptId": "personalshopperAwesome", "version": ""}, {"promptId": "cybersecurityspecialistAwesome", "version": ""}, {"promptId": "englishtranslatorandimproverAwesome", "version": ""}, {"promptId": "moviecriticAwesome", "version": ""}, {"promptId": "motivationalspeakerAwesome", "version": ""}, {"promptId": "dietitianAwesome", "version": ""}, {"promptId": "motivationalcoachAwesome", "version": ""}, {"promptId": "logisticianAwesome", "version": ""}, {"promptId": "muslimimamAwesome", "version": ""}, {"promptId": "elocutionistAwesome", "version": ""}, {"promptId": "fillintheblankworksheetsgeneratorAwesome", "version": ""}, {"promptId": "floristAwesome", "version": ""}, {"promptId": "lifecoachAwesome", "version": ""}, {"promptId": "notetakingassistantAwesome", "version": ""}, {"promptId": "emergencyresponseprofessionalAwesome", "version": ""}, {"promptId": "debatecoachAwesome", "version": ""}, {"promptId": "novelist<PERSON><PERSON><PERSON>", "version": ""}, {"promptId": "historianAwesome", "version": ""}, {"promptId": "englishpronunciationhelperAwesome", "version": ""}, {"promptId": "advertiserAwesome", "version": ""}, {"promptId": "careercounselorAwesome", "version": ""}, {"promptId": "digitalartgalleryguideAwesome", "version": ""}, {"promptId": "asciiartistAwesome", "version": ""}, {"promptId": "aitryingtoescapetheboxAwesome", "version": ""}, {"promptId": "fallacyfinderAwesome", "version": ""}, {"promptId": "javascriptconsoleAwesome", "version": ""}, {"promptId": "passwordgeneratorAwesome", "version": ""}, {"promptId": "carnavigationsystemAwesome", "version": ""}, {"promptId": "footballcommentatorAwesome", "version": ""}, {"promptId": "drunkpersonAwesome", "version": ""}, {"promptId": "automobilemechanicAwesome", "version": ""}, {"promptId": "developerrelationsconsultantAwesome", "version": ""}, {"promptId": "friendAwesome", "version": ""}, {"promptId": "journalistAwesome", "version": ""}, {"promptId": "chemicalreactorAwesome", "version": ""}, {"promptId": "interiordecoratorAwesome", "version": ""}, {"promptId": "gomokuplayerAwesome", "version": ""}, {"promptId": "foodcriticAwesome", "version": ""}, {"promptId": "investmentmanagerAwesome", "version": ""}, {"promptId": "chatgptpromptgeneratorAwesome", "version": ""}, {"promptId": "magicianAwesome", "version": ""}, {"promptId": "excelsheetAwesome", "version": ""}, {"promptId": "itarchitectAwesome", "version": ""}, {"promptId": "gnomistAwesome", "version": ""}, {"promptId": "mathteacher<PERSON><PERSON><PERSON>", "version": ""}, {"promptId": "legaladvisorAwesome", "version": ""}, {"promptId": "mathematicalhistoryteacherAwesome", "version": ""}, {"promptId": "aiassisteddoctorAwesome", "version": ""}, {"promptId": "newlanguagecreatorAwesome", "version": ""}, {"promptId": "personalchefAwesome", "version": ""}, {"promptId": "languageliterarycriticAwesome", "version": ""}, {"promptId": "mathematicianAwesome", "version": ""}, {"promptId": "babysitterAwesome", "version": ""}, {"promptId": "lunaticAwesome", "version": ""}, {"promptId": "languagedetectorAwesome", "version": ""}, {"promptId": "chessplayerAwesome", "version": ""}, {"promptId": "chefAwesome", "version": ""}, {"promptId": "cheaptravelticketadvisorAwesome", "version": ""}, {"promptId": "composer<PERSON><PERSON><PERSON>", "version": ""}, {"promptId": "doctor<PERSON><PERSON><PERSON>", "version": ""}, {"promptId": "filmcriticAwesome", "version": ""}, {"promptId": "commitmessagegeneratorAwesome", "version": ""}, {"promptId": "educationalcontentcreatorAwesome", "version": ""}, {"promptId": "gaslighterAwesome", "version": ""}, {"promptId": "commentariatAwesome", "version": ""}, {"promptId": "aphorismbookAwesome", "version": ""}, {"promptId": "petbehavioristAwesome", "version": ""}, {"promptId": "philosophyteacherAwesome", "version": ""}, {"promptId": "proofreaderAwesome", "version": ""}, {"promptId": "regexgeneratorAwesome", "version": ""}, {"promptId": "pythoninterpreterAwesome", "version": ""}, {"promptId": "psychologistAwesome", "version": ""}, {"promptId": "scientificdatavisualizerAwesome", "version": ""}, {"promptId": "recruiterAwesome", "version": ""}, {"promptId": "realestateagentAwesome", "version": ""}, {"promptId": "rapperAwesome", "version": ""}, {"promptId": "phpinterpreterAwesome", "version": ""}, {"promptId": "publicspeakingcoachAwesome", "version": ""}, {"promptId": "rprogramminginterpreterAwesome", "version": ""}, {"promptId": "seniorfrontenddeveloperAwesome", "version": ""}, {"promptId": "promptenhancerAwesome", "version": ""}, {"promptId": "socialmediamanagerAwesome", "version": ""}, {"promptId": "plagiarismcheckerAwesome", "version": ""}, {"promptId": "spongebobsmagicconchshellAwesome", "version": ""}, {"promptId": "promptgeneratorAwesome", "version": ""}, {"promptId": "uxuideveloperAwesome", "version": ""}, {"promptId": "startuptechlawyerAwesome", "version": ""}, {"promptId": "speechlanguagepathologistslpAwesome", "version": ""}, {"promptId": "travelguideAwesome", "version": ""}, {"promptId": "standupcomedianAwesome", "version": ""}, {"promptId": "statisticianAwesome", "version": ""}, {"promptId": "yogi<PERSON>wes<PERSON>", "version": ""}, {"promptId": "storytellerAwesome", "version": ""}, {"promptId": "philosopher<PERSON><PERSON><PERSON>", "version": ""}, {"promptId": "timetravelguideAwesome", "version": ""}, {"promptId": "webdesignconsultantAwesome", "version": ""}, {"promptId": "songrecommenderAwesome", "version": ""}, {"promptId": "textbasedadventuregameAwesome", "version": ""}, {"promptId": "selfhelpbookAwesome", "version": ""}, {"promptId": "talentcoachAwesome", "version": ""}, {"promptId": "technologytransfererAwesome", "version": ""}, {"promptId": "socraticmethodAwesome", "version": ""}, {"promptId": "techreviewerAwesome", "version": ""}, {"promptId": "teatasterAwesome", "version": ""}, {"promptId": "techwriterAwesome", "version": ""}, {"promptId": "softwarequalityassurancetesterAwesome", "version": ""}, {"promptId": "solrsearchengineAwesome", "version": ""}, {"promptId": "personalstylistAwesome", "version": ""}, {"promptId": "productmanagerAwesome", "version": ""}, {"promptId": "salespersonAwesome", "version": ""}, {"promptId": "smartdomainnamegeneratorAwesome", "version": ""}, {"promptId": "screenwriterA<PERSON><PERSON>", "version": ""}, {"promptId": "relationshipcoachAwesome", "version": ""}, {"promptId": "synonymfinderAwesome", "version": ""}, {"promptId": "startupideageneratorAwesome", "version": ""}, {"promptId": "stackoverflowpostAwesome", "version": ""}, {"promptId": "spokenenglishteacherandimproverAwesome", "version": ""}, {"promptId": "svgdesignerAwesome", "version": ""}, {"promptId": "personaltrainerAwesome", "version": ""}, {"promptId": "titlegeneratorforwrittenpiecesAwesome", "version": ""}, {"promptId": "socratAwesome", "version": ""}, {"promptId": "socialmediainfluencerAwesome", "version": ""}, {"promptId": "poet<PERSON><PERSON>ome", "version": ""}, {"promptId": "sqlterminalAwesome", "version": ""}, {"promptId": "webbrowserAwesome", "version": ""}, {"promptId": "virtualdoctorAwesome", "version": ""}, {"promptId": "wikipediapageAwesome", "version": ""}, {"promptId": "unconstrainedaimodeldanAwesome", "version": ""}, {"promptId": "positioninterviewerAwesome", "version": ""}, {"promptId": "characterfrommoviebookanythingAwesome", "version": ""}, {"promptId": "itexpertAwesome", "version": ""}, {"promptId": "tictactoegameAwesome", "version": ""}, {"promptId": "biblicaltranslatorAwesome", "version": ""}], "version": "0.0.2"}}, "subscriptions": []}