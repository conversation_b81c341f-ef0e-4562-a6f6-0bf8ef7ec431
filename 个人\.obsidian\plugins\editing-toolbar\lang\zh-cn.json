{"manifest": {"translationVersion": 1743335821715, "pluginVersion": "2.4.16"}, "description": {"original": "The Obsidian Editing Toolbar is modified from cmenu, which provides more powerful customization settings and has many built-in editing commands to be a MS Word-like toolbar editing experience.", "translation": "黑曜石编辑工具栏是从cmenu修改而来的，cmenu提供了更强大的自定义设置，并具有许多内置的编辑命令，可以提供类似MS Word的工具栏编辑体验。"}, "dict": {"name:\"Clear text formatting\"": "name:\"清除文本格式\"", "name:\"Header 2\"": "name:\"2 级标题\"", "name:\"Header 3\"": "name:\"3 级标题\"", "name:\"submenu\"": "name:\"子菜单\"", "name:\"Header 1\"": "name:\"1 级标题\"", "name:\"Header 4\"": "name:\"4 级标题\"", "name:\"Header 5\"": "name:\"5 级标题\"", "name:\"Header 6\"": "name:\"6 级标题\"", "name:\"Horizontal divider\"": "name:\"水平分隔器\"", "name:\"submenu-list\"": "name:\"子菜单-列表\"", "name:\"submenu-aligin\"": "name:\"子菜单-位置\"", "name:\"<center>\"": "name:\"<居中>\"", "name:\"Change font color[html]\"": "name:\"更改字体颜色[html]\"", "name:\"Change Backgroundcolor[html]\"": "name:\"更改背景颜色[html]\"", "name:\"Fullscreen focus mode\"": "name:\"全屏对焦模式\"", "name:\"Add embed\"": "name:\"添加嵌入\"", "name:\"Insert markdown link\"": "name:\"插入标记链接\"", "name:\"Add tag\"": "name:\"添加标签\"", "name:\"Add internal link\"": "name:\"添加内部链接\"", "name:\"Hide/show \"": "name:\"隐藏/显示\"", "name:\"Format Eraser\"": "name:\"格式化橡皮擦\"", "name:\"bullet list\"": "name:\"项目符号\"", "name:\"undo editor\"": "name:\"撤消\"", "name:\"redo editor\"": "name:\"重做\"", "name:\"Toggle bold\"": "name:\"粗体\"", "name:\"Toggle italics\"": "name:\"斜体\"", "name:\"Toggle strikethrough\"": "name:\"删除线\"", "name:\"Toggle underline\"": "name:\"下划线\"", "name:\"==Toggle highlight ==\"": "name:\"==高亮显示==\"", "name:\"Toggle blockquote\"": "name:\"块引用\"", "name:\"Toggle superscript\"": "name:\"上标\"", "name:\"Toggle subscript\"": "name:\"下标\"", "name:\"inline code\"": "name:\"内联代码\"", "name:\"Toggle codeblock\"": "name:\"代码块\"", "name:\"insert wikilink [[]]\"": "name:\"插入 wiki链接 [[]]\"", "name:\"insert link []()\"": "name:\"插入链接[]()\"", "name:\"insert embed ![[]]\"": "name:\"插入嵌入！[[]]\"", "name:\"numbered list\"": "name:\"编号列表\"", "name:\"cMenu: unindent-list\"": "name:\"cMenu:反缩进列表\"", "name:\"cMenu: indent list\"": "name:\"cMenu:缩进列表\"", "name:\"workplace-Fullscreen \"": "name:\"工作区全屏\"", "Name:\"revertOnSpill\"": "Name:\"返回溢出\"", "Name:\"removeOnSpill\"": "Name:\"清除溢出\"", "name:\"className\"": "name:\"类名\"", "name:\"glyphName\",\"glyph-name\"": "name:\"字形名称\",\"字形-名称\"", "name:\"Toggle code\"": "name:\"代码\"", "name:\"Toggle bullet\"": "name:\"项目符号\"", "name:\"Toggle checklist status\"": "name:\"检查表状态\"", "name:\"Toggle comment\"": "name:\"注释\"", "name:\"Toggle highlight\"": "name:\"高亮显示\"", "name:\"Toggle numbered list\"": "name:\"编号列表\"", "name:\"OFF Format Brush\"": "name:\"关闭格式画笔\"", "name:\"indent list\"": "name:\"缩进列表\"", "name:\"Remove header level\"": "name:\"清除标题级别\"", "name:`Toggle ${r}`": "name:`切换 ${r}`", "name:\"unindent-list\"": "name:\"反缩进列表\"", "align=\"left\"": "align=\"左\"", "If you like this Plugin and are considering donating to support continued development, use the button below!": "如果你喜欢这个插件，并且正在考虑捐款支持继续开发，请使用下面的按钮！", "name:\" copy \"": "name:\" 复制 \"", "name:\" cut \"": "name:\" 剪切 \"", "name:\"paste \"": "name:\"粘贴 \"", "name:\"swap line down\"": "name:\"行下移\"", "name:\"swap line up\"": "name:\"行上移\"", "name:\"upload attach file\"": "name:\"上传附件\"", "name:\"Toggle table\"": "name:\"切换表格\"", "name:\"Toggle cycle list checklist\"": "name:\"循环切换列表检查表\"", "name:\"Toggle Callout \"": "name:\"切换 Callout \"", "name:\"Toggle inline math\"": "name:\"切换内联公式\"", "name:\"Toggle MathBlock\"": "name:\"切换公式块\"", "name:\"checklist\"": "name:\"列表\"", "\"glass\"": "\"毛玻璃\"", "\"cut editor\"": "\"剪切编辑器\"", "\"copy editor\"": "\"复制编辑器\"", "\"paste editor\"": "\"粘贴编辑器\"", "\"Toggle upload attach file\"": "\"上传附件\"", "\"Toggle clear formatting\"": "\"格式清除\"", "\"Toggle swap line up\"": "\"行上移\"", "\"Toggle swap line down\"": "\"行下移\"", "Notice(Wr(\"The command\")": "Notice(Wr(\"命令\")", "Notice(Wr(\"Font-Color formatting brush ON!\")": "Notice(Wr(\"字体颜色格式化刷打开！\")", "Notice(Wr(\"Command IDs have been successfully repaired!\")": "Notice(Wr(\"命令ID已成功修复！\")", "Notice(Wr(\"No command IDs need to be repaired\")": "Notice(Wr(\"无需修复命令ID\")", "Notice(Wr(\"Error repairing command IDs, please check the console for details\")": "Notice(Wr(\"修复命令ID时出错，请查看控制台了解详细信息\")", "Notice(Wr(\"Successfully restored default settings! (Custom commands preserved)": "Notice(Wr(\"Successfully restored default settings! (Custom commands preserved)", "Notice(Wr(\"Error restoring default settings, please check the console for details\")": "Notice(Wr(\"还原默认设置时出错，请查看控制台了解详细信息\")", "Notice(Wr(\"Command ID and command name cannot be empty\")": "Notice(Wr(\"命令ID和命令名称不能为空\")", "Notice(Wr(\"Command ID cannot contain spaces\")": "Notice(Wr(\"命令ID不能包含空格\")", "Notice(Wr(\"Regex pattern cannot be empty\")": "Notice(Wr(\"正则表达式模式不能为空\")", "Notice(Wr(\"Command\")": "Notice(Wr(\"Command\")", "Notice(e)": "Notice(e)", "Notice(Wr(\"Configuration copied to clipboard\")": "Notice(Wr(\"配置已复制到剪贴板\")", "Notice(Wr(\"Failed to copy configuration\")": "Notice(Wr(\"复制配置失败\")", "Notice(Wr(\"Please paste configuration data first\")": "Notice(Wr(\"请先粘贴配置数据\")", "Notice(Wr(\"Invalid import data format\")": "Notice(Wr(\"导入数据格式无效\")", "Notice(Wr(\"No valid configuration found in import data\")": "Notice(Wr(\"在导入数据中找不到有效配置\")", "Notice(Wr(\"Configuration imported successfully\")": "Notice(Wr(\"配置导入成功\")", "Notice(Wr(\"Error:\")": "Notice(Wr(\"错误：\")", "Notice(\"The selected style has no commands to import\")": "Notice(\"所选样式没有要导入的命令\")", "Notice(`Commands imported successfully from \"${r}\" to \"${this.currentEditingConfig}\" `+Wr(\"configuration\")": "Notice(`Commands imported successfully from \"${r}\" to \"${this.currentEditingConfig}\" `+Wr(\"configuration\")", "Notice(\"All commands have been removed\")": "Notice(\"所有命令均已删除\")", "Notice(Wr(\"All commands have been removed\")": "Notice(Wr(\"所有命令均已删除\")", "Notice(Wr(\"This command is already in the toolbar\")": "Notice(Wr(\"此命令已在工具栏中\")", "Notice(Wr(\"Command added to toolbar\")": "Notice(Wr(\"命令已添加到工具栏\")", "Notice(Wr(\"Command deleted\")": "Notice(Wr(\"命令已删除\")", "Notice(Wr(\"Please select text or copy text to clipboard first\")": "Notice(Wr(\"请先选择文本或将文本复制到剪贴板\")", "Notice(Wr(\"Please select text first\")": "Notice(Wr(\"请先选择文本\")", "Notice(Wr(\"The selected text does not meet the condition requirements\")": "Notice(Wr(\"所选文本不符合条件要求\")", "Notice(Wr(\"Regex command execution error:\")": "Notice(Wr(\"Regex命令执行错误：\")", "Notice(t)": "Notice(t)", "Notice(Wr(\"Format brush ON! Select text to apply【\")": "Notice(Wr(\"格式化画笔打开！选择要应用的文本【\")", "Notice(Wr(\"Please execute a editingToolbar format command first, then enable the format brush\")": "Notice(Wr(\"请先执行editingToolbar格式命令，然后启用格式画笔\")", "Notice(Wr(\"Following style commands successfully initialized\")": "Notice(Wr(\"以下样式命令已成功初始化\")", "Notice(Wr(\"Top style commands successfully initialized\")": "Notice(Wr(\"顶部样式命令已成功初始化\")", "Notice(Wr(\"Fixed style commands successfully initialized\")": "Notice(Wr(\"已成功初始化固定样式命令\")", "Notice(Wr(\"Mobile style commands successfully initialized\")": "Notice(Wr(\"Mobile风格命令已成功初始化\")", ".log(`%c命令 '${this.command.name}' 已添加到编辑工具栏`,\"color: Violet\")": ".log(`%c命令 '${this.command.name}' 已添加到编辑工具栏`,\"color: Violet\")", ".log(`%c命令 '${i.name}' 已添加到编辑工具栏`,\"color: Violet\")": ".log(`%c命令 '${i.name}' 已添加到编辑工具栏`,\"color: Violet\")", ".log(\"Editing Toolbar: Failed to find target DOM element for toolbar insertion\")": ".log(\"编辑工具栏：找不到插入工具栏的目标DOM元素\")", "elog(){return e(this,void 0,void 0,(function*()": "elog(){return e(this,void 0,void 0,(function*()", "elog()})": "elog()})", ".log(`%cCommand '${i.name}' was removed from editingToolbar`,\"color: #989cab\")": ".log(`%cCommand '${i.name}' was removed from editingToolbar`,\"color: #989cab\")", ".log(`%cCommand '${t.id}' add `,\"color: #989cab\")": ".log(`%cCommand '${t.id}' add `,\"color: #989cab\")", ".log(\"当前浏览器不支持Fullscreen API !\")": ".log(\"当前浏览器不支持Fullscreen API !\")", ".log(t.message)": ".log(t.message)", ".log(\"editingToolbar v\"+e+\" loaded\")": ".log(\"editingToolbar v\"+e+\" loaded\")", ".log(\"editing toolbar disable loading on mobile\")": ".log(\"编辑工具栏禁止在移动设备上加载\")", ".log(\"editingToolbar unloaded\")": ".log(\"未下载的编辑工具\")", "nerror(String(e)": "nerror(String(e)", ".error(i+\":\",e)": ".error(i+\":\",e)", ".error(e)": ".error(e)", ".error(\"加载 Changelog 时出错:\",t)": ".error(\"加载 Changelog 时出错:\",t)", ".error(\"修复命令ID时出错:\",e)": ".error(\"修复命令ID时出错:\",e)", ".error(t)": ".error(t)", ".error(\"恢复默认设置时出错:\",e)": ".error(\"恢复默认设置时出错:\",e)", ".error(\"Failed to copy: \",e)": ".error(\"复制失败：\",e)", ".error(\"Import error:\",e)": ".error(\"导入错误：\",e)", ".error(\"Failed to set icon:\",t)": ".error(\"设置图标失败：\",t)", ".error(\"Subresult is undefined.\")": ".error(\"子结果未定义。\")", ".error(\"Failed to read clipboard:\",t)": ".error(\"无法读取剪贴板：\",t)", ".error(\"读取剪贴板失败:\",e)": ".error(\"读取剪贴板失败:\",e)", ".error(\"正则表达式命令执行错误:\",e)": ".error(\"正则表达式命令执行错误:\",e)", ".error(\"读取剪贴板失败:\",t)": ".error(\"读取剪贴板失败:\",t)", ".error(\"Copy failed:\",t)": ".error(\"复制失败：\",t)", ".error(\"Paste failed:\",t)": ".error(\"粘贴失败：\",t)", ".error(\"Cut failed:\",t)": ".error(\"剪切失败：\",t)", "name:\"Undo editor\"": "name:\"撤消编辑器\"", "name:\"Redo editor\"": "name:\"重做编辑器\"", "name:\"Format Brush\"": "name:\"格式化画笔\"", "name:\"Bold\"": "name:\"粗体", "name:\"Italics\"": "name:\"斜体\"", "name:\"Strikethrough\"": "name:\"删除线\"", "name:\"Underline\"": "name:\"在画线\"", "name:\"==Highlight==\"": "name:\"==突出显示==\"", "name:\"Copy\"": "name:\"复制\"", "name:\"Cut\"": "name:\"切\"", "name:\"Paste\"": "name:\"粘贴\"", "name:\"Swap line down\"": "name:\"向下交换线路\"", "name:\"Swap line up\"": "name:\"交换队列\"", "name:\"Attach file\"": "name:\"附加文件\"", "name:\"Insert Table\"": "name:\"插入表格\"", "name:\"Cycle list checklist\"": "name:\"循环清单检查表\"", "name:\"Blockquote\"": "name:\"块引用\"", "name:\"Insert Callout \"": "name:\"插入标注\"", "name:\"Superscript\"": "name:\"上标\"", "name:\"Subscript\"": "name:\"下标\"", "name:\"Inline code\"": "name:\"内联代码\"", "name:\"Code block\"": "name:\"代码块\"", "name:\"Insert wikilink [[]]\"": "name:\"插入wikilink[[]]\"", "name:\"Insert embed ![[]]\"": "name:\"插入嵌入![[]]\"", "name:\"Insert link []()\"": "name:\"插入链接[]（）\"", "name:\"Inline math\"": "name:\"内联数学\"", "name:\"MathBlock\"": "name:\"<PERSON><PERSON><PERSON>\"", "name:\"Checklist\"": "name:\"清单\"", "name:\"Renumber ordered list\"": "name:\"重新编号已排序列表\"", "name:\"Numbered list\"": "name:\"编号列表\"", "name:\"Bullet list\"": "name:\"项目符号\"", "name:\"Unindent-list\"": "name:\"意外列表\"", "name:\"Indent list\"": "name:\"缩进列表\"", "name:'<p aligin=\"justify\"></p>'": "name:'<p aligin=\"justify\"></p>'", "name:'<p aligin=\"left\"></p>'": "name:'<p aligin=\"left\"></p>'", "name:'<p aligin=\"right\"></p>'": "name:'<p aligin=\"right\"></p>'", "name:\"Workplace-Fullscreen \"": "name:\"工作场所全屏\"", "name:\"filter\"": "name:\"滤器\"", "name:\"choose\"": "name:\"选择\"", "name:\"start\"": "name:\"开始\"", "name:\"clone\"": "name:\"克隆\"", "name:\"change\"": "name:\"变化\"", "name:\"unchoose\"": "name:\"取消选择\"", "name:\"add\"": "name:\"添加\"", "name:\"remove\"": "name:\"去除\"", "name:\"sort\"": "name:\"排序\"", "name:\"update\"": "name:\"更新\"", "name:\"end\"": "name:\"结束\"", "name:\"Code\"": "name:\"代码\"", "name:\"Link\"": "name:\"链接\"", "name:\"Left sidebar\"": "name:\"左侧边栏\"", "name:\"Checklist status\"": "name:\"检查表状态\"", "name:\"Comment\"": "name:\"评论\"", "name:\"Insert Callout\"": "name:\"插入标注\"", "name:\"item\"": "name:\"项目\"", "name:\"HR\"": "name:\"H<PERSON>\"", "name:\"Clear formatting\"": "name:\"清除格式\"", "name:\"Unindent list\"": "name:\"意外事件列表\"", "name:\"highlight\"": "name:\"突出\"", "name:\"Copy editor\"": "name:\"文案编辑\"", "name:\"Paste editor\"": "name:\"粘贴编辑器\"", "name:\"Cut editor\"": "name:\"剪切编辑器\"", "name:\"Insert Callout(Modal)\"": "name:\"插入标注（模态）\"", "name:\"Insert Link(Modal)\"": "name:\"插入链接（模态）\"", "name:\"Workplace Fullscreen Focus\"": "name:\"工作场所全屏焦点\"", "name:`Toggle ${t}`": "name:`切换${t}`", "name:`${t.name}`": "name:`${t.name}`", "name:\"Toggle Format Brush\"": "name:\"切换画笔格式\"", "text:\":\"": "text:\":\"", "text:\",Preview:\"": "text:\"，预览：\"", "text:\")});b.style.display=\"": "text:\")});b.style.display=\"", "text:\"切换到正则表达式命令\"": "text:\"切换到正则表达式命令\"", "text:\"import\"": "text:\"导入\"", "text:\"Obsidian Editing Toolbar:\"": "text:\"Obsidian编辑工具栏：\"", "text:\"作者：\"": "text:\"作者：\"", "text:\"Cuman ✨\"": "text:\"Cuman ✨\"", "text:\"  教程：\"": "text:\"  教程：\"", "text:\"pkmer.cn\"": "text:\"pkmer.cn\"", "settings:\":\"": "settings:\":\"", "settings:\",\"": "settings:\",\"", ".setText(\"overwrite\"===this.importMode?Wr(\"Warning: Overwrite mode will replace existing settings with imported ones.\")": ".setText(\"overwrite\"===this.importMode?Wr(\"Warning: Overwrite mode will replace existing settings with imported ones.\")", ".setText(`${t.Platform.isMacOS?\"⌘\":\"Ctrl\"} + Enter ${Wr(\"to insert\")}`)": ".setText(`${t.Platform.isMacOS?\"⌘\":\"Ctrl\"} + Enter ${Wr(\"to insert\")}`)", ".setButtonText(\"保存\")": ".setButtonText(\"保存\")", ".setButtonText(\"overwrite\"===this.importMode?Wr(\"Overwrite Import\")": ".setButtonText(\"overwrite\"===this.importMode?Wr(\"Overwrite Import\")", ".setPlaceholder(\"\").setValue(null!==(i=this.item.name)&&void 0!==i?i:\"\")": ".setPlaceholder(\"\").setValue(null!==(i=this.item.name)&&void 0!==i?i:\"\")", ".setTooltip(\"Copy commands from selected style\")": ".setTooltip(\"从选定样式复制命令\")", "Notice(f(\"The command\")": "Notice(f(\"命令\")", "Notice(f(\"Font-Color formatting brush ON!\")": "Notice(f(\"字体颜色格式化刷打开！\")", "Notice(f(\"Command IDs have been successfully repaired!\")": "Notice(f(\"命令ID已成功修复！\")", "Notice(f(\"No command IDs need to be repaired\")": "Notice(f(\"无需修复命令ID\")", "Notice(f(\"Error repairing command IDs, please check the console for details\")": "Notice(f(\"修复命令ID时出错，请查看控制台了解详细信息\")", "Notice(f(\"Successfully restored default settings! (Custom commands preserved)": "Notice(f(\"Successfully restored default settings! (Custom commands preserved)", "Notice(f(\"Error restoring default settings, please check the console for details\")": "Notice(f(\"还原默认设置时出错，请查看控制台了解详细信息\")", "Notice(f(\"Command ID and command name cannot be empty\")": "Notice(f(\"命令ID和命令名称不能为空\")", "Notice(f(\"Command ID cannot contain spaces\")": "Notice(f(\"命令ID不能包含空格\")", "Notice(f(\"Regex pattern cannot be empty\")": "Notice(f(\"正则表达式模式不能为空\")", "Notice(f(\"Command\")": "Notice(f(\"命令\")", "Notice(f(\"Configuration copied to clipboard\")": "Notice(f(\"配置已复制到剪贴板\")", "Notice(f(\"Failed to copy configuration\")": "Notice(f(\"复制配置失败\")", "Notice(f(\"Please paste configuration data first\")": "Notice(f(\"请先粘贴配置数据\")", "Notice(f(\"Invalid import data format\")": "Notice(f(\"导入数据格式无效\")", "Notice(f(\"No valid configuration found in import data\")": "Notice(f(\"在导入数据中找不到有效配置\")", "Notice(f(\"Configuration imported successfully\")": "Notice(f(\"配置导入成功\")", "Notice(f(\"Error:\")": "Notice(f(\"错误：\")", "Notice(`Commands imported successfully from \"${a}\" to \"${this.currentEditingConfig}\" `+f(\"configuration\")": "Notice(`命令已成功从“$｛a｝”导入到“$｛this.currentEditingConfig｝”`+f(\"configuration\")", "Notice(f(\"All commands have been removed\")": "Notice(f(\"所有命令均已删除\")", "Notice(f(\"This command is already in the toolbar\")": "Notice(f(\"此命令已在工具栏中\")", "Notice(f(\"Command added to toolbar\")": "Notice(f(\"命令已添加到工具栏\")", "Notice(f(\"Command deleted\")": "Notice(f(\"命令已删除\")", "Notice(f(\"Please select text or copy text to clipboard first\")": "Notice(f(\"请先选择文本或将文本复制到剪贴板\")", "Notice(f(\"Please select text first\")": "Notice(f(\"请先选择文本\")", "Notice(f(\"The selected text does not meet the condition requirements\")": "Notice(f(\"所选文本不符合条件要求\")", "Notice(f(\"Regex command execution error:\")": "Notice(f(\"Regex命令执行错误：\")", "Notice(f(\"Format brush ON! Select text to apply【\")": "Notice(f(\"格式化画笔打开！选择要应用的文本【\")", "Notice(f(\"Please execute a editingToolbar format command first, then enable the format brush\")": "Notice(f(\"请先执行editingToolbar格式命令，然后启用格式画笔\")", "Notice(f(\"Following style commands successfully initialized\")": "Notice(f(\"以下样式命令已成功初始化\")", "Notice(f(\"Top style commands successfully initialized\")": "Notice(f(\"顶部样式命令已成功初始化\")", "Notice(f(\"Fixed style commands successfully initialized\")": "Notice(f(\"已成功初始化固定样式命令\")", "Notice(f(\"Mobile style commands successfully initialized\")": "Notice(f(\"移动风格命令已成功初始化\")", ".log(`%c命令 '${o.name}' 已添加到编辑工具栏`,\"color: Violet\")": ".log(`%c命令 '${o.name}' 已添加到编辑工具栏`,\"color: Violet\")", ".log(`%cCommand '${o.name}' was removed from editingToolbar`,\"color: #989cab\")": ".log(`%cCommand '${o.name}' was removed from editingToolbar`,\"color: #989cab\")", "text:\")});v.style.display=\"": "text:\")});v.style.display=\"", ".setText(\"overwrite\"===this.importMode?f(\"Warning: Overwrite mode will replace existing settings with imported ones.\")": ".setText(\"overwrite”===this.inmportMode？f（“警告：覆盖模式将用导入的设置替换现有设置。\")", ".setText(`${t.Platform.isMacOS?\"⌘\":\"Ctrl\"} + Enter ${f(\"to insert\")}`)": ".setText(`${t.Platform.isMacOS?\"⌘\":\"Ctrl\"} + Enter ${f(\"to insert\")}`)", ".setButtonText(\"overwrite\"===this.importMode?f(\"Overwrite Import\")": ".setButtonText(\"overwrite“===this.inmportMode？f（”覆盖导入\")", ".setPlaceholder(\"\").setValue(null!==(o=this.item.name)&&void 0!==o?o:\"\")": ".setPlaceholder(\"\").setValue(null!==(o=this.item.name)&&void 0!==o?o:\"\")"}}