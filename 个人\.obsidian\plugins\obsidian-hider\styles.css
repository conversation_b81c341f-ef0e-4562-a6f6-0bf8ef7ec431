/* Hides vault name */
.hider-vault .workspace-sidedock-vault-profile,
body.hider-vault:not(.is-mobile) .workspace-split.mod-left-split .workspace-sidedock-vault-profile {
	display:none;
}

/* Hide tabs */
.hider-tabs .mod-root .workspace-tabs .workspace-tab-header-container {
	display: none;
}

.hider-tabs .mod-top-left-space .view-header {
	padding-left: var(--frame-left-space);
}

/* Hide sidebar buttons */
.hider-sidebar-buttons .sidebar-toggle-button.mod-right,
.hider-sidebar-buttons .sidebar-toggle-button.mod-left {
	display: none;
}
.hider-sidebar-buttons.mod-macos.is-hidden-frameless:not(.is-popout-window) .workspace .workspace-tabs.mod-top-right-space .workspace-tab-header-container {
	padding-right: 4px;
}
.hider-sidebar-buttons.mod-macos {
	--frame-left-space: 60px;
}

/* Hide meta */
.hider-meta .markdown-reading-view .metadata-container {
	display:none;
}

/* Hide scrollbars */
.hider-scroll ::-webkit-scrollbar {
	display:none;
}

/* Hide status */
.hider-status .status-bar {
	display:none;
}

/* Hide tooltips */
.hider-tooltips .tooltip {
	display:none;
}

/* Hide search suggestions */
.hider-search-suggestions .suggestion-container.mod-search-suggestion {
	display: none;
}

/* Hide search count flair */
.hider-search-counts .tree-item-flair:not(.tag-pane-tag-count) { 
	display:none;
}

/* Hide instructions */
.hider-instructions .prompt-instructions {
	display:none;
}

/* Hide file nav header */
.hider-file-nav-header .workspace-leaf-content[data-type=file-explorer] .nav-header {
	display: none;
}
