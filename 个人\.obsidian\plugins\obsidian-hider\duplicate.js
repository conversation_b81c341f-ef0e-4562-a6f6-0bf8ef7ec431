/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var g=Object.defineProperty;var o=Object.getOwnPropertyDescriptor;var d=Object.getOwnPropertyNames;var u=Object.prototype.hasOwnProperty;var r=(a,n)=>{for(var t in n)g(a,t,{get:n[t],enumerable:!0})},c=(a,n,t,e)=>{if(n&&typeof n=="object"||typeof n=="function")for(let s of d(n))!u.call(a,s)&&s!==t&&g(a,s,{get:()=>n[s],enumerable:!(e=o(n,s))||e.enumerable});return a};var p=a=>c(g({},"__esModule",{value:!0}),a);var S={};r(S,{default:()=>h});module.exports=p(S);var i=require("obsidian"),h=class extends i.Plugin{constructor(){super(...arguments);this.refresh=()=>{this.updateStyle()};this.updateStyle=()=>{document.body.classList.toggle("hider-status",this.settings.hideStatus),document.body.classList.toggle("hider-tabs",this.settings.hideTabs),document.body.classList.toggle("hider-scroll",this.settings.hideScroll),document.body.classList.toggle("hider-sidebar-buttons",this.settings.hideSidebarButtons),document.body.classList.toggle("hider-tooltips",this.settings.hideTooltips),document.body.classList.toggle("hider-search-suggestions",this.settings.hideSearchSuggestions),document.body.classList.toggle("hider-file-nav-header",this.settings.hideFileNavButtons),document.body.classList.toggle("hider-search-counts",this.settings.hideSearchCounts),document.body.classList.toggle("hider-instructions",this.settings.hideInstructions),document.body.classList.toggle("hider-meta",this.settings.hidePropertiesReading),document.body.classList.toggle("hider-vault",this.settings.hideVault)}}async onload(){await this.loadSettings(),this.addSettingTab(new l(this.app,this)),this.addCommand({id:"toggle-tab-containers",name:"Toggle tab bar",callback:()=>{this.settings.hideTabs=!this.settings.hideTabs,this.saveData(this.settings),this.refresh()}}),this.addCommand({id:"toggle-hider-status",name:"Toggle status bar",callback:()=>{this.settings.hideStatus=!this.settings.hideStatus,this.saveData(this.settings),this.refresh()}}),this.refresh()}onunload(){console.log("Unloading Hider plugin")}async loadSettings(){this.settings=Object.assign(b,await this.loadData())}async saveSettings(){await this.saveData(this.settings)}},b={hideStatus:!1,hideTabs:!1,hideScroll:!1,hideSidebarButtons:!1,hideTooltips:!1,hideFileNavButtons:!1,hideSearchSuggestions:!1,hideSearchCounts:!1,hideInstructions:!1,hidePropertiesReading:!1,hideVault:!1},l=class extends i.PluginSettingTab{constructor(t,e){super(t,e);this.plugin=e}display(){let{containerEl:t}=this;t.empty(),new i.Setting(t).setName("Hide tab bar").setDesc("Hides the tab container at the top of the window.").addToggle(e=>e.setValue(this.plugin.settings.hideTabs).onChange(s=>{this.plugin.settings.hideTabs=s,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new i.Setting(t).setName("Hide status bar").setDesc("Hides word count, character count and backlink count.").addToggle(e=>e.setValue(this.plugin.settings.hideStatus).onChange(s=>{this.plugin.settings.hideStatus=s,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new i.Setting(t).setName("Hide vault name").setDesc("Hides your vault profile. Warning: this also hides access to the Settings and vault switcher icons. You can use hotkeys or the command palette to open them.").addToggle(e=>e.setValue(this.plugin.settings.hideVault).onChange(s=>{this.plugin.settings.hideVault=s,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new i.Setting(t).setName("Hide scroll bars").setDesc("Hides all scroll bars.").addToggle(e=>e.setValue(this.plugin.settings.hideScroll).onChange(s=>{this.plugin.settings.hideScroll=s,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new i.Setting(t).setName("Hide sidebar toggle buttons").setDesc("Hides both sidebar buttons.").addToggle(e=>e.setValue(this.plugin.settings.hideSidebarButtons).onChange(s=>{this.plugin.settings.hideSidebarButtons=s,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new i.Setting(t).setName("Hide tooltips").setDesc("Hides all tooltips.").addToggle(e=>e.setValue(this.plugin.settings.hideTooltips).onChange(s=>{this.plugin.settings.hideTooltips=s,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new i.Setting(t).setName("Hide file explorer buttons").setDesc("Hides buttons at the top of file explorer (new file, new folder, etc).").addToggle(e=>e.setValue(this.plugin.settings.hideFileNavButtons).onChange(s=>{this.plugin.settings.hideFileNavButtons=s,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new i.Setting(t).setName("Hide instructions").setDesc("Hides instructional tips in modals.").addToggle(e=>e.setValue(this.plugin.settings.hideInstructions).onChange(s=>{this.plugin.settings.hideInstructions=s,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new i.Setting(t).setName("Hide search suggestions").setDesc("Hides suggestions in search pane.").addToggle(e=>e.setValue(this.plugin.settings.hideSearchSuggestions).onChange(s=>{this.plugin.settings.hideSearchSuggestions=s,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new i.Setting(t).setName("Hide count of search term matches").setDesc("Hides the number of matches within each search result.").addToggle(e=>e.setValue(this.plugin.settings.hideSearchCounts).onChange(s=>{this.plugin.settings.hideSearchCounts=s,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new i.Setting(t).setName("Hide properties in Reading view").setDesc("Hides the properties section in Reading view.").addToggle(e=>e.setValue(this.plugin.settings.hidePropertiesReading).onChange(s=>{this.plugin.settings.hidePropertiesReading=s,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()}))}};

/* nosourcemap */