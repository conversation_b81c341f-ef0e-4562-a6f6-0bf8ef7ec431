{
  "main": {
    "id": "f8c87c7c109eee76",
    "type": "split",
    "children": [
      {
<<<<<<< HEAD
<<<<<<< HEAD
        "id": "902664f3d0ccc1d8",
        "type": "tabs",
        "children": [
          {
            "id": "55296597deb0a2e9",
=======
=======
>>>>>>> a038185fd37289b4ef0c25dc6379877fb4fa9420
        "id": "bc9eff874f82bd5c",
        "type": "tabs",
        "children": [
          {
            "id": "1d200339cc020755",
<<<<<<< HEAD
>>>>>>> origin/main
=======
>>>>>>> a038185fd37289b4ef0c25dc6379877fb4fa9420
            "type": "leaf",
            "state": {
              "type": "empty",
              "state": {},
              "icon": "lucide-file",
              "title": "新标签页"
            }
          }
        ]
      }
    ],
    "direction": "vertical"
  },
  "left": {
    "id": "877f40932c74fd67",
    "type": "mobile-drawer",
    "children": [
      {
        "id": "b6158b84476eb1fb",
        "type": "leaf",
        "state": {
          "type": "file-explorer",
          "state": {
            "sortOrder": "alphabetical",
            "autoReveal": false
          },
          "icon": "lucide-folder-closed",
          "title": "文件列表"
        }
      },
      {
        "id": "de9150013451a8a6",
        "type": "leaf",
        "state": {
          "type": "search",
          "state": {
            "query": "",
            "matchingCase": false,
            "explainSearch": false,
            "collapseAll": false,
            "extraContext": false,
            "sortOrder": "alphabetical"
          },
          "icon": "lucide-search",
          "title": "Search"
        }
      },
      {
        "id": "1882397972a3ba0f",
        "type": "leaf",
        "state": {
          "type": "tag",
          "state": {
            "sortOrder": "frequency",
            "useHierarchy": true,
            "showSearch": false,
            "searchQuery": ""
          },
          "icon": "lucide-tags",
          "title": "Tags"
        }
      },
      {
        "id": "f91c77901ea49d8a",
        "type": "leaf",
        "state": {
          "type": "bookmarks",
          "state": {},
          "icon": "lucide-bookmark",
          "title": "Bookmarks"
        }
      },
      {
<<<<<<< HEAD
<<<<<<< HEAD
        "id": "93b5dc27c871d04c",
=======
        "id": "3b052e1f64a7e853",
>>>>>>> origin/main
=======
        "id": "3b052e1f64a7e853",
>>>>>>> a038185fd37289b4ef0c25dc6379877fb4fa9420
        "type": "leaf",
        "state": {
          "type": "mk-path-view",
          "state": {},
          "icon": "layout-grid",
          "title": "Navigator"
        }
      }
    ],
<<<<<<< HEAD
<<<<<<< HEAD
    "currentTab": 5
=======
    "currentTab": 0
>>>>>>> origin/main
=======
    "currentTab": 0
>>>>>>> a038185fd37289b4ef0c25dc6379877fb4fa9420
  },
  "right": {
    "id": "29736c50f099bb39",
    "type": "mobile-drawer",
    "children": [
      {
        "id": "131eaed8a3774799",
        "type": "leaf",
        "state": {
          "type": "backlink",
          "state": {
            "collapseAll": false,
            "extraContext": false,
            "sortOrder": "alphabetical",
            "showSearch": false,
            "searchQuery": "",
            "backlinkCollapsed": false,
            "unlinkedCollapsed": true
          },
          "icon": "links-coming-in",
          "title": "Backlinks"
        }
      },
      {
        "id": "d8c9cfe0702b4d1c",
        "type": "leaf",
        "state": {
          "type": "outgoing-link",
          "state": {
            "linksCollapsed": false,
            "unlinkedCollapsed": true
          },
          "icon": "links-going-out",
          "title": "Outgoing links"
        }
      },
      {
        "id": "ac1e6c24a02ec00e",
        "type": "leaf",
        "state": {
          "type": "outline",
          "state": {
            "followCursor": false,
            "showSearch": false,
            "searchQuery": ""
          },
          "icon": "lucide-list",
          "title": "Outline"
        }
      },
      {
        "id": "abed4662724ef799",
        "type": "leaf",
        "state": {
          "type": "copilot-chat-view",
          "state": {},
          "icon": "message-square",
          "title": "Copilot"
        }
      },
      {
        "id": "286180b7d50a11b8",
        "type": "leaf",
        "state": {
          "type": "git-view",
          "state": {},
          "icon": "git-pull-request",
          "title": "源代码管理"
        }
<<<<<<< HEAD
<<<<<<< HEAD
      },
      {
        "id": "a9890ab6eb653953",
        "type": "leaf",
        "state": {
          "type": "advanced-tables-toolbar",
          "state": {},
          "icon": "spreadsheet",
          "title": "Advanced Tables"
        }
      }
    ],
    "currentTab": 4
=======
      }
    ],
    "currentTab": 3
>>>>>>> origin/main
=======
      }
    ],
    "currentTab": 3
>>>>>>> a038185fd37289b4ef0c25dc6379877fb4fa9420
  },
  "left-ribbon": {
    "hiddenItems": {
      "switcher:打开快速切换": false,
      "graph:查看关系图谱": false,
      "canvas:新建白板": false,
      "daily-notes:打开/创建今天的日记": false,
      "templates:插入模板": false,
      "command-palette:打开命令面板": false,
      "markdown-importer:打开 Markdown 格式转换器": false,
      "zk-prefixer:创建时间戳笔记": false,
      "workspaces:管理工作区布局": false,
      "obsidian-excalidraw-plugin:新建绘图文件": false,
      "custom-image-auto-uploader:Custom Image Auto Uploader / 自定义图片上传云端保存插件": false,
      "table-editor-obsidian:Advanced Tables Toolbar": false,
      "obsidian-textgenerator-plugin:Generate Text!": false,
      "obsidian-textgenerator-plugin:Text Generator: Templates Packages Manager": false,
      "copilot:Open Copilot Chat": false,
      "obsidian-git:打开Git源代码管理": false,
      "templater-obsidian:Templater": false,
      "remotely-save:Remotely Save": false
    }
  },
<<<<<<< HEAD
<<<<<<< HEAD
  "active": "55296597deb0a2e9",
  "lastOpenFiles": [
    "教学/网络爬虫技术应用与开发/试题/试题.md",
    "教学/网络爬虫技术应用与开发/试题/习题.md",
    "Tags",
    "杂记/edu",
    "杂记/deeplink",
    "Excalidraw/Drawing 2025-05-20 12.51.17.excalidraw.md",
    "Excalidraw",
    "未命名.canvas",
    "2025-05-19.md",
    "教学/网络爬虫技术应用与开发/试题",
    "教学/网络爬虫技术应用与开发/课件/第8章.md",
    "教学/网络爬虫技术应用与开发/教案/第8章  爬虫框架Scrapy.md",
    "教学/网络爬虫技术应用与开发/课件/第6章.md",
    "Excalidraw/Drawing 2025-04-20 23.44.38.excalidraw.md",
    "202504202344.md",
    "作业二维码.png.md",
=======
  "active": "b6158b84476eb1fb",
  "lastOpenFiles": [
>>>>>>> origin/main
=======
  "active": "b6158b84476eb1fb",
  "lastOpenFiles": [
>>>>>>> a038185fd37289b4ef0c25dc6379877fb4fa9420
    "rs-test-folder-w8UNsYeT0KlOHYGmswfBh/rs-test-file-0LMFhoLhmcVdfkJ1zBXpH",
    "rs-test-folder-w8UNsYeT0KlOHYGmswfBh",
    "_resources/_E4_BB_98_E8_B4_B9BUG_f2302eebe2c540c6a3c29b0147ff.png",
    "Excalidraw/Drawing 2025-04-20 22.51.28.excalidraw.md",
    "2025-04-20.md",
<<<<<<< HEAD
<<<<<<< HEAD
    "未命名.md",
=======
=======
>>>>>>> a038185fd37289b4ef0c25dc6379877fb4fa9420
    "未命名.canvas",
    "Excalidraw",
    "未命名.md",
    "教学/网络爬虫技术应用与开发/课件/第6章.md",
<<<<<<< HEAD
>>>>>>> origin/main
=======
>>>>>>> a038185fd37289b4ef0c25dc6379877fb4fa9420
    "教学/网络爬虫技术应用与开发/教案/第6章  模拟登录和处理验证码.md",
    "教学/网络爬虫技术应用与开发/代码/第6章/例6-1.md",
    "杂记/VPS.md",
    "杂记/NKN.md",
    "杂记/编程教程.md",
    "教程/E5自动续订.md",
    "杂记/VPS/转发合集.md",
    "杂记/VPS/哪吒探针.md",
    "杂记/VPS/bash.md",
    "杂记/NKN/do—1.md",
    "教程/halo 2 0.md",
    "教程/挂机软件/挂机软件.md",
<<<<<<< HEAD
<<<<<<< HEAD
=======
=======
>>>>>>> a038185fd37289b4ef0c25dc6379877fb4fa9420
    "杂记/VPS/rclone.md",
    "杂记/怎样阅读一本书读书笔记.md",
    "杂记/VPS/V PS-JP.md",
    "杂记/如何解除Word限制编辑.md",
    "杂记/VPS/青龙docker.md",
    "杂记/VPS/哪吒探针/主题美化.md",
    "杂记/VPS/kurun vps.md",
    "教程/Trojan共用443端口.md",
    "杂记/VPS/docker国内源.md",
<<<<<<< HEAD
>>>>>>> origin/main
=======
>>>>>>> a038185fd37289b4ef0c25dc6379877fb4fa9420
    "_resources/image-29.png",
    "_resources/image-19.png",
    "_resources/image-18.png",
    "_resources/image-15.png",
    "_resources/Untitled.png",
    "_resources/image-19.webp",
    "_resources/image-17.webp",
    "_resources/image-16.webp",
    "_resources/image-19 1.webp",
    "教学/网络爬虫技术应用与开发/代码/第7章/实战演练.py",
    "教学/网络爬虫技术应用与开发/代码/第7章/例7-2.py",
<<<<<<< HEAD
<<<<<<< HEAD
    "教学/网络爬虫技术应用与开发/代码/第7章/例7-1.py"
=======
=======
>>>>>>> a038185fd37289b4ef0c25dc6379877fb4fa9420
    "教学/网络爬虫技术应用与开发/代码/第7章/例7-1.py",
    "教学/网络爬虫技术应用与开发/代码/第6章/例6-4.py",
    "教学/网络爬虫技术应用与开发/代码/第6章/例6-3.py",
    "教学/网络爬虫技术应用与开发/代码/第6章/例6-2.py",
    "教学/网络爬虫技术应用与开发/代码/第6章/例6-1.py",
    "教学/网络爬虫技术应用与开发/代码/第5章/实战演练.py"
<<<<<<< HEAD
>>>>>>> origin/main
=======
>>>>>>> a038185fd37289b4ef0c25dc6379877fb4fa9420
  ]
}