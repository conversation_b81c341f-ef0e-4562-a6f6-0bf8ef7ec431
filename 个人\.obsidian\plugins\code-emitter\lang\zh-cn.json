{"manifest": {"translationVersion": 1744799824179, "pluginVersion": "0.4.0"}, "description": {"original": "An Obsidian plugin that allows code blocks to be executed interactively like in Jupyter Notebooks. Supports languages like Rust, Kotlin, Python, JavaScript, TypeScript etc.", "translation": "An Obsidian plugin that allows code blocks to be executed interactively like in Jupyter Notebooks. Supports languages like Rust, Kotlin, Python, JavaScript, TypeScript etc."}, "dict": {".log(\"typescript loaded.\")": ".log(\"已加载打字稿。\")", ".log(\"python loaded.\")": ".log(\"python加载。\")", ".log(\"wenyan:\")": ".log(\"其他：\")", ".log(i)": ".log(i)", ".log(\"wenyan loaded.\")": ".log(\"文艳装上了货。\")", ".error(t)": ".error(t)", "name:\"File.kt\"": "name:\"File.kt\"", "name:\"play\"": "name:\"玩\"", "name:\"clear\"": "name:\"清楚的\"", "text:\"\",code:\"": "text:\"“，代码：\""}}