/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var g=Object.defineProperty;var o=Object.getOwnPropertyDescriptor;var d=Object.getOwnPropertyNames;var u=Object.prototype.hasOwnProperty;var r=(a,n)=>{for(var t in n)g(a,t,{get:n[t],enumerable:!0})},c=(a,n,t,e)=>{if(n&&typeof n=="object"||typeof n=="function")for(let s of d(n))!u.call(a,s)&&s!==t&&g(a,s,{get:()=>n[s],enumerable:!(e=o(n,s))||e.enumerable});return a};var p=a=>c(g({},"__esModule",{value:!0}),a);var S={};r(S,{default:()=>h});module.exports=p(S);var i=require("obsidian"),h=class extends i.Plugin{constructor(){super(...arguments);this.refresh=()=>{this.updateStyle()};this.updateStyle=()=>{document.body.classList.toggle("hider-status",this.settings.hideStatus),document.body.classList.toggle("hider-tabs",this.settings.hideTabs),document.body.classList.toggle("hider-scroll",this.settings.hideScroll),document.body.classList.toggle("hider-sidebar-buttons",this.settings.hideSidebarButtons),document.body.classList.toggle("hider-tooltips",this.settings.hideTooltips),document.body.classList.toggle("hider-search-suggestions",this.settings.hideSearchSuggestions),document.body.classList.toggle("hider-file-nav-header",this.settings.hideFileNavButtons),document.body.classList.toggle("hider-search-counts",this.settings.hideSearchCounts),document.body.classList.toggle("hider-instructions",this.settings.hideInstructions),document.body.classList.toggle("hider-meta",this.settings.hidePropertiesReading),document.body.classList.toggle("hider-vault",this.settings.hideVault)}}async onload(){await this.loadSettings(),this.addSettingTab(new l(this.app,this)),this.addCommand({id:"toggle-tab-containers",name:"切换标签栏",callback:()=>{this.settings.hideTabs=!this.settings.hideTabs,this.saveData(this.settings),this.refresh()}}),this.addCommand({id:"toggle-hider-status",name:"切换状态栏",callback:()=>{this.settings.hideStatus=!this.settings.hideStatus,this.saveData(this.settings),this.refresh()}}),this.refresh()}onunload(){console.log("Unloading Hider plugin")}async loadSettings(){this.settings=Object.assign(b,await this.loadData())}async saveSettings(){await this.saveData(this.settings)}},b={hideStatus:!1,hideTabs:!1,hideScroll:!1,hideSidebarButtons:!1,hideTooltips:!1,hideFileNavButtons:!1,hideSearchSuggestions:!1,hideSearchCounts:!1,hideInstructions:!1,hidePropertiesReading:!1,hideVault:!1},l=class extends i.PluginSettingTab{constructor(t,e){super(t,e);this.plugin=e}display(){let{containerEl:t}=this;t.empty(),new i.Setting(t).setName("隐藏标签栏").setDesc("隐藏窗口顶部的选项卡容器。").addToggle(e=>e.setValue(this.plugin.settings.hideTabs).onChange(s=>{this.plugin.settings.hideTabs=s,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new i.Setting(t).setName("隐藏状态栏").setDesc("隐藏字数、字符数和反向链接数。").addToggle(e=>e.setValue(this.plugin.settings.hideStatus).onChange(s=>{this.plugin.settings.hideStatus=s,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new i.Setting(t).setName("隐藏保管库名称").setDesc("隐藏您的保险库配置文件。警告：这也隐藏了对“设置”和vault切换器图标的访问。您可以使用热键或命令选项板打开它们。").addToggle(e=>e.setValue(this.plugin.settings.hideVault).onChange(s=>{this.plugin.settings.hideVault=s,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new i.Setting(t).setName("隐藏滚动条").setDesc("隐藏所有滚动条。").addToggle(e=>e.setValue(this.plugin.settings.hideScroll).onChange(s=>{this.plugin.settings.hideScroll=s,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new i.Setting(t).setName("隐藏侧边栏切换按钮").setDesc("隐藏两个侧边栏按钮。").addToggle(e=>e.setValue(this.plugin.settings.hideSidebarButtons).onChange(s=>{this.plugin.settings.hideSidebarButtons=s,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new i.Setting(t).setName("隐藏工具提示").setDesc("隐藏所有工具提示。").addToggle(e=>e.setValue(this.plugin.settings.hideTooltips).onChange(s=>{this.plugin.settings.hideTooltips=s,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new i.Setting(t).setName("隐藏文件资源管理器按钮").setDesc("隐藏文件资源管理器顶部的按钮（新文件、新文件夹等）。").addToggle(e=>e.setValue(this.plugin.settings.hideFileNavButtons).onChange(s=>{this.plugin.settings.hideFileNavButtons=s,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new i.Setting(t).setName("隐藏说明").setDesc("在模态中隐藏教学提示。").addToggle(e=>e.setValue(this.plugin.settings.hideInstructions).onChange(s=>{this.plugin.settings.hideInstructions=s,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new i.Setting(t).setName("隐藏搜索建议").setDesc("隐藏搜索窗格中的建议。").addToggle(e=>e.setValue(this.plugin.settings.hideSearchSuggestions).onChange(s=>{this.plugin.settings.hideSearchSuggestions=s,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new i.Setting(t).setName("隐藏搜索词匹配数").setDesc("隐藏每个搜索结果中的匹配数。").addToggle(e=>e.setValue(this.plugin.settings.hideSearchCounts).onChange(s=>{this.plugin.settings.hideSearchCounts=s,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()})),new i.Setting(t).setName("在“阅读”视图中隐藏属性").setDesc("在“阅读”视图中隐藏属性部分。").addToggle(e=>e.setValue(this.plugin.settings.hidePropertiesReading).onChange(s=>{this.plugin.settings.hidePropertiesReading=s,this.plugin.saveData(this.plugin.settings),this.plugin.refresh()}))}};

/* nosourcemap */