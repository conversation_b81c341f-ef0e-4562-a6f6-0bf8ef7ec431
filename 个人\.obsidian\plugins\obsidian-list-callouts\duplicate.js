/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var Ke=Object.create;var O=Object.defineProperty;var Ge=Object.getOwnPropertyDescriptor;var Ye=Object.getOwnPropertyNames,he=Object.getOwnPropertySymbols,Xe=Object.getPrototypeOf,pe=Object.prototype.hasOwnProperty,Qe=Object.prototype.propertyIsEnumerable;var me=(a,e,i)=>e in a?O(a,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):a[e]=i,R=(a,e)=>{for(var i in e||(e={}))pe.call(e,i)&&me(a,i,e[i]);if(he)for(var i of he(e))Qe.call(e,i)&&me(a,i,e[i]);return a};var ge=a=>O(a,"__esModule",{value:!0});var Je=(a,e)=>{ge(a);for(var i in e)O(a,i,{get:e[i],enumerable:!0})},Ze=(a,e,i)=>{if(e&&typeof e=="object"||typeof e=="function")for(let s of Ye(e))!pe.call(a,s)&&s!=="default"&&O(a,s,{get:()=>e[s],enumerable:!(i=Ge(e,s))||i.enumerable});return a},j=a=>Ze(ge(O(a!=null?Ke(Xe(a)):{},"default",a&&a.__esModule&&"default"in a?{get:()=>a.default,enumerable:!0}:{value:a,enumerable:!0})),a);var P=(a,e,i)=>new Promise((s,l)=>{var t=o=>{try{r(i.next(o))}catch(d){l(d)}},c=o=>{try{r(i.throw(o))}catch(d){l(d)}},r=o=>o.done?s(o.value):Promise.resolve(o.value).then(t,c);r((i=i.apply(a,e)).next())});Je(exports,{default:()=>oe});function H(a){if(typeof a!="string")throw new TypeError("Expected a string");return a.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}var $=j(require("obsidian"));var B=j(require("@codemirror/language")),q=j(require("@codemirror/state")),M=j(require("@codemirror/view")),fe=j(require("obsidian")),W=q.StateEffect.define(),be=class extends M.WidgetType{toDOM(){return createSpan({cls:"lc-list-bg",attr:{"aria-hidden":"true"}})}eq(){return!0}},ye=class extends M.WidgetType{constructor(e,i){super();this.char=e,this.icon=i}toDOM(){return createSpan({text:this.char,cls:"lc-list-marker",attr:{"aria-hidden":"true"}},e=>{this.icon&&(0,fe.setIcon)(e,this.icon)})}eq(e){return e.char===this.char&&e.icon===this.icon}},ei=(a,e)=>M.Decoration.line({attributes:{class:"lc-list-callout",style:`--lc-callout-color: ${e}`,"data-callout":a}}),Q=q.StateField.define({create(){return{callouts:{},re:null}},update(a,e){for(let i of e.effects)i.is(W)&&(a=i.value);return a}});function we(a,e){let i=e.field(Q);if(!(i==null?void 0:i.re)||!a.visibleRanges.length)return M.Decoration.none;let s=new q.RangeSetBuilder,l=a.visibleRanges[a.visibleRanges.length-1],t=(0,B.ensureSyntaxTree)(e,l.to,50),{doc:c}=e,r=-1;for(let{from:o,to:d}of a.visibleRanges)t.iterate({from:o,to:d,enter({type:n,from:u,to:m}){if(u<=r)return;let h=n.prop(B.tokenClassNodeProp);if(h&&/formatting-list/.test(h)){let{from:g,to:f,text:w}=c.lineAt(u),y=w.match(i.re),k=y?i.callouts[y[2]]:null;if(r=f,k){let C=g+y[1].length;s.add(g,g,ei(k.char,k.color)),s.add(g,g,M.Decoration.widget({widget:new be,side:-1})),s.add(C,C+k.char.length,M.Decoration.replace({widget:new ye(k.char,k.icon)}))}}}});return s.finish()}var ke=M.ViewPlugin.fromClass(class{constructor(a){this.decorations=we(a,a.state)}update(a){(a.docChanged||a.viewportChanged||a.transactions.some(e=>e.effects.some(i=>i.is(W))))&&(this.decorations=we(a.view,a.state))}},{decorations:a=>a.decorations});var ve=j(require("obsidian"));function ii(a){var e;for(let i of Array.from(a.childNodes)){if(i.nodeType===document.ELEMENT_NODE&&i.classList.contains("tasks-list-text")){let s=i.firstElementChild;if(s==null?void 0:s.classList.contains("task-description")){let l=(e=s.firstElementChild)==null?void 0:e.firstChild;if(l.nodeType===document.TEXT_NODE)return l}}if(i.nodeType===document.ELEMENT_NODE&&i.tagName==="P")return i.firstChild;if(i.nodeType===document.TEXT_NODE&&i.nodeValue.trim()!=="")return i}return null}function ai(a){let e=[],i=null;for(let l=0,t=a.childNodes.length;l<t;l++){let c=a.childNodes.item(l);if(c.nodeType===document.ELEMENT_NODE){let r=c;if(r.hasClass("list-collapse-indicator")||r.hasClass("list-bullet"))continue;if(["UL","OL"].includes(r.tagName)){i=c;break}}e.push(c)}let s=createSpan({cls:"lc-li-wrapper"});e.forEach(l=>s.append(l)),i?i.before(s):a.append(s)}function xe(a){return(e,i)=>P(this,null,function*(){var l;let s=a();((l=i.promises)==null?void 0:l.length)&&(yield Promise.all(i.promises)),e.findAll("li").forEach(t=>{let c=ii(t);if(!c)return;let r=c.textContent;if(!r)return;let o=r.match(s.re),d=o?s.callouts[o[1]]:null;d&&(t.addClass("lc-list-callout"),t.setAttribute("data-callout",d.char),t.style.setProperty("--lc-callout-color",d.color),c.replaceWith(createFragment(n=>{n.append(createSpan({cls:"lc-list-marker",text:r.slice(0,d.char.length)},u=>{d.icon&&(0,ve.setIcon)(u,d.icon)})),n.append(r.slice(d.char.length))})),ai(t))})})}var b=j(require("obsidian"));function z(a){return Array.isArray?Array.isArray(a):Me(a)==="[object Array]"}var si=1/0;function li(a){if(typeof a=="string")return a;let e=a+"";return e=="0"&&1/a==-si?"-0":e}function ti(a){return a==null?"":li(a)}function S(a){return typeof a=="string"}function Ee(a){return typeof a=="number"}function ci(a){return a===!0||a===!1||ri(a)&&Me(a)=="[object Boolean]"}function Ce(a){return typeof a=="object"}function ri(a){return Ce(a)&&a!==null}function v(a){return a!=null}function J(a){return!a.trim().length}function Me(a){return a==null?a===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(a)}var di="Incorrect 'index' type",oi=a=>`Invalid value for key ${a}`,ni=a=>`Pattern length exceeds max of ${a}.`,ui=a=>`Missing ${a} property in key`,hi=a=>`Property 'weight' in key '${a}' must be a positive integer`,Se=Object.prototype.hasOwnProperty,ze=class{constructor(e){this._keys=[],this._keyMap={};let i=0;e.forEach(s=>{let l=Le(s);i+=l.weight,this._keys.push(l),this._keyMap[l.id]=l,i+=l.weight}),this._keys.forEach(s=>{s.weight/=i})}get(e){return this._keyMap[e]}keys(){return this._keys}toJSON(){return JSON.stringify(this._keys)}};function Le(a){let e=null,i=null,s=null,l=1,t=null;if(S(a)||z(a))s=a,e=_e(a),i=Z(a);else{if(!Se.call(a,"name"))throw new Error(ui("name"));let c=a.name;if(s=c,Se.call(a,"weight")&&(l=a.weight,l<=0))throw new Error(hi(c));e=_e(c),i=Z(c),t=a.getFn}return{path:e,id:i,weight:l,src:s,getFn:t}}function _e(a){return z(a)?a:a.split(".")}function Z(a){return z(a)?a.join("."):a}function pi(a,e){let i=[],s=!1,l=(t,c,r)=>{if(!!v(t))if(!c[r])i.push(t);else{let o=c[r],d=t[o];if(!v(d))return;if(r===c.length-1&&(S(d)||Ee(d)||ci(d)))i.push(ti(d));else if(z(d)){s=!0;for(let n=0,u=d.length;n<u;n+=1)l(d[n],c,r+1)}else c.length&&l(d,c,r+1)}};return l(a,S(e)?e.split("."):e,0),s?i:i[0]}var mi={includeMatches:!1,findAllMatches:!1,minMatchCharLength:1},gi={isCaseSensitive:!1,includeScore:!1,keys:[],shouldSort:!0,sortFn:(a,e)=>a.score===e.score?a.idx<e.idx?-1:1:a.score<e.score?-1:1},fi={location:0,threshold:.6,distance:100},bi={useExtendedSearch:!1,getFn:pi,ignoreLocation:!1,ignoreFieldNorm:!1,fieldNormWeight:1},p=R(R(R(R({},gi),mi),fi),bi),yi=/[^ ]+/g;function wi(a=1,e=3){let i=new Map,s=Math.pow(10,e);return{get(l){let t=l.match(yi).length;if(i.has(t))return i.get(t);let c=1/Math.pow(t,.5*a),r=parseFloat(Math.round(c*s)/s);return i.set(t,r),r},clear(){i.clear()}}}var U=class{constructor({getFn:e=p.getFn,fieldNormWeight:i=p.fieldNormWeight}={}){this.norm=wi(i,3),this.getFn=e,this.isCreated=!1,this.setIndexRecords()}setSources(e=[]){this.docs=e}setIndexRecords(e=[]){this.records=e}setKeys(e=[]){this.keys=e,this._keysMap={},e.forEach((i,s)=>{this._keysMap[i.id]=s})}create(){this.isCreated||!this.docs.length||(this.isCreated=!0,S(this.docs[0])?this.docs.forEach((e,i)=>{this._addString(e,i)}):this.docs.forEach((e,i)=>{this._addObject(e,i)}),this.norm.clear())}add(e){let i=this.size();S(e)?this._addString(e,i):this._addObject(e,i)}removeAt(e){this.records.splice(e,1);for(let i=e,s=this.size();i<s;i+=1)this.records[i].i-=1}getValueForItemAtKeyId(e,i){return e[this._keysMap[i]]}size(){return this.records.length}_addString(e,i){if(!v(e)||J(e))return;let s={v:e,i,n:this.norm.get(e)};this.records.push(s)}_addObject(e,i){let s={i,$:{}};this.keys.forEach((l,t)=>{let c=l.getFn?l.getFn(e):this.getFn(e,l.path);if(!!v(c)){if(z(c)){let r=[],o=[{nestedArrIndex:-1,value:c}];for(;o.length;){let{nestedArrIndex:d,value:n}=o.pop();if(!!v(n))if(S(n)&&!J(n)){let u={v:n,i:d,n:this.norm.get(n)};r.push(u)}else z(n)&&n.forEach((u,m)=>{o.push({nestedArrIndex:m,value:u})})}s.$[t]=r}else if(S(c)&&!J(c)){let r={v:c,n:this.norm.get(c)};s.$[t]=r}}}),this.records.push(s)}toJSON(){return{keys:this.keys,records:this.records}}};function Te(a,e,{getFn:i=p.getFn,fieldNormWeight:s=p.fieldNormWeight}={}){let l=new U({getFn:i,fieldNormWeight:s});return l.setKeys(a.map(Le)),l.setSources(e),l.create(),l}function ki(a,{getFn:e=p.getFn,fieldNormWeight:i=p.fieldNormWeight}={}){let{keys:s,records:l}=a,t=new U({getFn:e,fieldNormWeight:i});return t.setKeys(s),t.setIndexRecords(l),t}function K(a,{errors:e=0,currentLocation:i=0,expectedLocation:s=0,distance:l=p.distance,ignoreLocation:t=p.ignoreLocation}={}){let c=e/a.length;if(t)return c;let r=Math.abs(s-i);return l?c+r/l:r?1:c}function vi(a=[],e=p.minMatchCharLength){let i=[],s=-1,l=-1,t=0;for(let c=a.length;t<c;t+=1){let r=a[t];r&&s===-1?s=t:!r&&s!==-1&&(l=t-1,l-s+1>=e&&i.push([s,l]),s=-1)}return a[t-1]&&t-s>=e&&i.push([s,t-1]),i}var A=32;function xi(a,e,i,{location:s=p.location,distance:l=p.distance,threshold:t=p.threshold,findAllMatches:c=p.findAllMatches,minMatchCharLength:r=p.minMatchCharLength,includeMatches:o=p.includeMatches,ignoreLocation:d=p.ignoreLocation}={}){if(e.length>A)throw new Error(ni(A));let n=e.length,u=a.length,m=Math.max(0,Math.min(s,u)),h=t,g=m,f=r>1||o,w=f?Array(u):[],y;for(;(y=a.indexOf(e,g))>-1;){let x=K(e,{currentLocation:y,expectedLocation:m,distance:l,ignoreLocation:d});if(h=Math.min(x,h),g=y+n,f){let _=0;for(;_<n;)w[y+_]=1,_+=1}}g=-1;let k=[],C=1,F=n+u,Ue=1<<n-1;for(let x=0;x<n;x+=1){let _=0,T=F;for(;_<T;)K(e,{errors:x,currentLocation:m+T,expectedLocation:m,distance:l,ignoreLocation:d})<=h?_=T:F=T,T=Math.floor((F-_)/2+_);F=T;let ne=Math.max(1,m-T+1),X=c?u:Math.min(m+T,u)+n,N=Array(X+2);N[X+1]=(1<<x)-1;for(let E=X;E>=ne;E-=1){let V=E-1,ue=i[a.charAt(V)];if(f&&(w[V]=+!!ue),N[E]=(N[E+1]<<1|1)&ue,x&&(N[E]|=(k[E+1]|k[E])<<1|1|k[E+1]),N[E]&Ue&&(C=K(e,{errors:x,currentLocation:V,expectedLocation:m,distance:l,ignoreLocation:d}),C<=h)){if(h=C,g=V,g<=m)break;ne=Math.max(1,2*m-g)}}if(K(e,{errors:x+1,currentLocation:m,expectedLocation:m,distance:l,ignoreLocation:d})>h)break;k=N}let Y={isMatch:g>=0,score:Math.max(.001,C)};if(f){let x=vi(w,r);x.length?o&&(Y.indices=x):Y.isMatch=!1}return Y}function Ei(a){let e={};for(let i=0,s=a.length;i<s;i+=1){let l=a.charAt(i);e[l]=(e[l]||0)|1<<s-i-1}return e}var ee=class{constructor(e,{location:i=p.location,threshold:s=p.threshold,distance:l=p.distance,includeMatches:t=p.includeMatches,findAllMatches:c=p.findAllMatches,minMatchCharLength:r=p.minMatchCharLength,isCaseSensitive:o=p.isCaseSensitive,ignoreLocation:d=p.ignoreLocation}={}){if(this.options={location:i,threshold:s,distance:l,includeMatches:t,findAllMatches:c,minMatchCharLength:r,isCaseSensitive:o,ignoreLocation:d},this.pattern=o?e:e.toLowerCase(),this.chunks=[],!this.pattern.length)return;let n=(m,h)=>{this.chunks.push({pattern:m,alphabet:Ei(m),startIndex:h})},u=this.pattern.length;if(u>A){let m=0,h=u%A,g=u-h;for(;m<g;)n(this.pattern.substr(m,A),m),m+=A;if(h){let f=u-A;n(this.pattern.substr(f),f)}}else n(this.pattern,0)}searchIn(e){let{isCaseSensitive:i,includeMatches:s}=this.options;if(i||(e=e.toLowerCase()),this.pattern===e){let g={isMatch:!0,score:0};return s&&(g.indices=[[0,e.length-1]]),g}let{location:l,distance:t,threshold:c,findAllMatches:r,minMatchCharLength:o,ignoreLocation:d}=this.options,n=[],u=0,m=!1;this.chunks.forEach(({pattern:g,alphabet:f,startIndex:w})=>{let{isMatch:y,score:k,indices:C}=xi(e,g,f,{location:l+w,distance:t,threshold:c,findAllMatches:r,minMatchCharLength:o,includeMatches:s,ignoreLocation:d});y&&(m=!0),u+=k,y&&C&&(n=[...n,...C])});let h={isMatch:m,score:m?u/this.chunks.length:1};return m&&s&&(h.indices=n),h}},L=class{constructor(e){this.pattern=e}static isMultiMatch(e){return je(e,this.multiRegex)}static isSingleMatch(e){return je(e,this.singleRegex)}search(){}};function je(a,e){let i=a.match(e);return i?i[1]:null}var Ie=class extends L{constructor(e){super(e)}static get type(){return"exact"}static get multiRegex(){return/^="(.*)"$/}static get singleRegex(){return/^=(.*)$/}search(e){let i=e===this.pattern;return{isMatch:i,score:i?0:1,indices:[0,this.pattern.length-1]}}},Re=class extends L{constructor(e){super(e)}static get type(){return"inverse-exact"}static get multiRegex(){return/^!"(.*)"$/}static get singleRegex(){return/^!(.*)$/}search(e){let s=e.indexOf(this.pattern)===-1;return{isMatch:s,score:s?0:1,indices:[0,e.length-1]}}},Ae=class extends L{constructor(e){super(e)}static get type(){return"prefix-exact"}static get multiRegex(){return/^\^"(.*)"$/}static get singleRegex(){return/^\^(.*)$/}search(e){let i=e.startsWith(this.pattern);return{isMatch:i,score:i?0:1,indices:[0,this.pattern.length-1]}}},Ne=class extends L{constructor(e){super(e)}static get type(){return"inverse-prefix-exact"}static get multiRegex(){return/^!\^"(.*)"$/}static get singleRegex(){return/^!\^(.*)$/}search(e){let i=!e.startsWith(this.pattern);return{isMatch:i,score:i?0:1,indices:[0,e.length-1]}}},Pe=class extends L{constructor(e){super(e)}static get type(){return"suffix-exact"}static get multiRegex(){return/^"(.*)"\$$/}static get singleRegex(){return/^(.*)\$$/}search(e){let i=e.endsWith(this.pattern);return{isMatch:i,score:i?0:1,indices:[e.length-this.pattern.length,e.length-1]}}},qe=class extends L{constructor(e){super(e)}static get type(){return"inverse-suffix-exact"}static get multiRegex(){return/^!"(.*)"\$$/}static get singleRegex(){return/^!(.*)\$$/}search(e){let i=!e.endsWith(this.pattern);return{isMatch:i,score:i?0:1,indices:[0,e.length-1]}}},ie=class extends L{constructor(e,{location:i=p.location,threshold:s=p.threshold,distance:l=p.distance,includeMatches:t=p.includeMatches,findAllMatches:c=p.findAllMatches,minMatchCharLength:r=p.minMatchCharLength,isCaseSensitive:o=p.isCaseSensitive,ignoreLocation:d=p.ignoreLocation}={}){super(e);this._bitapSearch=new ee(e,{location:i,threshold:s,distance:l,includeMatches:t,findAllMatches:c,minMatchCharLength:r,isCaseSensitive:o,ignoreLocation:d})}static get type(){return"fuzzy"}static get multiRegex(){return/^"(.*)"$/}static get singleRegex(){return/^(.*)$/}search(e){return this._bitapSearch.searchIn(e)}},ae=class extends L{constructor(e){super(e)}static get type(){return"include"}static get multiRegex(){return/^'"(.*)"$/}static get singleRegex(){return/^'(.*)$/}search(e){let i=0,s,l=[],t=this.pattern.length;for(;(s=e.indexOf(this.pattern,i))>-1;)i=s+t,l.push([s,i-1]);let c=!!l.length;return{isMatch:c,score:c?0:1,indices:l}}},se=[Ie,ae,Ae,Ne,qe,Pe,Re,ie],De=se.length,Ci=/ +(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)/,Mi="|";function Si(a,e={}){return a.split(Mi).map(i=>{let s=i.trim().split(Ci).filter(t=>t&&!!t.trim()),l=[];for(let t=0,c=s.length;t<c;t+=1){let r=s[t],o=!1,d=-1;for(;!o&&++d<De;){let n=se[d],u=n.isMultiMatch(r);u&&(l.push(new n(u,e)),o=!0)}if(!o)for(d=-1;++d<De;){let n=se[d],u=n.isSingleMatch(r);if(u){l.push(new n(u,e));break}}}return l})}var zi=new Set([ie.type,ae.type]),$e=class{constructor(e,{isCaseSensitive:i=p.isCaseSensitive,includeMatches:s=p.includeMatches,minMatchCharLength:l=p.minMatchCharLength,ignoreLocation:t=p.ignoreLocation,findAllMatches:c=p.findAllMatches,location:r=p.location,threshold:o=p.threshold,distance:d=p.distance}={}){this.query=null,this.options={isCaseSensitive:i,includeMatches:s,minMatchCharLength:l,findAllMatches:c,ignoreLocation:t,location:r,threshold:o,distance:d},this.pattern=i?e:e.toLowerCase(),this.query=Si(this.pattern,this.options)}static condition(e,i){return i.useExtendedSearch}searchIn(e){let i=this.query;if(!i)return{isMatch:!1,score:1};let{includeMatches:s,isCaseSensitive:l}=this.options;e=l?e:e.toLowerCase();let t=0,c=[],r=0;for(let o=0,d=i.length;o<d;o+=1){let n=i[o];c.length=0,t=0;for(let u=0,m=n.length;u<m;u+=1){let h=n[u],{isMatch:g,indices:f,score:w}=h.search(e);if(g){if(t+=1,r+=w,s){let y=h.constructor.type;zi.has(y)?c=[...c,...f]:c.push(f)}}else{r=0,t=0,c.length=0;break}}if(t){let u={isMatch:!0,score:r/t};return s&&(u.indices=c),u}}return{isMatch:!1,score:1}}},le=[];function Li(...a){le.push(...a)}function te(a,e){for(let i=0,s=le.length;i<s;i+=1){let l=le[i];if(l.condition(a,e))return new l(a,e)}return new ee(a,e)}var G={AND:"$and",OR:"$or"},ce={PATH:"$path",PATTERN:"$val"},re=a=>!!(a[G.AND]||a[G.OR]),_i=a=>!!a[ce.PATH],Ti=a=>!z(a)&&Ce(a)&&!re(a),Oe=a=>({[G.AND]:Object.keys(a).map(e=>({[e]:a[e]}))});function Fe(a,e,{auto:i=!0}={}){let s=l=>{let t=Object.keys(l),c=_i(l);if(!c&&t.length>1&&!re(l))return s(Oe(l));if(Ti(l)){let o=c?l[ce.PATH]:t[0],d=c?l[ce.PATTERN]:l[o];if(!S(d))throw new Error(oi(o));let n={keyId:Z(o),pattern:d};return i&&(n.searcher=te(d,e)),n}let r={children:[],operator:t[0]};return t.forEach(o=>{let d=l[o];z(d)&&d.forEach(n=>{r.children.push(s(n))})}),r};return re(a)||(a=Oe(a)),s(a)}function ji(a,{ignoreFieldNorm:e=p.ignoreFieldNorm}){a.forEach(i=>{let s=1;i.matches.forEach(({key:l,norm:t,score:c})=>{let r=l?l.weight:null;s*=Math.pow(c===0&&r?Number.EPSILON:c,(r||1)*(e?1:t))}),i.score=s})}function Ii(a,e){let i=a.matches;e.matches=[],!!v(i)&&i.forEach(s=>{if(!v(s.indices)||!s.indices.length)return;let{indices:l,value:t}=s,c={indices:l,value:t};s.key&&(c.key=s.key.src),s.idx>-1&&(c.refIndex=s.idx),e.matches.push(c)})}function Ri(a,e){e.score=a.score}function Ai(a,e,{includeMatches:i=p.includeMatches,includeScore:s=p.includeScore}={}){let l=[];return i&&l.push(Ii),s&&l.push(Ri),a.map(t=>{let{idx:c}=t,r={item:e[c],refIndex:c};return l.length&&l.forEach(o=>{o(t,r)}),r})}var I=class{constructor(e,i={},s){this.options=R(R({},p),i),this.options.useExtendedSearch,this._keyStore=new ze(this.options.keys),this.setCollection(e,s)}setCollection(e,i){if(this._docs=e,i&&!(i instanceof U))throw new Error(di);this._myIndex=i||Te(this.options.keys,this._docs,{getFn:this.options.getFn,fieldNormWeight:this.options.fieldNormWeight})}add(e){!v(e)||(this._docs.push(e),this._myIndex.add(e))}remove(e=()=>!1){let i=[];for(let s=0,l=this._docs.length;s<l;s+=1){let t=this._docs[s];e(t,s)&&(this.removeAt(s),s-=1,l-=1,i.push(t))}return i}removeAt(e){this._docs.splice(e,1),this._myIndex.removeAt(e)}getIndex(){return this._myIndex}search(e,{limit:i=-1}={}){let{includeMatches:s,includeScore:l,shouldSort:t,sortFn:c,ignoreFieldNorm:r}=this.options,o=S(e)?S(this._docs[0])?this._searchStringList(e):this._searchObjectList(e):this._searchLogical(e);return ji(o,{ignoreFieldNorm:r}),t&&o.sort(c),Ee(i)&&i>-1&&(o=o.slice(0,i)),Ai(o,this._docs,{includeMatches:s,includeScore:l})}_searchStringList(e){let i=te(e,this.options),{records:s}=this._myIndex,l=[];return s.forEach(({v:t,i:c,n:r})=>{if(!v(t))return;let{isMatch:o,score:d,indices:n}=i.searchIn(t);o&&l.push({item:t,idx:c,matches:[{score:d,value:t,norm:r,indices:n}]})}),l}_searchLogical(e){let i=Fe(e,this.options),s=(r,o,d)=>{if(!r.children){let{keyId:u,searcher:m}=r,h=this._findMatches({key:this._keyStore.get(u),value:this._myIndex.getValueForItemAtKeyId(o,u),searcher:m});return h&&h.length?[{idx:d,item:o,matches:h}]:[]}let n=[];for(let u=0,m=r.children.length;u<m;u+=1){let h=r.children[u],g=s(h,o,d);if(g.length)n.push(...g);else if(r.operator===G.AND)return[]}return n},l=this._myIndex.records,t={},c=[];return l.forEach(({$:r,i:o})=>{if(v(r)){let d=s(i,r,o);d.length&&(t[o]||(t[o]={idx:o,item:r,matches:[]},c.push(t[o])),d.forEach(({matches:n})=>{t[o].matches.push(...n)}))}}),c}_searchObjectList(e){let i=te(e,this.options),{keys:s,records:l}=this._myIndex,t=[];return l.forEach(({$:c,i:r})=>{if(!v(c))return;let o=[];s.forEach((d,n)=>{o.push(...this._findMatches({key:d,value:c[n],searcher:i}))}),o.length&&t.push({idx:r,item:c,matches:o})}),t}_findMatches({key:e,value:i,searcher:s}){if(!v(i))return[];let l=[];if(z(i))i.forEach(({v:t,i:c,n:r})=>{if(!v(t))return;let{isMatch:o,score:d,indices:n}=s.searchIn(t);o&&l.push({score:d,key:e,value:t,idx:c,norm:r,indices:n})});else{let{v:t,n:c}=i,{isMatch:r,score:o,indices:d}=s.searchIn(t);r&&l.push({score:o,key:e,value:t,norm:c,indices:d})}return l}};I.version="6.6.2";I.createIndex=Te;I.parseIndex=ki;I.config=p;I.parseQuery=Fe;Li($e);var Ve=j(require("obsidian")),He=[{id:"lucide-accessibility",aliases:["disability","disabled","dda","wheelchair"]},{id:"lucide-activity",aliases:["pulse","health","action","motion"]},{id:"lucide-air-vent",aliases:["air conditioner","ac","central air","cooling","climate-control"]},{id:"lucide-airplay",aliases:["stream","cast","mirroring"]},{id:"lucide-alarm-check",aliases:["done","todo","tick","complete","task"]},{id:"lucide-alarm-clock-off",aliases:["morning","turn-off"]},{id:"lucide-alarm-clock",aliases:["morning"]},{id:"lucide-alarm-minus",aliases:["remove"]},{id:"lucide-alarm-plus",aliases:["add"]},{id:"lucide-album",aliases:["photo","book"]},{id:"lucide-alert-circle",aliases:["warning","alert","danger","exclamation mark"]},{id:"lucide-alert-octagon",aliases:["warning","alert","danger","exclamation mark"]},{id:"lucide-alert-triangle",aliases:["warning","alert","danger","exclamation mark"]},{id:"lucide-align-center-horizontal",aliases:["items","flex","justify"]},{id:"lucide-align-center-vertical",aliases:["items","flex","justify"]},{id:"lucide-align-center",aliases:["text","alignment","center"]},{id:"lucide-align-end-horizontal",aliases:["items","bottom","flex","justify"]},{id:"lucide-align-end-vertical",aliases:["items","right","flex","justify"]},{id:"lucide-align-horizontal-distribute-center",aliases:["items","flex","justify","space","evenly","around"]},{id:"lucide-align-horizontal-distribute-end",aliases:["right","items","flex","justify"]},{id:"lucide-align-horizontal-distribute-start",aliases:["left","items","flex","justify"]},{id:"lucide-align-horizontal-justify-center",aliases:["center","items","flex","justify"]},{id:"lucide-align-horizontal-justify-end",aliases:["right","items","flex","justify"]},{id:"lucide-align-horizontal-justify-start",aliases:["left","items","flex","justify"]},{id:"lucide-align-horizontal-space-around",aliases:["center","items","flex","justify","distribute","between"]},{id:"lucide-align-horizontal-space-between",aliases:["around","items","bottom","flex","justify"]},{id:"lucide-align-justify",aliases:["text","alignment","justified"]},{id:"lucide-align-left",aliases:["text","alignment","left"]},{id:"lucide-align-right",aliases:["text","alignment","right"]},{id:"lucide-align-start-horizontal",aliases:["top","items","flex","justify"]},{id:"lucide-align-start-vertical",aliases:["left","items","flex","justify"]},{id:"lucide-align-vertical-distribute-center",aliases:["items","flex","justify","space","evenly","around"]},{id:"lucide-align-vertical-distribute-end",aliases:["bottom","items","flex","justify"]},{id:"lucide-align-vertical-distribute-start",aliases:["top","items","flex","justify"]},{id:"lucide-align-vertical-justify-center",aliases:["center","items","flex","justify","distribute","between"]},{id:"lucide-align-vertical-justify-end",aliases:["bottom","items","flex","justify","distribute","between"]},{id:"lucide-align-vertical-justify-start",aliases:["top","items","flex","justify","distribute","between"]},{id:"lucide-align-vertical-space-around",aliases:["center","items","flex","justify","distribute","between"]},{id:"lucide-align-vertical-space-between",aliases:["center","items","flex","justify","distribute","between"]},{id:"lucide-anchor",aliases:["ship"]},{id:"lucide-angry",aliases:["emoji","anger","face","emotion"]},{id:"lucide-annoyed",aliases:["emoji","nuisance","face","emotion"]},{id:"lucide-aperture",aliases:["camera","photo"]},{id:"lucide-apple",aliases:["fruit","food"]},{id:"lucide-archive-restore",aliases:["archive","unarchive","restore","index","box"]},{id:"lucide-archive",aliases:["index","box"]},{id:"lucide-armchair",aliases:["sofa","furniture","leisure","lounge","loveseat","couch"]},{id:"lucide-arrow-big-down",aliases:["key"]},{id:"lucide-arrow-big-left",aliases:["key"]},{id:"lucide-arrow-big-right",aliases:["key","forward"]},{id:"lucide-arrow-big-up",aliases:["key","forward"]},{id:"lucide-arrow-down-circle",aliases:["direction"]},{id:"lucide-arrow-down-left-from-circle",aliases:["direction"]},{id:"lucide-arrow-down-left",aliases:["direction"]},{id:"lucide-arrow-down-right-from-circle",aliases:["direction"]},{id:"lucide-arrow-down-right",aliases:["direction"]},{id:"lucide-arrow-down",aliases:["direction"]},{id:"lucide-arrow-left-circle",aliases:["direction"]},{id:"lucide-arrow-left-right",aliases:["bidirectional","direction","swap","switch","transaction","reorder","move"]},{id:"lucide-arrow-left",aliases:["direction"]},{id:"lucide-arrow-right-circle",aliases:["direction"]},{id:"lucide-arrow-right",aliases:["direction"]},{id:"lucide-arrow-up-circle",aliases:["direction"]},{id:"lucide-arrow-up-down",aliases:["bidirectional","direction","swap","switch","network","mobile data","internet","reorder","move"]},{id:"lucide-arrow-up-left-from-circle",aliases:["direction","keyboard","key","escape","button"]},{id:"lucide-arrow-up-left",aliases:["direction"]},{id:"lucide-arrow-up-right-from-circle",aliases:["direction"]},{id:"lucide-arrow-up-right",aliases:["direction"]},{id:"lucide-arrow-up",aliases:["direction"]},{id:"lucide-asterisk",aliases:["reference"]},{id:"lucide-at-sign",aliases:["mention","at","email","message"]},{id:"lucide-atom",aliases:["atomic","nuclear","physics","particle","element","molecule"]},{id:"lucide-award",aliases:["achievement","badge"]},{id:"lucide-axe",aliases:["hatchet"]},{id:"lucide-axis-3d",aliases:["gizmo","coordinates"]},{id:"lucide-baby",aliases:["child","childproof","children"]},{id:"lucide-backpack",aliases:["bag","hiking","travel","camping","school","childhood"]},{id:"lucide-baggage-claim",aliases:["baggage","luggage","travel","cart","trolley","suitcase"]},{id:"lucide-ban",aliases:["cancel","no","stop","forbidden","prohibited","error","slash"]},{id:"lucide-banana",aliases:["fruit","food"]},{id:"lucide-banknote",aliases:["currency","money","payment"]},{id:"lucide-bar-chart-2",aliases:["statistics","diagram","graph"]},{id:"lucide-bar-chart-3",aliases:["statistics","diagram","graph"]},{id:"lucide-bar-chart-4",aliases:["statistics","diagram","graph"]},{id:"lucide-bar-chart-horizontal",aliases:["statistics","diagram","graph"]},{id:"lucide-bar-chart",aliases:["statistics","diagram","graph"]},{id:"lucide-baseline",aliases:["text","format","color"]},{id:"lucide-bath",aliases:["amenities","services","bathroom","shower"]},{id:"lucide-battery-charging",aliases:["power","electricity","accumulator","charge"]},{id:"lucide-battery-full",aliases:["power","electricity","accumulator","charge"]},{id:"lucide-battery-low",aliases:["power","electricity","accumulator","charge"]},{id:"lucide-battery-medium",aliases:["power","electricity","accumulator","charge"]},{id:"lucide-battery-warning",aliases:["power","electricity","accumulator","charge","exclamation mark"]},{id:"lucide-battery",aliases:["power","electricity","accumulator","charge"]},{id:"lucide-beaker",aliases:["cup","lab","chemistry","experiment","test"]},{id:"lucide-bean-off",aliases:["soy free","legume","soy","food","seed","allergy","intolerance","diet"]},{id:"lucide-bean",aliases:["legume","soy","food","seed"]},{id:"lucide-bed-double",aliases:["sleep","hotel","furniture"]},{id:"lucide-bed-single",aliases:["sleep","hotel","furniture"]},{id:"lucide-bed",aliases:["sleep","hotel","furniture"]},{id:"lucide-beef",aliases:["food","dish","restaurant","course","meal","meat","bbq","steak"]},{id:"lucide-beer",aliases:["alcohol","bar","beverage","brewery","drink"]},{id:"lucide-bell-minus",aliases:["alarm","notification","silent","reminder","delete","remove","erase"]},{id:"lucide-bell-off",aliases:["alarm","notification","silent","reminder"]},{id:"lucide-bell-plus",aliases:["notification","silent","reminder","add","create","new"]},{id:"lucide-bell-ring",aliases:["alarm","notification","sound","reminder"]},{id:"lucide-bell",aliases:["alarm","notification","sound","reminder"]},{id:"lucide-bike",aliases:["bicycle","transport","trip"]},{id:"lucide-binary",aliases:["code","digits","computer","zero","one"]},{id:"lucide-bird",aliases:["peace","freedom","wing","avian"]},{id:"lucide-bitcoin",aliases:["currency","money","payment"]},{id:"lucide-blinds",aliases:["shades","screen","curtain","shutter","roller blind","window","lighting","household","home"]},{id:"lucide-bluetooth-connected",aliases:["paired"]},{id:"lucide-bluetooth-off",aliases:["lost"]},{id:"lucide-bluetooth-searching",aliases:["pairing"]},{id:"lucide-bluetooth",aliases:["wireless"]},{id:"lucide-bold",aliases:["text","strong","format"]},{id:"lucide-bomb",aliases:["fatal","error","crash","blockbuster","mine","explosion","explode","explosive"]},{id:"lucide-bone",aliases:["medical","health","death","pet","gaming"]},{id:"lucide-book-copy",aliases:["read","dictionary","booklet","library","code","version control","git","repository","clone"]},{id:"lucide-book-down",aliases:["code","version control","git","repository","pull"]},{id:"lucide-book-key",aliases:["code","version control","git","repository","private"]},{id:"lucide-book-lock",aliases:["code","version control","git","repository","private"]},{id:"lucide-book-marked",aliases:["read","dictionary","booklet","library","code","version control","git","repository"]},{id:"lucide-book-minus",aliases:["code","version control","git","repository","remove","delete"]},{id:"lucide-book-open-check",aliases:["read","library","plain language","done","todo","tick","complete","task"]},{id:"lucide-book-open",aliases:["read","library"]},{id:"lucide-book-plus",aliases:["code","version control","git","repository","add"]},{id:"lucide-book-template",aliases:["read","code","version control","git","repository","dashed"]},{id:"lucide-book-up",aliases:["code","version control","git","repository","push"]},{id:"lucide-book-x",aliases:["code","version control","git","repository","remove","delete"]},{id:"lucide-book",aliases:["read","dictionary","booklet","magazine","library"]},{id:"lucide-bookmark-minus",aliases:["delete","remove"]},{id:"lucide-bookmark-plus",aliases:["add"]},{id:"lucide-bookmark",aliases:["read","clip","marker","tag"]},{id:"lucide-bot",aliases:["robot","ai"]},{id:"lucide-box-select",aliases:["selection","square","rectangular","marquee","tool"]},{id:"lucide-box",aliases:["cube","package"]},{id:"lucide-boxes",aliases:["cubes","packages","parts","group","units","collection","cluster"]},{id:"lucide-brain-circuit",aliases:["mind","intellect","artificial intelligence","ai","deep learning","machine learning","computing"]},{id:"lucide-brain-cog",aliases:["mind","intellect","artificial intelligence","ai","deep learning","machine learning","computing"]},{id:"lucide-brain",aliases:["medical","mind","intellect","cerebral","consciousness","genius","artificial intelligence","ai"]},{id:"lucide-briefcase",aliases:["work","bag","baggage","folder"]},{id:"lucide-brush",aliases:["draw","paint","color"]},{id:"lucide-bug",aliases:["issue","report","insect"]},{id:"lucide-building-2",aliases:["business","company","enterprise","skyscraper","organisation","organization"]},{id:"lucide-building",aliases:["organisation","organization"]},{id:"lucide-bus",aliases:["bus","vehicle","transport","trip"]},{id:"lucide-cake",aliases:["birthday","birthdate","celebration","party"]},{id:"lucide-calculator",aliases:["count","calculating machine"]},{id:"lucide-calendar-check-2",aliases:["date","time","event","confirm","subscribe","done","todo","tick","complete","task"]},{id:"lucide-calendar-check",aliases:["date","time","event","confirm","subscribe","schedule","done","todo","tick","complete","task"]},{id:"lucide-calendar-clock",aliases:["date","time","event","clock"]},{id:"lucide-calendar-days",aliases:["date","time","event"]},{id:"lucide-calendar-heart",aliases:["date","time","event","heart","favourite","subscribe"]},{id:"lucide-calendar-minus",aliases:["date","time","event","delete","remove"]},{id:"lucide-calendar-off",aliases:["date","time","event","delete","remove"]},{id:"lucide-calendar-plus",aliases:["date","time","event","add","subscribe","create","new"]},{id:"lucide-calendar-range",aliases:["date","time","event","range","period"]},{id:"lucide-calendar-search",aliases:["date","time","search","events"]},{id:"lucide-calendar-x-2",aliases:["date","time","event","remove"]},{id:"lucide-calendar-x",aliases:["date","time","event","remove","busy"]},{id:"lucide-calendar",aliases:["date","birthdate","birthday","time","event"]},{id:"lucide-camera-off",aliases:["photo","webcam","video"]},{id:"lucide-camera",aliases:["photo","webcam","video"]},{id:"lucide-candy-off",aliases:["sugar free","food","sweet","allergy","intolerance","diet"]},{id:"lucide-candy",aliases:["sugar","food","sweet"]},{id:"lucide-car",aliases:["vehicle","transport","trip"]},{id:"lucide-carrot",aliases:["vegetable","food","eat"]},{id:"lucide-case-lower",aliases:["text","letters","characters","font","typography"]},{id:"lucide-case-sensitive",aliases:["text","letters","characters","font","typography"]},{id:"lucide-case-upper",aliases:["text","letters","characters","font","typography"]},{id:"lucide-cast",aliases:["chromecast","airplay"]},{id:"lucide-castle",aliases:["fortress","stronghold","palace","chateau","building"]},{id:"lucide-cat",aliases:["animal","pet","kitten","feline"]},{id:"lucide-check-check",aliases:["done","received","double","todo","tick","complete","task"]},{id:"lucide-check-circle-2",aliases:["done","todo","tick","complete","task"]},{id:"lucide-check-circle",aliases:["done","todo","tick","complete","task"]},{id:"lucide-check-square",aliases:["done","todo","tick","complete","task"]},{id:"lucide-check",aliases:["done","todo","tick","complete","task"]},{id:"lucide-chef-hat",aliases:["cooking","food","kitchen","restaurant"]},{id:"lucide-cherry",aliases:["fruit","food"]},{id:"lucide-chevron-down-square",aliases:["arrow"]},{id:"lucide-chevron-down",aliases:["arrow"]},{id:"lucide-chevron-first",aliases:["arrow","previous","music"]},{id:"lucide-chevron-last",aliases:["arrow","skip","next","music"]},{id:"lucide-chevron-left-square",aliases:["arrow"]},{id:"lucide-chevron-left",aliases:["arrow"]},{id:"lucide-chevron-right-square",aliases:["code","command line","terminal","prompt","shell","console"]},{id:"lucide-chevron-right",aliases:["arrow","code","command line","terminal","prompt","shell"]},{id:"lucide-chevron-up-square",aliases:["arrow"]},{id:"lucide-chevron-up",aliases:["arrow"]},{id:"lucide-chevrons-down-up",aliases:["arrow","collapse","fold","vertical"]},{id:"lucide-chevrons-down",aliases:["arrow"]},{id:"lucide-chevrons-left-right",aliases:["arrow","expand","horizontal","unfold"]},{id:"lucide-chevrons-left",aliases:["arrow"]},{id:"lucide-chevrons-right-left",aliases:["arrow","collapse","fold","horizontal"]},{id:"lucide-chevrons-right",aliases:["arrow"]},{id:"lucide-chevrons-up-down",aliases:["arrow","expand","unfold","vertical"]},{id:"lucide-chevrons-up",aliases:["arrow"]},{id:"lucide-chrome",aliases:["browser","logo"]},{id:"lucide-church",aliases:["temple","building"]},{id:"lucide-cigarette-off",aliases:["smoking","no-smoking"]},{id:"lucide-cigarette",aliases:["smoking"]},{id:"lucide-circle-dot",aliases:["pending","dot","progress","issue"]},{id:"lucide-circle-ellipsis",aliases:["pending","ellipsis","progress"]},{id:"lucide-circle-equal",aliases:["calculate","maths","shape"]},{id:"lucide-circle-off",aliases:["diameter","zero","\xD8","null","nothing","cancel","ban","no","stop","forbidden","prohibited","error"]},{id:"lucide-circle-slash-2",aliases:["diameter","zero","\xD8","null","nothing","maths","circle-slashed"]},{id:"lucide-circle-slash",aliases:["diameter","zero","\xD8","null","nothing","cancel","ban","no","stop","forbidden","prohibited","error"]},{id:"lucide-circle",aliases:["off","zero","record","shape"]},{id:"lucide-circuit-board",aliases:["computing","electricity","electronics"]},{id:"lucide-citrus",aliases:["lemon","orange","grapefruit","fruit"]},{id:"lucide-clapperboard",aliases:["movie","film","video","camera","tv","television"]},{id:"lucide-clipboard-check",aliases:["copied","pasted","done","todo","tick","complete","task"]},{id:"lucide-clipboard-copy",aliases:["copy","paste"]},{id:"lucide-clipboard-edit",aliases:["edit","paste","signature"]},{id:"lucide-clipboard-list",aliases:["copy","paste","tasks"]},{id:"lucide-clipboard-paste",aliases:["copy","paste"]},{id:"lucide-clipboard-signature",aliases:["paste","signature"]},{id:"lucide-clipboard-type",aliases:["paste","format","text"]},{id:"lucide-clipboard-x",aliases:["copy","paste","discard","remove"]},{id:"lucide-clipboard",aliases:["copy","paste"]},{id:"lucide-clock-1",aliases:["time","watch","alarm"]},{id:"lucide-clock-10",aliases:["time","watch","alarm"]},{id:"lucide-clock-11",aliases:["time","watch","alarm"]},{id:"lucide-clock-12",aliases:["time","watch","alarm","noon","midnight"]},{id:"lucide-clock-2",aliases:["time","watch","alarm"]},{id:"lucide-clock-3",aliases:["time","watch","alarm"]},{id:"lucide-clock-4",aliases:["time","watch","alarm"]},{id:"lucide-clock-5",aliases:["time","watch","alarm"]},{id:"lucide-clock-6",aliases:["time","watch","alarm"]},{id:"lucide-clock-7",aliases:["time","watch","alarm"]},{id:"lucide-clock-8",aliases:["time","watch","alarm"]},{id:"lucide-clock-9",aliases:["time","watch","alarm"]},{id:"lucide-clock",aliases:["time","watch","alarm"]},{id:"lucide-cloud-cog",aliases:["computing","ai","cluster","network"]},{id:"lucide-cloud-drizzle",aliases:["weather","shower"]},{id:"lucide-cloud-fog",aliases:["weather","mist"]},{id:"lucide-cloud-hail",aliases:["weather","rainfall"]},{id:"lucide-cloud-lightning",aliases:["weather","bolt"]},{id:"lucide-cloud-moon-rain",aliases:["weather","partly","night","rainfall"]},{id:"lucide-cloud-moon",aliases:["weather","night"]},{id:"lucide-cloud-off",aliases:["disconnect"]},{id:"lucide-cloud-rain-wind",aliases:["weather","rainfall"]},{id:"lucide-cloud-rain",aliases:["weather","rainfall"]},{id:"lucide-cloud-snow",aliases:["weather","blizzard"]},{id:"lucide-cloud-sun-rain",aliases:["weather","partly","rainfall"]},{id:"lucide-cloud-sun",aliases:["weather","partly"]},{id:"lucide-cloud",aliases:["weather"]},{id:"lucide-cloudy",aliases:["weather","clouds"]},{id:"lucide-clover",aliases:["leaf","luck","plant"]},{id:"lucide-code-2",aliases:["source","programming","html","xml"]},{id:"lucide-code",aliases:["source","programming","html","xml"]},{id:"lucide-codepen",aliases:["logo"]},{id:"lucide-codesandbox",aliases:["logo"]},{id:"lucide-coffee",aliases:["drink","cup","mug","tea","cafe","hot","beverage"]},{id:"lucide-cog",aliases:["computing","settings","cog","edit","gear","preferences"]},{id:"lucide-coins",aliases:["money","cash","finance","gamble"]},{id:"lucide-columns",aliases:["layout"]},{id:"lucide-command",aliases:["keyboard","cmd","terminal","prompt"]},{id:"lucide-compass",aliases:["navigation","safari","travel","direction"]},{id:"lucide-component",aliases:["design","element","group","module","part","symbol"]},{id:"lucide-concierge-bell",aliases:["reception","bell","porter"]},{id:"lucide-construction",aliases:["roadwork","maintenance","blockade","barricade"]},{id:"lucide-contact",aliases:["person","user"]},{id:"lucide-contrast",aliases:["display","accessibility"]},{id:"lucide-cookie",aliases:["biscuit","privacy","legal","food"]},{id:"lucide-copy-check",aliases:["clone","duplicate","done","multiple"]},{id:"lucide-copy-minus",aliases:["clone","duplicate","remove","delete","collapse","multiple"]},{id:"lucide-copy-plus",aliases:["clone","duplicate","add","multiple"]},{id:"lucide-copy-slash",aliases:["clone","duplicate","cancel","ban","no","stop","forbidden","prohibited","error","multiple"]},{id:"lucide-copy-x",aliases:["cancel","close","delete","remove","clear","multiple"]},{id:"lucide-copy",aliases:["clone","duplicate","multiple"]},{id:"lucide-copyleft",aliases:["licence"]},{id:"lucide-copyright",aliases:["licence","license"]},{id:"lucide-corner-down-left",aliases:["arrow","return"]},{id:"lucide-corner-down-right",aliases:["arrow"]},{id:"lucide-corner-left-down",aliases:["arrow"]},{id:"lucide-corner-left-up",aliases:["arrow"]},{id:"lucide-corner-right-down",aliases:["arrow"]},{id:"lucide-corner-right-up",aliases:["arrow"]},{id:"lucide-corner-up-left",aliases:["arrow"]},{id:"lucide-corner-up-right",aliases:["arrow"]},{id:"lucide-cpu",aliases:["processor","technology","computer","chip"]},{id:"lucide-creative-commons",aliases:["licence","license"]},{id:"lucide-credit-card",aliases:["bank","purchase","payment","cc"]},{id:"lucide-croissant",aliases:["bakery","cooking","food","pastry"]},{id:"lucide-crop",aliases:["photo","image"]},{id:"lucide-cross",aliases:["healthcare","first aid"]},{id:"lucide-crosshair",aliases:["aim","target"]},{id:"lucide-crown",aliases:["king","winner","favourite"]},{id:"lucide-cup-soda",aliases:["beverage","cup","drink","soda","straw","water"]},{id:"lucide-curly-braces",aliases:["json","code","token"]},{id:"lucide-currency",aliases:["finance","money"]},{id:"lucide-database-backup",aliases:["storage","memory","backup","timemachine","rotate","arrow","left"]},{id:"lucide-database",aliases:["storage","memory"]},{id:"lucide-delete",aliases:["backspace","remove"]},{id:"lucide-diamond",aliases:["square","rectangle","oblique","rhombus","shape"]},{id:"lucide-dice-1",aliases:["dice","random","tabletop","1","board","game"]},{id:"lucide-dice-2",aliases:["dice","random","tabletop","2","board","game"]},{id:"lucide-dice-3",aliases:["dice","random","tabletop","3","board","game"]},{id:"lucide-dice-4",aliases:["dice","random","tabletop","4","board","game"]},{id:"lucide-dice-5",aliases:["dice","random","tabletop","5","board","game"]},{id:"lucide-dice-6",aliases:["dice","random","tabletop","6","board","game"]},{id:"lucide-dices",aliases:["dice","random","tabletop","board","game"]},{id:"lucide-diff",aliases:["patch","difference","plus","minus","plus-minus","maths"]},{id:"lucide-disc-2",aliases:["album","vinyl","record","music"]},{id:"lucide-disc",aliases:["album","cd","dvd","music"]},{id:"lucide-divide-circle",aliases:["calculate","maths"]},{id:"lucide-divide-square",aliases:["calculate","maths"]},{id:"lucide-divide",aliases:["calculate","maths"]},{id:"lucide-dna-off",aliases:["gene","gmo free","helix","heredity","chromosome","nucleic acid"]},{id:"lucide-dna",aliases:["gene","gmo","helix","heredity","chromosome","nucleic acid"]},{id:"lucide-dog",aliases:["animal","pet","puppy","hound","canine"]},{id:"lucide-dollar-sign",aliases:["currency","money","payment"]},{id:"lucide-door-closed",aliases:["entrance","entry","exit","ingress","egress","gate","gateway","emergency exit"]},{id:"lucide-door-open",aliases:["entrance","entry","exit","ingress","egress","gate","gateway","emergency exit"]},{id:"lucide-download-cloud",aliases:["import"]},{id:"lucide-download",aliases:["import","export"]},{id:"lucide-dribbble",aliases:["design","social"]},{id:"lucide-droplet",aliases:["water","weather"]},{id:"lucide-droplets",aliases:["water","humidity","weather"]},{id:"lucide-drumstick",aliases:["food","chicken","meat"]},{id:"lucide-dumbbell",aliases:["barbell","weight","workout","gym"]},{id:"lucide-ear-off",aliases:["hearing","hard of hearing","hearing loss","deafness","noise","silence","audio","accessibility"]},{id:"lucide-ear",aliases:["hearing","noise","audio","accessibility"]},{id:"lucide-edit-2",aliases:["pencil","change","pen"]},{id:"lucide-edit-3",aliases:["pencil","change","pen-line"]},{id:"lucide-edit",aliases:["pencil","change","pen-box"]},{id:"lucide-egg-fried",aliases:["food","breakfast"]},{id:"lucide-egg-off",aliases:["egg free","food"]},{id:"lucide-egg",aliases:["food","bird","chick"]},{id:"lucide-equal-not",aliases:["calculate","off","maths"]},{id:"lucide-equal",aliases:["calculate","maths"]},{id:"lucide-eraser",aliases:["pencil","drawing","undo","delete","clear"]},{id:"lucide-euro",aliases:["currency","money","payment"]},{id:"lucide-expand",aliases:["scale","fullscreen"]},{id:"lucide-external-link",aliases:["outbound"]},{id:"lucide-eye-off",aliases:["view","watch","hide","hidden"]},{id:"lucide-eye",aliases:["view","watch"]},{id:"lucide-facebook",aliases:["logo","social"]},{id:"lucide-factory",aliases:["building","business","energy","industry","manufacture","sector"]},{id:"lucide-fan",aliases:["air","cooler","ventilation","ventilator","blower"]},{id:"lucide-fast-forward",aliases:["music"]},{id:"lucide-feather",aliases:["logo"]},{id:"lucide-figma",aliases:["logo","design","tool"]},{id:"lucide-file-archive",aliases:["zip","package","archive"]},{id:"lucide-file-audio-2",aliases:["music","audio","sound","headphones"]},{id:"lucide-file-audio",aliases:["music","audio","sound","headphones"]},{id:"lucide-file-axis-3d",aliases:["model","3d","axis","coordinates"]},{id:"lucide-file-badge-2",aliases:["award","achievement","badge"]},{id:"lucide-file-badge",aliases:["award","achievement","badge"]},{id:"lucide-file-bar-chart-2",aliases:["statistics","diagram","graph","presentation"]},{id:"lucide-file-bar-chart",aliases:["statistics","diagram","graph","presentation"]},{id:"lucide-file-box",aliases:["box","package","model"]},{id:"lucide-file-check-2",aliases:["done","document","todo","tick","complete","task"]},{id:"lucide-file-check",aliases:["done","document","todo","tick","complete","task"]},{id:"lucide-file-clock",aliases:["history","log","clock"]},{id:"lucide-file-code",aliases:["script","document"]},{id:"lucide-file-cog-2",aliases:["executable","settings","cog","edit","gear"]},{id:"lucide-file-cog",aliases:["executable","settings","cog","edit","gear"]},{id:"lucide-file-diff",aliases:["diff","patch"]},{id:"lucide-file-digit",aliases:["number","document"]},{id:"lucide-file-down",aliases:["download","import","export"]},{id:"lucide-file-edit",aliases:["edit","signature"]},{id:"lucide-file-heart",aliases:["heart","favourite","bookmark","quick link"]},{id:"lucide-file-image",aliases:["image","graphics","photo","picture"]},{id:"lucide-file-input",aliases:["document"]},{id:"lucide-file-json-2",aliases:["code","json","curly braces"]},{id:"lucide-file-json",aliases:["code","json","curly braces"]},{id:"lucide-file-key-2",aliases:["key","private","public","security"]},{id:"lucide-file-key",aliases:["key","private","public","security"]},{id:"lucide-file-line-chart",aliases:["statistics","diagram","graph","presentation"]},{id:"lucide-file-lock-2",aliases:["lock","password","security"]},{id:"lucide-file-lock",aliases:["lock","password","security"]},{id:"lucide-file-minus-2",aliases:["document"]},{id:"lucide-file-minus",aliases:["delete","remove","erase","document"]},{id:"lucide-file-output",aliases:["document"]},{id:"lucide-file-pie-chart",aliases:["statistics","diagram","graph","presentation"]},{id:"lucide-file-plus-2",aliases:["add","create","new","document"]},{id:"lucide-file-plus",aliases:["add","create","new","document"]},{id:"lucide-file-question",aliases:["readme","help","question"]},{id:"lucide-file-scan",aliases:["scan","code","qr-code"]},{id:"lucide-file-search-2",aliases:["lost","document","find","browser"]},{id:"lucide-file-search",aliases:["lost","document","find","browser"]},{id:"lucide-file-signature",aliases:["edit","signature"]},{id:"lucide-file-spreadsheet",aliases:["spreadsheet","sheet","table"]},{id:"lucide-file-symlink",aliases:["symlink","symbolic","link"]},{id:"lucide-file-terminal",aliases:["terminal","bash","script","executable"]},{id:"lucide-file-text",aliases:["data","txt","pdf","document"]},{id:"lucide-file-type-2",aliases:["font","text","typography","type"]},{id:"lucide-file-type",aliases:["font","text","typography","type"]},{id:"lucide-file-up",aliases:["upload","import","export"]},{id:"lucide-file-video-2",aliases:["movie","video","film"]},{id:"lucide-file-video",aliases:["movie","video","film"]},{id:"lucide-file-volume-2",aliases:["audio","music","volume"]},{id:"lucide-file-volume",aliases:["audio","music","volume"]},{id:"lucide-file-warning",aliases:["hidden","warning","alert","danger","protected","exclamation mark"]},{id:"lucide-file-x-2",aliases:["lost","delete","remove","document"]},{id:"lucide-file-x",aliases:["lost","delete","remove","document"]},{id:"lucide-file",aliases:["document"]},{id:"lucide-files",aliases:["multiple","copy","documents"]},{id:"lucide-film",aliases:["movie","video"]},{id:"lucide-filter-x",aliases:["funnel","hopper"]},{id:"lucide-filter",aliases:["funnel","hopper"]},{id:"lucide-fingerprint",aliases:["2fa","authentication","biometric","identity","security"]},{id:"lucide-fish-off",aliases:["food","dish","restaurant","course","meal","seafood","animal","pet","sea","marine","allergy","intolerance","diet"]},{id:"lucide-fish",aliases:["food","dish","restaurant","course","meal","seafood","animal","pet","sea","marine"]},{id:"lucide-flag-off",aliases:["unflag"]},{id:"lucide-flag-triangle-left",aliases:["report","timeline"]},{id:"lucide-flag-triangle-right",aliases:["report","timeline"]},{id:"lucide-flag",aliases:["report"]},{id:"lucide-flame",aliases:["fire","lit"]},{id:"lucide-flashlight-off",aliases:["torch"]},{id:"lucide-flashlight",aliases:["torch"]},{id:"lucide-flask-conical-off",aliases:["beaker","erlenmeyer","non toxic","lab","chemistry","experiment","test"]},{id:"lucide-flask-conical",aliases:["beaker","erlenmeyer","lab","chemistry","experiment","test"]},{id:"lucide-flask-round",aliases:["beaker","lab","chemistry","experiment","test"]},{id:"lucide-flip-horizontal-2",aliases:["reflect","mirror","alignment"]},{id:"lucide-flip-horizontal",aliases:["reflect","mirror","alignment"]},{id:"lucide-flip-vertical-2",aliases:["reflect","mirror","alignment"]},{id:"lucide-flip-vertical",aliases:["reflect","mirror","alignment"]},{id:"lucide-flower-2",aliases:["sustainability","nature","plant"]},{id:"lucide-flower",aliases:["sustainability","nature","plant","spring"]},{id:"lucide-focus",aliases:["camera","lens","photo"]},{id:"lucide-folder-archive",aliases:["archive","zip","package"]},{id:"lucide-folder-check",aliases:["done","directory","todo","tick","complete","task"]},{id:"lucide-folder-clock",aliases:["history","directory","clock"]},{id:"lucide-folder-closed",aliases:["directory","closed"]},{id:"lucide-folder-cog-2",aliases:["directory","settings","control","preferences","cog","edit","gear"]},{id:"lucide-folder-cog",aliases:["directory","settings","control","preferences","cog","edit","gear"]},{id:"lucide-folder-down",aliases:["directory","download","import","export"]},{id:"lucide-folder-edit",aliases:["directory","edit","rename"]},{id:"lucide-folder-git-2",aliases:["directory","root","project","git","repo"]},{id:"lucide-folder-git",aliases:["directory","root","project","git","repo"]},{id:"lucide-folder-heart",aliases:["directory","heart","favourite","bookmark","quick link"]},{id:"lucide-folder-input",aliases:["directory","import","export"]},{id:"lucide-folder-key",aliases:["directory","key","private","security","protected"]},{id:"lucide-folder-lock",aliases:["directory","lock","private","security","protected"]},{id:"lucide-folder-minus",aliases:["directory","remove","delete"]},{id:"lucide-folder-open",aliases:["directory"]},{id:"lucide-folder-output",aliases:["directory","import","export"]},{id:"lucide-folder-plus",aliases:["directory","add","create","new"]},{id:"lucide-folder-search-2",aliases:["directory","search","find","lost","browser"]},{id:"lucide-folder-search",aliases:["directory","search","find","lost","browser"]},{id:"lucide-folder-symlink",aliases:["directory","symlink","symbolic","link"]},{id:"lucide-folder-tree",aliases:["directory","tree","browser"]},{id:"lucide-folder-up",aliases:["directory","upload","import","export"]},{id:"lucide-folder-x",aliases:["directory","remove","delete"]},{id:"lucide-folder",aliases:["directory"]},{id:"lucide-folders",aliases:["multiple","copy","directories"]},{id:"lucide-footprints",aliases:["steps","walking","foot","feet","trail","shoe"]},{id:"lucide-forklift",aliases:["vehicle","transport","logistics"]},{id:"lucide-form-input",aliases:["2fa","authenticate","login","field","text"]},{id:"lucide-forward",aliases:["send","share","email"]},{id:"lucide-frame",aliases:["logo","design","tool"]},{id:"lucide-framer",aliases:["logo","design","tool"]},{id:"lucide-frown",aliases:["emoji","face","bad","sad","emotion"]},{id:"lucide-fuel",aliases:["filling-station","gas","petrol","tank"]},{id:"lucide-function-square",aliases:["programming","code","automation","maths"]},{id:"lucide-gamepad-2",aliases:["console"]},{id:"lucide-gamepad",aliases:["console"]},{id:"lucide-gauge",aliases:["dashboard"]},{id:"lucide-gavel",aliases:["hammer","mallet"]},{id:"lucide-gem",aliases:["diamond","price","special","present"]},{id:"lucide-ghost",aliases:["pac-man","spooky"]},{id:"lucide-gift",aliases:["present","box","birthday","party"]},{id:"lucide-git-branch-plus",aliases:["add","create"]},{id:"lucide-git-branch",aliases:["code","version control"]},{id:"lucide-git-commit",aliases:["code","version control"]},{id:"lucide-git-compare",aliases:["code","version control"]},{id:"lucide-git-fork",aliases:["code","version control"]},{id:"lucide-git-merge",aliases:["code","version control"]},{id:"lucide-git-pull-request-closed",aliases:["code","version control","rejected"]},{id:"lucide-git-pull-request-draft",aliases:["code","version control","draft"]},{id:"lucide-git-pull-request",aliases:["code","version control"]},{id:"lucide-github",aliases:["logo","version control"]},{id:"lucide-gitlab",aliases:["logo","version control"]},{id:"lucide-glass-water",aliases:["beverage","drink","glass","water"]},{id:"lucide-glasses",aliases:["glasses","spectacles"]},{id:"lucide-globe-2",aliases:["world","browser","language","translate"]},{id:"lucide-globe",aliases:["world","browser","language","translate"]},{id:"lucide-grab",aliases:["hand"]},{id:"lucide-graduation-cap",aliases:["school","university","learn","study"]},{id:"lucide-grape",aliases:["fruit","wine","food"]},{id:"lucide-grid",aliases:["table"]},{id:"lucide-grip-horizontal",aliases:["grab","dots","handle","move","drag"]},{id:"lucide-grip-vertical",aliases:["grab","dots","handle","move","drag"]},{id:"lucide-grip",aliases:["grab","dots","handle","move","drag"]},{id:"lucide-hammer",aliases:["mallet"]},{id:"lucide-hand-metal",aliases:["rock"]},{id:"lucide-hand",aliases:["wave","move","mouse","grab"]},{id:"lucide-hard-drive",aliases:["computer","server","memory","data","ssd","disk","hard disk"]},{id:"lucide-hard-hat",aliases:["helmet","construction","safety","savety"]},{id:"lucide-hash",aliases:["hashtag","number","pound"]},{id:"lucide-haze",aliases:["mist","fog"]},{id:"lucide-heading-1",aliases:[]},{id:"lucide-heading-2",aliases:[]},{id:"lucide-heading-3",aliases:[]},{id:"lucide-heading-4",aliases:[]},{id:"lucide-heading-5",aliases:[]},{id:"lucide-heading-6",aliases:[]},{id:"lucide-heading",aliases:[]},{id:"lucide-headphones",aliases:["music","audio","sound"]},{id:"lucide-heart-crack",aliases:["heartbreak","sadness","emotion"]},{id:"lucide-heart-handshake",aliases:["agreement","charity","help","deal","terms","emotion","together","handshake"]},{id:"lucide-heart-off",aliases:["unlike","dislike","hate","emotion"]},{id:"lucide-heart-pulse",aliases:["heartbeat","pulse","health","medical","blood pressure","cardiac","systole","diastole"]},{id:"lucide-heart",aliases:["like","love","emotion"]},{id:"lucide-help-circle",aliases:["question mark"]},{id:"lucide-helping-hand",aliases:["agreement","help","proposal","charity","begging","terms"]},{id:"lucide-hexagon",aliases:["shape","node.js","logo"]},{id:"lucide-highlighter",aliases:["mark","text"]},{id:"lucide-history",aliases:["time","redo","undo","rewind","timeline","version","time machine","backup"]},{id:"lucide-home",aliases:["house","living"]},{id:"lucide-hop-off",aliases:["beer","brewery","drink","hop free","allergy","intolerance","diet"]},{id:"lucide-hop",aliases:["beer","brewery","drink"]},{id:"lucide-hotel",aliases:["building","hostel","motel","inn"]},{id:"lucide-hourglass",aliases:["timer","time","sandglass"]},{id:"lucide-ice-cream-2",aliases:["gelato","food","dessert","dish","restaurant","course","meal"]},{id:"lucide-ice-cream",aliases:["gelato","food"]},{id:"lucide-image-minus",aliases:["remove","delete"]},{id:"lucide-image-off",aliases:["picture","photo"]},{id:"lucide-image-plus",aliases:["add","create"]},{id:"lucide-image",aliases:["picture","photo"]},{id:"lucide-import",aliases:["save"]},{id:"lucide-inbox",aliases:["email"]},{id:"lucide-indent",aliases:["text","tab"]},{id:"lucide-indian-rupee",aliases:["currency","money","payment"]},{id:"lucide-infinity",aliases:["unlimited","forever","loop","maths"]},{id:"lucide-info",aliases:["help"]},{id:"lucide-inspect",aliases:["element","mouse","click","cursor","pointer","box"]},{id:"lucide-instagram",aliases:["logo","camera","social"]},{id:"lucide-italic",aliases:["oblique","text","format"]},{id:"lucide-japanese-yen",aliases:["currency","money","payment"]},{id:"lucide-joystick",aliases:["game","console","control stick"]},{id:"lucide-key",aliases:["password","login","authentication","secure"]},{id:"lucide-keyboard",aliases:["layout","spell","settings","mouse"]},{id:"lucide-lamp-ceiling",aliases:["lighting","household","home","furniture"]},{id:"lucide-lamp-desk",aliases:["lighting","household","office","desk","home","furniture"]},{id:"lucide-lamp-floor",aliases:["lighting","household","floor","home","furniture"]},{id:"lucide-lamp-wall-down",aliases:["lighting","household","wall","home","furniture"]},{id:"lucide-lamp-wall-up",aliases:["lighting","household","wall","home","furniture"]},{id:"lucide-lamp",aliases:["lighting","household","home","furniture"]},{id:"lucide-landmark",aliases:["bank","building","capitol","finance","money"]},{id:"lucide-languages",aliases:["translate"]},{id:"lucide-laptop-2",aliases:["computer"]},{id:"lucide-laptop",aliases:["computer"]},{id:"lucide-lasso-select",aliases:["select","cursor"]},{id:"lucide-lasso",aliases:["select","cursor"]},{id:"lucide-laugh",aliases:["emoji","face","happy","good","emotion"]},{id:"lucide-layers",aliases:["stack","pages"]},{id:"lucide-layout-dashboard",aliases:["masonry","brick"]},{id:"lucide-layout-grid",aliases:["app","home","start"]},{id:"lucide-layout-list",aliases:["image","photo","item"]},{id:"lucide-layout-template",aliases:["window","webpage","block","section"]},{id:"lucide-layout",aliases:["window","webpage"]},{id:"lucide-leaf",aliases:["sustainability","nature","energy","plant","autumn"]},{id:"lucide-library",aliases:["book","music","album"]},{id:"lucide-life-buoy",aliases:["help","rescue","ship"]},{id:"lucide-lightbulb-off",aliases:["lights"]},{id:"lucide-lightbulb",aliases:["idea","bright","lights"]},{id:"lucide-line-chart",aliases:["statistics","diagram","graph"]},{id:"lucide-link-2-off",aliases:["unchain","chain"]},{id:"lucide-link-2",aliases:["chain","url"]},{id:"lucide-link",aliases:["chain","url"]},{id:"lucide-linkedin",aliases:["logo","social media","social"]},{id:"lucide-list-checks",aliases:["todo","done","tick","complete","task"]},{id:"lucide-list-end",aliases:["queue","bottom","end","playlist"]},{id:"lucide-list-minus",aliases:["playlist","remove","song","subtract","delete","unqueue"]},{id:"lucide-list-music",aliases:["playlist","queue","music","audio","playback"]},{id:"lucide-list-ordered",aliases:["number","order","queue"]},{id:"lucide-list-plus",aliases:["playlist","add","song","track","new"]},{id:"lucide-list-start",aliases:["queue","top","start","next","playlist"]},{id:"lucide-list-tree",aliases:["tree","browser"]},{id:"lucide-list-video",aliases:["playlist","video","playback"]},{id:"lucide-list-x",aliases:["playlist","subtract","remove","delete","unqueue"]},{id:"lucide-list",aliases:["options"]},{id:"lucide-loader-2",aliases:["load"]},{id:"lucide-loader",aliases:["load","wait"]},{id:"lucide-locate-fixed",aliases:["map","gps","location","cross"]},{id:"lucide-locate-off",aliases:["map","gps","location","cross"]},{id:"lucide-locate",aliases:["map","gps","location","cross"]},{id:"lucide-lock",aliases:["security","password","secure","admin"]},{id:"lucide-log-in",aliases:["sign in","arrow","enter","auth"]},{id:"lucide-log-out",aliases:["sign out","arrow","exit","auth"]},{id:"lucide-luggage",aliases:["baggage","luggage","travel","suitcase"]},{id:"lucide-magnet",aliases:["horseshoe","lock","science","snap"]},{id:"lucide-mail-check",aliases:["email","message","letter","subscribe","delivered","success","read","done","todo","tick","complete","task"]},{id:"lucide-mail-minus",aliases:["email","message","letter","remove","delete"]},{id:"lucide-mail-open",aliases:["email","message","letter","read"]},{id:"lucide-mail-plus",aliases:["email","message","letter","add","create","new","compose"]},{id:"lucide-mail-question",aliases:["email","message","letter","delivery","undelivered"]},{id:"lucide-mail-search",aliases:["email","message","letter","search"]},{id:"lucide-mail-warning",aliases:["email","message","letter","delivery error","exclamation mark"]},{id:"lucide-mail-x",aliases:["email","message","letter","remove","delete"]},{id:"lucide-mail",aliases:["email","message","letter","unread"]},{id:"lucide-mailbox",aliases:["emails","messages","letters","mailing list","newsletter"]},{id:"lucide-mails",aliases:["emails","messages","letters","multiple","mailing list","newsletter","copy"]},{id:"lucide-map-pin-off",aliases:["location","navigation","travel","marker"]},{id:"lucide-map-pin",aliases:["location","navigation","travel","marker"]},{id:"lucide-map",aliases:["location","navigation","travel"]},{id:"lucide-martini",aliases:["cocktail","alcohol","beverage","bar","drink","glass"]},{id:"lucide-maximize-2",aliases:["fullscreen","arrows","expand"]},{id:"lucide-maximize",aliases:["fullscreen","expand"]},{id:"lucide-medal",aliases:["prize","sports","winner","trophy","award","achievement"]},{id:"lucide-megaphone-off",aliases:["advertisement","attention","alert","notification","disable","silent"]},{id:"lucide-megaphone",aliases:["advertisement","attention","alert","notification"]},{id:"lucide-meh",aliases:["emoji","face","neutral","emotion"]},{id:"lucide-menu",aliases:["bars","navigation","hamburger","options"]},{id:"lucide-message-circle",aliases:["comment","chat","conversation"]},{id:"lucide-message-square-dashed",aliases:["comment","chat","conversation","draft"]},{id:"lucide-message-square-plus",aliases:["comment","chat","conversation","add","feedback"]},{id:"lucide-message-square",aliases:["comment","chat","conversation"]},{id:"lucide-messages-square",aliases:["comment","chat","conversation","copy","multiple"]},{id:"lucide-mic-2",aliases:["lyrics","voice","listen","sound","music","radio","podcast","karaoke","singing","microphone"]},{id:"lucide-mic-off",aliases:["record","sound","mute","microphone"]},{id:"lucide-mic",aliases:["record","sound","listen","radio","podcast","microphone"]},{id:"lucide-microscope",aliases:["medical","education","science","imaging","research"]},{id:"lucide-microwave",aliases:["oven","cooker","toaster oven","bake"]},{id:"lucide-milestone",aliases:["sign","signpost","version control"]},{id:"lucide-milk-off",aliases:["lactose free","bottle","beverage","drink","water","allergy","intolerance","diet"]},{id:"lucide-milk",aliases:["lactose","bottle","beverage","drink","water","diet"]},{id:"lucide-minimize-2",aliases:["exit fullscreen","arrows","close","shrink"]},{id:"lucide-minimize",aliases:["exit fullscreen","close","shrink"]},{id:"lucide-minus-circle",aliases:["subtract","calculate","maths"]},{id:"lucide-minus-square",aliases:["subtract","calculate","maths"]},{id:"lucide-minus",aliases:["subtract","calculate","maths","line","divide","horizontal rule","hr"]},{id:"lucide-monitor-off",aliases:["share"]},{id:"lucide-monitor-smartphone",aliases:["smartphone","phone","cellphone","device","mobile","desktop","monitor","responsive"]},{id:"lucide-monitor-speaker",aliases:["devices","connect","cast"]},{id:"lucide-monitor",aliases:["tv","screen","display"]},{id:"lucide-moon",aliases:["dark","night"]},{id:"lucide-more-horizontal",aliases:["ellipsis","menu","options"]},{id:"lucide-more-vertical",aliases:["ellipsis","menu","options"]},{id:"lucide-mountain-snow",aliases:["alpine","climb","snow"]},{id:"lucide-mountain",aliases:["climb","hike","rock"]},{id:"lucide-mouse-pointer-2",aliases:["arrow","cursor","click"]},{id:"lucide-mouse-pointer-click",aliases:["arrow","cursor","click"]},{id:"lucide-mouse-pointer",aliases:["arrow","cursor","click"]},{id:"lucide-mouse",aliases:["device","scroll","click"]},{id:"lucide-move-3d",aliases:["arrows","axis","gizmo","coordinates","transform","translate"]},{id:"lucide-move-diagonal-2",aliases:["double","arrow"]},{id:"lucide-move-diagonal",aliases:["double","arrow"]},{id:"lucide-move-horizontal",aliases:["double","arrow"]},{id:"lucide-move-vertical",aliases:["double","arrow"]},{id:"lucide-move",aliases:["arrows"]},{id:"lucide-music-2",aliases:["quaver","eighth note","note"]},{id:"lucide-music-3",aliases:["crotchet","minim","quarter note","half note","note"]},{id:"lucide-music-4",aliases:["semiquaver","sixteenth note","note"]},{id:"lucide-music",aliases:["note","quaver","eighth note"]},{id:"lucide-navigation-2-off",aliases:["location","travel"]},{id:"lucide-navigation-2",aliases:["location","travel"]},{id:"lucide-navigation-off",aliases:["location","travel"]},{id:"lucide-navigation",aliases:["location","travel"]},{id:"lucide-network",aliases:["tree"]},{id:"lucide-newspaper",aliases:["news","feed","home","magazine","article","headline"]},{id:"lucide-nfc",aliases:["contactless","payment","near-field communication"]},{id:"lucide-nut-off",aliases:["hazelnut","acorn","food","allergy","intolerance","diet"]},{id:"lucide-nut",aliases:["hazelnut","acorn","food","diet"]},{id:"lucide-octagon",aliases:["stop","shape"]},{id:"lucide-option",aliases:["key","mac","button"]},{id:"lucide-orbit",aliases:["planet","space","physics"]},{id:"lucide-outdent",aliases:["text","tab"]},{id:"lucide-package-2",aliases:["box","container","archive","zip"]},{id:"lucide-package-check",aliases:["confirm","verified","done","todo","tick","complete","task"]},{id:"lucide-package-minus",aliases:["delete","remove"]},{id:"lucide-package-open",aliases:["box","container","unpack","open"]},{id:"lucide-package-plus",aliases:["new","add","create"]},{id:"lucide-package-search",aliases:["find","product process"]},{id:"lucide-package-x",aliases:["delete","remove"]},{id:"lucide-package",aliases:["box","container"]},{id:"lucide-paint-bucket",aliases:["fill","paint","bucket","design"]},{id:"lucide-paintbrush-2",aliases:["brush","paintbrush","design","color"]},{id:"lucide-paintbrush",aliases:["brush","paintbrush","design","color"]},{id:"lucide-palette",aliases:["color","theme"]},{id:"lucide-palmtree",aliases:["vacation","leisure","island"]},{id:"lucide-paperclip",aliases:["attachment","file"]},{id:"lucide-parking-circle-off",aliases:["parking lot","car park","no parking"]},{id:"lucide-parking-circle",aliases:["parking lot","car park"]},{id:"lucide-parking-square-off",aliases:["parking lot","car park","no parking"]},{id:"lucide-parking-square",aliases:["parking lot","car park"]},{id:"lucide-party-popper",aliases:["emoji","congratulations","celebration","party"]},{id:"lucide-pause-circle",aliases:["music","audio","stop"]},{id:"lucide-pause-octagon",aliases:["music","audio","stop"]},{id:"lucide-pause",aliases:["music","stop"]},{id:"lucide-pen-tool",aliases:["vector","drawing","path"]},{id:"lucide-pencil",aliases:["edit","pen","create"]},{id:"lucide-percent",aliases:["discount"]},{id:"lucide-person-standing",aliases:["people","human","accessibility","stick figure"]},{id:"lucide-phone-call",aliases:["ring"]},{id:"lucide-phone-forwarded",aliases:["call"]},{id:"lucide-phone-incoming",aliases:["call"]},{id:"lucide-phone-missed",aliases:["call"]},{id:"lucide-phone-off",aliases:["call","mute"]},{id:"lucide-phone-outgoing",aliases:["call"]},{id:"lucide-phone",aliases:["call"]},{id:"lucide-picture-in-picture-2",aliases:["display","play","video","pop out","always on top","window","inset","multitask"]},{id:"lucide-picture-in-picture",aliases:["display","play","video","pop out","always on top","window","inset","multitask"]},{id:"lucide-pie-chart",aliases:["statistics","diagram","presentation"]},{id:"lucide-piggy-bank",aliases:["money","savings"]},{id:"lucide-pilcrow",aliases:["paragraph","mark","paraph","blind","typography","type","text"]},{id:"lucide-pill",aliases:["medicine","medication","drug","prescription","tablet","pharmacy"]},{id:"lucide-pin-off",aliases:["unpin","map","unlock","unfix","unsave","remove"]},{id:"lucide-pin",aliases:["save","map","lock","fix"]},{id:"lucide-pipette",aliases:["eye dropper","color picker","lab","chemistry"]},{id:"lucide-pizza",aliases:["pie","quiche","food"]},{id:"lucide-plane-landing",aliases:["arrival","plane","trip","airplane","landing"]},{id:"lucide-plane-takeoff",aliases:["departure","plane","trip","airplane","takeoff"]},{id:"lucide-plane",aliases:["plane","trip","airplane"]},{id:"lucide-play-circle",aliases:["music","start","run"]},{id:"lucide-play",aliases:["music","start","run"]},{id:"lucide-plug-2",aliases:["electricity","socket","outlet"]},{id:"lucide-plug-zap",aliases:["charge","charging","battery","connect"]},{id:"lucide-plug",aliases:["electricity","socket","outlet"]},{id:"lucide-plus-circle",aliases:["add","new","maths"]},{id:"lucide-plus-square",aliases:["add","new","maths"]},{id:"lucide-plus",aliases:["add","new","maths"]},{id:"lucide-pocket",aliases:["logo","save"]},{id:"lucide-podcast",aliases:["mic","music"]},{id:"lucide-pointer",aliases:["mouse"]},{id:"lucide-pound-sterling",aliases:["currency","money","payment"]},{id:"lucide-power-off",aliases:["on","off","device","switch"]},{id:"lucide-power",aliases:["on","off","device","switch","reboot","restart"]},{id:"lucide-printer",aliases:["fax","office","device"]},{id:"lucide-puzzle",aliases:["component","module","part","piece"]},{id:"lucide-qr-code",aliases:["barcode"]},{id:"lucide-quote",aliases:["quotation"]},{id:"lucide-radio-receiver",aliases:["device","music","connect"]},{id:"lucide-radio-tower",aliases:["signal","broadcast","connectivity","live","frequency"]},{id:"lucide-radio",aliases:["signal","broadcast","connectivity","live","frequency"]},{id:"lucide-rat",aliases:["animal","mouse","mice","rodent","pet","pest","plague"]},{id:"lucide-receipt",aliases:["bill","voucher","slip","check","counterfoil"]},{id:"lucide-rectangle-horizontal",aliases:["rectangle","aspect ratio","16:9","horizontal","shape"]},{id:"lucide-rectangle-vertical",aliases:["rectangle","aspect ratio","9:16","vertical","shape"]},{id:"lucide-recycle",aliases:["sustainability","salvage","arrows"]},{id:"lucide-redo-2",aliases:["undo","rerun","history"]},{id:"lucide-redo",aliases:["undo","rerun","history"]},{id:"lucide-refresh-ccw",aliases:["arrows","rotate","reload","rerun","synchronise","synchronize","circular","cycle"]},{id:"lucide-refresh-cw",aliases:["rotate","reload","rerun","synchronise","synchronize","arrows","circular","cycle"]},{id:"lucide-refrigerator",aliases:["frigerator","fridge","freezer","cooler","icebox","chiller","cold storage"]},{id:"lucide-regex",aliases:["search","text","code"]},{id:"lucide-remove-formatting",aliases:["text","font","typography","format","x","remove","delete","times","clear"]},{id:"lucide-repeat-1",aliases:["replay"]},{id:"lucide-repeat",aliases:["loop","arrows"]},{id:"lucide-replace-all",aliases:["search","substitute","swap","change"]},{id:"lucide-replace",aliases:["search","substitute","swap","change"]},{id:"lucide-reply-all",aliases:["email"]},{id:"lucide-reply",aliases:["email"]},{id:"lucide-rewind",aliases:["music"]},{id:"lucide-rocket",aliases:["release","boost","launch","space","version"]},{id:"lucide-rocking-chair",aliases:["chair","furniture","seat"]},{id:"lucide-rotate-3d",aliases:["gizmo","transform","orientation","orbit"]},{id:"lucide-rotate-ccw",aliases:["arrow","left","counter-clockwise","restart","reload","rerun","refresh","backup","undo"]},{id:"lucide-rotate-cw",aliases:["arrow","right","clockwise","refresh","reload","rerun","redo"]},{id:"lucide-router",aliases:["computer","server","cloud"]},{id:"lucide-rss",aliases:["feed","subscribe"]},{id:"lucide-ruler",aliases:["measure","meter","foot","inch"]},{id:"lucide-russian-ruble",aliases:["currency","money","payment"]},{id:"lucide-sailboat",aliases:["ship","boat","harbor","harbour","dock"]},{id:"lucide-salad",aliases:["food","vegetarian","dish","restaurant","course","meal","side","vegetables","health"]},{id:"lucide-sandwich",aliases:["food","snack","dish","restaurant","lunch","meal"]},{id:"lucide-save",aliases:["floppy disk"]},{id:"lucide-scale-3d",aliases:["gizmo","transform","size"]},{id:"lucide-scale",aliases:["balance","legal","license","right","rule","law"]},{id:"lucide-scaling",aliases:["scale","resize","design"]},{id:"lucide-scan-face",aliases:["face","biometric","authentication","2fa"]},{id:"lucide-scan-line",aliases:["qr-code"]},{id:"lucide-scan",aliases:["qr-code"]},{id:"lucide-school-2",aliases:["building","education","childhood","university"]},{id:"lucide-school",aliases:["building","education","childhood","university"]},{id:"lucide-scissors",aliases:["cut"]},{id:"lucide-screen-share-off",aliases:["desktop","disconnect"]},{id:"lucide-screen-share",aliases:["host","desktop"]},{id:"lucide-scroll",aliases:["paper","log","scripture","document","parchment"]},{id:"lucide-search",aliases:["find","magnifier","magnifying glass"]},{id:"lucide-send",aliases:["email","message","mail","paper airplane","paper aeroplane","submit"]},{id:"lucide-separator-horizontal",aliases:["move","split"]},{id:"lucide-separator-vertical",aliases:["move","split"]},{id:"lucide-server-cog",aliases:["cloud","storage","computing","cog","gear"]},{id:"lucide-server-crash",aliases:["cloud","storage","problem","error"]},{id:"lucide-server-off",aliases:["cloud","storage"]},{id:"lucide-server",aliases:["cloud","storage"]},{id:"lucide-settings-2",aliases:["cog","edit","gear","preferences"]},{id:"lucide-settings",aliases:["cog","edit","gear","preferences"]},{id:"lucide-share-2",aliases:["network","connections"]},{id:"lucide-share",aliases:["network","connections"]},{id:"lucide-sheet",aliases:["spreadsheets","table","excel"]},{id:"lucide-shield-alert",aliases:["security","secure","virus","admin","safety","savety"]},{id:"lucide-shield-check",aliases:["security","secure","done","save","todo","tick","complete","task"]},{id:"lucide-shield-close",aliases:["security","secure","wrong","unsave","virus"]},{id:"lucide-shield-off",aliases:["security","secure","insecure"]},{id:"lucide-shield-question",aliases:["security","secure","insecure"]},{id:"lucide-shield",aliases:["security","secure"]},{id:"lucide-ship",aliases:["boat","trip","maritime","navy"]},{id:"lucide-shirt",aliases:["t-shirt","shopping","store","clothing","clothes"]},{id:"lucide-shopping-bag",aliases:["ecommerce","cart","purchase","store"]},{id:"lucide-shopping-cart",aliases:["ecommerce","cart","purchase","store"]},{id:"lucide-shovel",aliases:["dig","spade","treasure"]},{id:"lucide-shower-head",aliases:["shower","bath","bathroom","amenities","services"]},{id:"lucide-shrink",aliases:["scale","fullscreen"]},{id:"lucide-shrub",aliases:["forest","undergrowth","park","nature"]},{id:"lucide-shuffle",aliases:["music"]},{id:"lucide-sidebar-close",aliases:["menu"]},{id:"lucide-sidebar-open",aliases:["menu"]},{id:"lucide-sidebar",aliases:["menu"]},{id:"lucide-sigma",aliases:["sum","calculate","formula","maths"]},{id:"lucide-signal-high",aliases:["connection","wireless","gsm","phone","2g","3g","4g","5g"]},{id:"lucide-signal-low",aliases:["connection","wireless","gsm","phone","2g","3g","4g","5g"]},{id:"lucide-signal-medium",aliases:["connection","wireless","gsm","phone","2g","3g","4g","5g"]},{id:"lucide-signal-zero",aliases:["connection","wireless","gsm","phone","2g","3g","4g","5g","lost"]},{id:"lucide-signal",aliases:["connection","wireless","gsm","phone","2g","3g","4g","5g"]},{id:"lucide-siren",aliases:["police","ambulance","emergency","security","alert","alarm","light"]},{id:"lucide-skip-back",aliases:["arrow","previous","music"]},{id:"lucide-skip-forward",aliases:["arrow","skip","next","music"]},{id:"lucide-skull",aliases:["death","danger","bone"]},{id:"lucide-slack",aliases:["logo"]},{id:"lucide-slice",aliases:["cutter","scalpel","knife"]},{id:"lucide-sliders-horizontal",aliases:["settings","filters","controls"]},{id:"lucide-sliders",aliases:["settings","controls"]},{id:"lucide-smartphone-charging",aliases:["phone","cellphone","device","power"]},{id:"lucide-smartphone-nfc",aliases:["contactless","payment","near-field communication"]},{id:"lucide-smartphone",aliases:["phone","cellphone","device"]},{id:"lucide-smile-plus",aliases:["emoji","face","happy","good","emotion","react","reaction","add"]},{id:"lucide-smile",aliases:["emoji","face","happy","good","emotion"]},{id:"lucide-snowflake",aliases:["cold","weather","freeze","snow","winter"]},{id:"lucide-sofa",aliases:["armchair","furniture","leisure","lounge","loveseat","couch"]},{id:"lucide-sort-asc",aliases:["filter"]},{id:"lucide-sort-desc",aliases:["filter"]},{id:"lucide-soup",aliases:["food","dish","restaurant","course","meal","bowl","starter"]},{id:"lucide-space",aliases:["text","selection","letters","characters","font","typography"]},{id:"lucide-speaker",aliases:["audio","music"]},{id:"lucide-spline",aliases:[]},{id:"lucide-split-square-horizontal",aliases:["split","divide"]},{id:"lucide-split-square-vertical",aliases:["split","divide"]},{id:"lucide-sprout",aliases:["leaf","nature","plant"]},{id:"lucide-square",aliases:["rectangle","aspect ratio","1:1","shape"]},{id:"lucide-stamp",aliases:["mark","print","clone","loyalty","library"]},{id:"lucide-star-half",aliases:["bookmark","favorite","like","review","rating"]},{id:"lucide-star-off",aliases:["dislike","unlike","remove","unrate"]},{id:"lucide-star",aliases:["bookmark","favorite","like","review","rating"]},{id:"lucide-step-back",aliases:["arrow","previous","music","left","reverse"]},{id:"lucide-step-forward",aliases:["arrow","next","music","right","continue"]},{id:"lucide-stethoscope",aliases:["phonendoscope","medical","heart","lungs","sound"]},{id:"lucide-sticker",aliases:["reaction","emotion","smile","happy","feedback"]},{id:"lucide-sticky-note",aliases:["note","comment","reaction","memo"]},{id:"lucide-stop-circle",aliases:["media","music"]},{id:"lucide-store",aliases:["shop","supermarket","stand","boutique","building"]},{id:"lucide-stretch-horizontal",aliases:["items","flex","justify","distribute"]},{id:"lucide-stretch-vertical",aliases:["items","flex","justify","distribute"]},{id:"lucide-strikethrough",aliases:["cross out","delete","remove","format"]},{id:"lucide-subscript",aliases:["text"]},{id:"lucide-subtitles",aliases:["captions","closed captions","accessibility"]},{id:"lucide-sun-dim",aliases:["brightness","dim","low","brightness low"]},{id:"lucide-sun-medium",aliases:["brightness","medium"]},{id:"lucide-sun-moon",aliases:["night","light","moon","sun","brightness"]},{id:"lucide-sun-snow",aliases:["weather","air conditioning","temperature","hot","cold","seasons"]},{id:"lucide-sun",aliases:["brightness","weather","light","summer"]},{id:"lucide-sunrise",aliases:["weather","time","morning","day"]},{id:"lucide-sunset",aliases:["weather","time","evening","night"]},{id:"lucide-superscript",aliases:["text","exponent"]},{id:"lucide-swiss-franc",aliases:["currency","money","payment"]},{id:"lucide-switch-camera",aliases:["photo","selfie","front","back"]},{id:"lucide-sword",aliases:["battle","challenge","game","war","weapon"]},{id:"lucide-swords",aliases:["battle","challenge","game","war","weapon"]},{id:"lucide-syringe",aliases:["medicine","medical","needle","pump","plunger","nozzle","blood"]},{id:"lucide-table-2",aliases:["sheet","grid","spreadsheet"]},{id:"lucide-table",aliases:["sheet","grid","spreadsheet"]},{id:"lucide-tablet",aliases:["device"]},{id:"lucide-tablets",aliases:["medicine","medication","drug","prescription","pills","pharmacy"]},{id:"lucide-tag",aliases:["label","badge","ticket","mark"]},{id:"lucide-tags",aliases:["labels","badges","tickets","marks","copy","multiple"]},{id:"lucide-target",aliases:["logo","bullseye"]},{id:"lucide-tent",aliases:["campsite","wigwam"]},{id:"lucide-terminal-square",aliases:["code","command line","prompt","shell"]},{id:"lucide-terminal",aliases:["code","command line","prompt","shell"]},{id:"lucide-test-tube-2",aliases:["tube","vial","phial","flask","ampoule","ampule","lab","chemistry","experiment","test"]},{id:"lucide-test-tube",aliases:["tube","vial","phial","flask","ampoule","ampule","lab","chemistry","experiment","test"]},{id:"lucide-test-tubes",aliases:["tubes","vials","phials","flasks","ampoules","ampules","lab","chemistry","experiment","test"]},{id:"lucide-text-cursor-input",aliases:["select"]},{id:"lucide-text-cursor",aliases:["select"]},{id:"lucide-text-selection",aliases:["find","search"]},{id:"lucide-text",aliases:["find","search","data","txt","pdf","document"]},{id:"lucide-thermometer-snowflake",aliases:["temperature","celsius","fahrenheit","weather","cold","freeze","freezing"]},{id:"lucide-thermometer-sun",aliases:["temperature","celsius","fahrenheit","weather","warm","hot"]},{id:"lucide-thermometer",aliases:["temperature","celsius","fahrenheit","weather"]},{id:"lucide-thumbs-down",aliases:["dislike","bad","emotion"]},{id:"lucide-thumbs-up",aliases:["like","good","emotion"]},{id:"lucide-ticket",aliases:["entry","pass","voucher"]},{id:"lucide-timer-off",aliases:["time","timer","stopwatch"]},{id:"lucide-timer-reset",aliases:["time","timer","stopwatch"]},{id:"lucide-timer",aliases:["time","timer","stopwatch"]},{id:"lucide-toggle-left",aliases:["on","off","switch"]},{id:"lucide-toggle-right",aliases:["on","off","switch"]},{id:"lucide-tornado",aliases:["weather","wind","storm","hurricane"]},{id:"lucide-tower-control",aliases:["airport","travel","tower","transportation","lighthouse"]},{id:"lucide-toy-brick",aliases:["lego","block","addon","plugin","integration"]},{id:"lucide-train",aliases:["transport","metro","subway","underground"]},{id:"lucide-trash-2",aliases:["garbage","delete","remove","bin"]},{id:"lucide-trash",aliases:["garbage","delete","remove","bin"]},{id:"lucide-tree-deciduous",aliases:["tree","forest","park","nature"]},{id:"lucide-tree-pine",aliases:["tree","pine","forest","park","nature"]},{id:"lucide-trees",aliases:["tree","forest","park","nature"]},{id:"lucide-trello",aliases:["logo","brand"]},{id:"lucide-trending-down",aliases:["statistics"]},{id:"lucide-trending-up",aliases:["statistics"]},{id:"lucide-triangle",aliases:["delta","shape"]},{id:"lucide-trophy",aliases:["prize","sports","winner","achievement","award"]},{id:"lucide-truck",aliases:["delivery","van","shipping","transport","lorry"]},{id:"lucide-tv-2",aliases:["flatscreen","television","stream","display"]},{id:"lucide-tv",aliases:["television","stream"]},{id:"lucide-twitch",aliases:["logo","social"]},{id:"lucide-twitter",aliases:["logo","social"]},{id:"lucide-type",aliases:["text","font","typography"]},{id:"lucide-umbrella",aliases:["rain","weather"]},{id:"lucide-underline",aliases:["text","format"]},{id:"lucide-undo-2",aliases:["redo","rerun","history"]},{id:"lucide-undo",aliases:["redo","rerun","history"]},{id:"lucide-unlink-2",aliases:["url","unchain"]},{id:"lucide-unlink",aliases:["url","unchain"]},{id:"lucide-unlock",aliases:["security"]},{id:"lucide-upload-cloud",aliases:["file"]},{id:"lucide-upload",aliases:["file"]},{id:"lucide-usb",aliases:["universal","serial","bus","controller","connector","interface"]},{id:"lucide-user-check",aliases:["followed","subscribed","done","todo","tick","complete","task"]},{id:"lucide-user-cog",aliases:["settings","edit","cog","gear"]},{id:"lucide-user-minus",aliases:["delete","remove","unfollow","unsubscribe"]},{id:"lucide-user-plus",aliases:["new","add","create","follow","subscribe"]},{id:"lucide-user-x",aliases:["delete","remove","unfollow","unsubscribe","unavailable"]},{id:"lucide-user",aliases:["person","account","contact"]},{id:"lucide-users",aliases:["group","people"]},{id:"lucide-utensils-crossed",aliases:["food","restaurant","meal","cutlery","breakfast","dinner","supper"]},{id:"lucide-utensils",aliases:["food","restaurant","meal","cutlery","breakfast","dinner","supper"]},{id:"lucide-utility-pole",aliases:["electricity","energy","transmission line","telegraph pole"]},{id:"lucide-vegan",aliases:["vegetarian","fruitarian","herbivorous","animal rights","diet"]},{id:"lucide-venetian-mask",aliases:["mask","masquerade","impersonate","secret","incognito"]},{id:"lucide-verified",aliases:["check"]},{id:"lucide-vibrate-off",aliases:["smartphone","notification","rumble","haptic feedback","notifications"]},{id:"lucide-vibrate",aliases:["smartphone","notification","rumble","haptic feedback"]},{id:"lucide-video-off",aliases:["camera","movie","film"]},{id:"lucide-video",aliases:["camera","movie","film"]},{id:"lucide-view",aliases:["eye","look"]},{id:"lucide-voicemail",aliases:["phone","cassette"]},{id:"lucide-volume-1",aliases:["music","sound","speaker"]},{id:"lucide-volume-2",aliases:["music","sound","speaker"]},{id:"lucide-volume-x",aliases:["music","sound","mute","speaker"]},{id:"lucide-volume",aliases:["music","sound","mute","speaker"]},{id:"lucide-vote",aliases:["vote","poll","ballot","political","social","check","tick"]},{id:"lucide-wallet",aliases:["money","finance","pocket"]},{id:"lucide-wand-2",aliases:["magic","wizard"]},{id:"lucide-wand",aliases:["magic","selection"]},{id:"lucide-warehouse",aliases:["storage","logistics","building"]},{id:"lucide-watch",aliases:["clock","time"]},{id:"lucide-waves",aliases:["water","sea","sound","hertz","wavelength","vibrate"]},{id:"lucide-webcam",aliases:["camera","security"]},{id:"lucide-webhook",aliases:["push api","interface","callback"]},{id:"lucide-wheat-off",aliases:["corn","cereal","grain","gluten free","allergy","intolerance","diet"]},{id:"lucide-wheat",aliases:["corn","cereal","grain","gluten"]},{id:"lucide-whole-word",aliases:["text","selection","letters","characters","font","typography"]},{id:"lucide-wifi-off",aliases:["disabled"]},{id:"lucide-wifi",aliases:["connection","signal","wireless"]},{id:"lucide-wind",aliases:["weather","air","blow"]},{id:"lucide-wine-off",aliases:["alcohol","beverage","drink","glass","alcohol free","abstinence","abstaining","teetotalism","allergy","intolerance"]},{id:"lucide-wine",aliases:["alcohol","beverage","bar","drink","glass","sommelier","vineyard","winery"]},{id:"lucide-wrap-text",aliases:["words","lines","break","paragraph"]},{id:"lucide-wrench",aliases:["account","tool","settings","spanner"]},{id:"lucide-x-circle",aliases:["cancel","close","delete","remove","times","clear","maths"]},{id:"lucide-x-octagon",aliases:["delete","stop","alert","warning","times","clear","maths"]},{id:"lucide-x-square",aliases:["cancel","close","delete","remove","times","clear","maths"]},{id:"lucide-x",aliases:["cancel","close","delete","remove","times","clear","maths"]},{id:"lucide-youtube",aliases:["logo","social","video","play"]},{id:"lucide-zap-off",aliases:["flash","camera","lightning"]},{id:"lucide-zap",aliases:["flash","camera","lightning"]},{id:"lucide-zoom-in",aliases:["magnifying glass","plus"]},{id:"lucide-zoom-out",aliases:["magnifying glass","plus"]}];(0,Ve.getIconIds)().forEach(a=>{/^lucide/.test(a)||He.push({id:a,aliases:[]})});var Be=new I(He,{threshold:.1,minMatchCharLength:2,keys:["id","aliases"]});function D(a,e){a.empty(),a.createDiv({cls:"markdown-source-view cm-s-obsidian mod-cm6 is-readable-line-width is-live-preview"},i=>{i.createDiv({cls:"HyperMD-list-line HyperMD-list-line-1 lc-list-callout cm-line",attr:{style:`text-indent: -8px; padding-left: 12px; --lc-callout-color: ${e.color}`}},s=>{s.createSpan({cls:"cm-formatting cm-formatting-list cm-formatting-list-ul cm-list-1"},l=>{l.createSpan({cls:"list-bullet",text:"-"}),l.appendText(" ")}),s.createSpan({cls:"lc-list-bg"}),s.createSpan({cls:"lc-list-marker"},l=>{e.icon?(0,b.setIcon)(l,e.icon):l.appendText(e.char)}),s.createSpan({cls:"cm-list-1",text:" Sed eu nisl rhoncus, consectetur mi quis, scelerisque enim."})})})}function We(a,e){let i=null,s=a.buttonEl;a.onClick(l=>{l.preventDefault();let t=s.closest(".vertical-tab-content"),c=()=>{s.win.removeEventListener("click",r),t.removeEventListener("scroll",d)},r=n=>{i?i.contains(n.targetNode)||(i.detach(),i=null,c()):c()},o=()=>{let n=`top: ${s.offsetTop+s.offsetHeight+2-t.scrollTop}px;`;b.Platform.isMobile?n+=` right: ${s.offsetParent.clientWidth-(s.offsetLeft+s.offsetWidth)}px;`:n+=` left: ${s.offsetLeft}px;`,i.style.cssText=n},d=()=>{i?o():c()};if(i){c(),i.detach(),i=null;return}createDiv("lc-menu",n=>{i=n,s.after(i),o();let u={};n.createDiv("lc-menu-search",h=>{h.createEl("input",{attr:{type:"text",placeholder:"Search..."}},g=>{activeWindow.setTimeout(()=>{g.focus()});let f=(0,b.debounce)(()=>{let w=Be.search(g.value);if(!g.value){(0,b.getIconIds)().forEach(y=>{m.append(u[y])});return}m.empty(),w.forEach(y=>{m.append(u[y.item.id])})},250,!0);g.addEventListener("input",f)})});let m=n.createDiv("lc-menu-icons",h=>{(0,b.getIconIds)().forEach(g=>{h.createDiv({cls:"clickable-icon",attr:{"data-icon":g}},f=>{u[g]=f,(0,b.setIcon)(f,g),f.onClickEvent(()=>{a.buttonEl.empty(),a.setIcon(g),e(g),c(),i.detach(),i=null})})})})}),s.win.setTimeout(()=>{s.win.addEventListener("click",r),t.addEventListener("scroll",d)},10)})}function Ni(a,e,i,s,l){a.createDiv({cls:"lc-setting"},t=>{let c=t.createDiv({cls:"lc-callout-container"});D(c,s),t.createDiv({cls:"lc-input-container"},r=>{new b.TextComponent(r).setValue(s.char).onChange(d=>{!d||(e.settings[i].char=d,e.saveSettings(),D(c,e.settings[i]))});let o=new b.ButtonComponent(r).then(d=>{s.icon?d.setIcon(s.icon):d.setButtonText("Set Icon"),We(d,n=>{n==null?delete e.settings[i].icon:e.settings[i].icon=n,e.saveSettings(),D(c,e.settings[i])})});if(new b.ButtonComponent(r).then(d=>{d.setButtonText("Clear Icon"),d.onClick(()=>{delete e.settings[i].icon,o.buttonEl.empty(),o.setButtonText("Set Icon"),e.saveSettings(),D(c,e.settings[i])})}),s.custom){let[d,n,u]=s.color.split(",").map(h=>parseInt(h.trim(),10)),m=new b.ColorComponent(r).setValueRgb({r:d,g:n,b:u}).onChange(h=>{let{r:g,g:f,b:w}=m.getValueRgb();e.settings[i].color=`${g}, ${f}, ${w}`,e.saveSettings(),D(c,e.settings[i])})}if(s.custom){let d=r.createDiv({cls:"lc-input-right-align"});new b.ButtonComponent(d).setButtonText("Delete").setWarning().onClick(n=>{l(i)})}})})}function Pi(a,e,i){let s={char:"",color:"158, 158, 158",icon:null,custom:!0};a.createDiv({cls:"lc-setting"},l=>{l.createDiv({cls:"setting-item-name"},h=>h.setText("Create a new Callout")),l.createDiv({cls:"setting-item-description"},h=>h.setText("Create additional list callout styles."));let t=l.createDiv({cls:"lc-callout-container"}),c=l.createDiv({cls:"lc-input-container"}),r=new b.TextComponent(c).setValue("").setPlaceholder("...").onChange(h=>{s.char=h,m()}),o=new b.ButtonComponent(c).setButtonText("Set Icon");We(o,h=>{h==null?delete s.icon:s.icon=h,m()});let d=new b.ColorComponent(c).setValueRgb({r:127,g:127,b:127}).onChange(h=>{let{r:g,g:f,b:w}=d.getValueRgb();s.color=`${g}, ${f}, ${w}`,m()}),n=c.createDiv({cls:"lc-input-right-align"}),u=new b.ButtonComponent(n).setButtonText("Create").setDisabled(!0).onClick(()=>{i(s)});function m(){D(t,s);let h=s.char.length===0,g=e.settings.find(f=>f.char===r.getValue())!==void 0;u.setDisabled(h||g)}m()})}var de=class extends b.PluginSettingTab{constructor(e){super(app,e);this.plugin=e}display(){let{containerEl:e}=this;e.empty(),new b.Setting(e).setDesc(createFragment(i=>{i.appendText("See the Style Settings plugin for additional configuration options."),i.append(createEl("br")),i.append(createEl("strong",{text:"Note: Using +, *, -, >, or # as the callout character can disrupt reading mode."}))})),this.plugin.settings.forEach((i,s)=>{Ni(e,this.plugin,s,i,l=>{this.plugin.settings.splice(l,1),this.plugin.saveSettings(),this.display()})}),Pi(e,this.plugin,i=>{this.plugin.settings.push(i),this.plugin.saveSettings(),this.display()})}};var qi=[{color:"255, 214, 0",char:"&"},{color:"255, 145, 0",char:"?"},{color:"255, 23, 68",char:"!"},{color:"124, 77, 255",char:"~"},{color:"0, 184, 212",char:"@"},{color:"0, 200, 83",char:"$"},{color:"158, 158, 158",char:"%"}],oe=class extends $.Plugin{constructor(){super(...arguments);this.emitSettingsUpdate=(0,$.debounce)(()=>this.dispatchUpdate(),2e3,!0)}onload(){return P(this,null,function*(){yield this.loadSettings(),this.buildPostProcessorConfig(),this.addSettingTab(new de(this)),this.emitter=new $.Events,this.registerMarkdownPostProcessor(xe(()=>this.postProcessorConfig),1e4),this.registerEditorExtension([Q.init(()=>this.buildEditorConfig()),ke]),app.workspace.trigger("parse-style-settings")})}dispatchUpdate(){let e=this.buildEditorConfig();app.workspace.getLeavesOfType("markdown").find(i=>{let l=i.view.editor.cm;l==null||l.dispatch({effects:[W.of(e)]})})}buildEditorConfig(){return{callouts:this.settings.reduce((e,i)=>(e[i.char]=i,e),{}),re:new RegExp(`(^\\s*[-*+](?: \\[.\\])? |^\\s*\\d+[\\.\\)](?: \\[.\\])? )(${this.settings.map(e=>H(e.char)).join("|")}) `)}}buildPostProcessorConfig(){this.postProcessorConfig={callouts:this.settings.reduce((e,i)=>(e[i.char]=i,e),{}),re:new RegExp(`^(${this.settings.map(e=>H(e.char)).join("|")}) `)}}loadSettings(){return P(this,null,function*(){let e=yield this.loadData(),i=e==null?void 0:e.filter(l=>l.custom===!0),s=e==null?void 0:e.filter(l=>l.custom!==!0);this.settings=qi.map((l,t)=>Object.assign({},l,s?s[t]:{})),i&&this.settings.push(...i)})}saveSettings(){return P(this,null,function*(){yield this.saveData(this.settings),this.emitSettingsUpdate(),this.buildPostProcessorConfig()})}};

/* nosourcemap */