"use strict";var tt=Object.create;var ee=Object.defineProperty;var it=Object.getOwnPropertyDescriptor;var nt=Object.getOwnPropertyNames;var st=Object.getPrototypeOf,ot=Object.prototype.hasOwnProperty;var at=(s,e)=>()=>(e||s((e={exports:{}}).exports,e),e.exports),rt=(s,e)=>{for(var t in e)ee(s,t,{get:e[t],enumerable:!0})},Te=(s,e,t,i)=>{if(e&&typeof e=="object"||typeof e=="function")for(let n of nt(e))!ot.call(s,n)&&n!==t&&ee(s,n,{get:()=>e[n],enumerable:!(i=it(e,n))||i.enumerable});return s};var lt=(s,e,t)=>(t=s!=null?tt(st(s)):{},Te(e||!s||!s.__esModule?ee(t,"default",{value:s,enumerable:!0}):t,s)),gt=s=>Te(ee({},"__esModule",{value:!0}),s);var Ze=at(d=>{"use strict";Object.defineProperty(d,"__esModule",{value:!0});var b=require("obsidian"),pe="YYYY-MM-DD",fe="gggg-[W]ww",Oe="YYYY-MM",Ve="YYYY-[Q]Q",Ue="YYYY";function q(s){var t,i;let e=window.app.plugins.getPlugin("periodic-notes");return e&&((i=(t=e.settings)==null?void 0:t[s])==null?void 0:i.enabled)}function G(){var s,e,t,i;try{let{internalPlugins:n,plugins:o}=window.app;if(q("daily")){let{format:g,folder:u,template:p}=((e=(s=o.getPlugin("periodic-notes"))==null?void 0:s.settings)==null?void 0:e.daily)||{};return{format:g||pe,folder:(u==null?void 0:u.trim())||"",template:(p==null?void 0:p.trim())||""}}let{folder:a,format:r,template:l}=((i=(t=n.getPluginById("daily-notes"))==null?void 0:t.instance)==null?void 0:i.options)||{};return{format:r||pe,folder:(a==null?void 0:a.trim())||"",template:(l==null?void 0:l.trim())||""}}catch(n){console.info("No custom daily note settings found!",n)}}function W(){var s,e,t,i,n,o,a;try{let r=window.app.plugins,l=(s=r.getPlugin("calendar"))==null?void 0:s.options,g=(t=(e=r.getPlugin("periodic-notes"))==null?void 0:e.settings)==null?void 0:t.weekly;if(q("weekly"))return{format:g.format||fe,folder:((i=g.folder)==null?void 0:i.trim())||"",template:((n=g.template)==null?void 0:n.trim())||""};let u=l||{};return{format:u.weeklyNoteFormat||fe,folder:((o=u.weeklyNoteFolder)==null?void 0:o.trim())||"",template:((a=u.weeklyNoteTemplate)==null?void 0:a.trim())||""}}catch(r){console.info("No custom weekly note settings found!",r)}}function J(){var e,t,i,n;let s=window.app.plugins;try{let o=q("monthly")&&((t=(e=s.getPlugin("periodic-notes"))==null?void 0:e.settings)==null?void 0:t.monthly)||{};return{format:o.format||Oe,folder:((i=o.folder)==null?void 0:i.trim())||"",template:((n=o.template)==null?void 0:n.trim())||""}}catch(o){console.info("No custom monthly note settings found!",o)}}function Q(){var e,t,i,n;let s=window.app.plugins;try{let o=q("quarterly")&&((t=(e=s.getPlugin("periodic-notes"))==null?void 0:e.settings)==null?void 0:t.quarterly)||{};return{format:o.format||Ve,folder:((i=o.folder)==null?void 0:i.trim())||"",template:((n=o.template)==null?void 0:n.trim())||""}}catch(o){console.info("No custom quarterly note settings found!",o)}}function Z(){var e,t,i,n;let s=window.app.plugins;try{let o=q("yearly")&&((t=(e=s.getPlugin("periodic-notes"))==null?void 0:e.settings)==null?void 0:t.yearly)||{};return{format:o.format||Ue,folder:((i=o.folder)==null?void 0:i.trim())||"",template:((n=o.template)==null?void 0:n.trim())||""}}catch(o){console.info("No custom yearly note settings found!",o)}}function He(...s){let e=[];for(let i=0,n=s.length;i<n;i++)e=e.concat(s[i].split("/"));let t=[];for(let i=0,n=e.length;i<n;i++){let o=e[i];!o||o==="."||t.push(o)}return e[0]===""&&t.unshift(""),t.join("/")}function ht(s){let e=s.substring(s.lastIndexOf("/")+1);return e.lastIndexOf(".")!=-1&&(e=e.substring(0,e.lastIndexOf("."))),e}async function bt(s){let e=s.replace(/\\/g,"/").split("/");if(e.pop(),e.length){let t=He(...e);window.app.vault.getAbstractFileByPath(t)||await window.app.vault.createFolder(t)}}async function K(s,e){e.endsWith(".md")||(e+=".md");let t=b.normalizePath(He(s,e));return await bt(t),t}async function M(s){let{metadataCache:e,vault:t}=window.app,i=b.normalizePath(s);if(i==="/")return Promise.resolve(["",null]);try{let n=e.getFirstLinkpathDest(i,""),o=await t.cachedRead(n),a=window.app.foldManager.load(n);return[o,a]}catch(n){return console.error(`Failed to read the daily note template '${i}'`,n),new b.Notice("读取日报模板失败"),["",null]}}function S(s,e="day"){let t=s.clone().startOf(e).format();return`${e}-${t}`}function _e(s){return s.replace(/\[[^\]]*\]/g,"")}function wt(s,e){if(e==="week"){let t=_e(s);return/w{1,2}/i.test(t)&&(/M{1,4}/.test(t)||/D{1,4}/.test(t))}return!1}function E(s,e){return ze(s.basename,e)}function yt(s,e){return ze(ht(s),e)}function ze(s,e){let i={day:G,week:W,month:J,quarter:Q,year:Z}[e]().format.split("/").pop(),n=window.moment(s,i,!0);if(!n.isValid())return null;if(wt(i,e)&&e==="week"){let o=_e(i);if(/w{1,2}/i.test(o))return window.moment(s,i.replace(/M{1,4}/g,"").replace(/D{1,4}/g,""),!1)}return n}var he=class extends Error{};async function Ye(s){let e=window.app,{vault:t}=e,i=window.moment,{template:n,format:o,folder:a}=G(),[r,l]=await M(n),g=s.format(o),u=await K(a,g);try{let p=await t.create(u,r.replace(/{{\s*date\s*}}/gi,g).replace(/{{\s*time\s*}}/gi,i().format("HH:mm")).replace(/{{\s*title\s*}}/gi,g).replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi,(T,m,w,h,f,y)=>{let D=i(),re=s.clone().set({hour:D.get("hour"),minute:D.get("minute"),second:D.get("second")});return w&&re.add(parseInt(h,10),f),y?re.format(y.substring(1).trim()):re.format(o)}).replace(/{{\s*yesterday\s*}}/gi,s.clone().subtract(1,"day").format(o)).replace(/{{\s*tomorrow\s*}}/gi,s.clone().add(1,"d").format(o)));return e.foldManager.save(p,l),p}catch(p){console.error(`Failed to create file: '${u}'`,p),new b.Notice("无法创建新文件。")}}function vt(s,e){var t;return(t=e[S(s,"day")])!=null?t:null}function Tt(){let{vault:s}=window.app,{folder:e}=G(),t=s.getAbstractFileByPath(b.normalizePath(e));if(!t)throw new he("Failed to find daily notes folder");let i={};return b.Vault.recurseChildren(t,n=>{if(n instanceof b.TFile){let o=E(n,"day");if(o){let a=S(o,"day");i[a]=n}}}),i}var be=class extends Error{};function Pt(){let{moment:s}=window,e=s.localeData()._week.dow,t=["sunday","monday","tuesday","wednesday","thursday","friday","saturday"];for(;e;)t.push(t.shift()),e--;return t}function Ct(s){return Pt().indexOf(s.toLowerCase())}async function je(s){let{vault:e}=window.app,{template:t,format:i,folder:n}=W(),[o,a]=await M(t),r=s.format(i),l=await K(n,r);try{let g=await e.create(l,o.replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi,(u,p,T,m,w,h)=>{let f=window.moment(),y=s.clone().set({hour:f.get("hour"),minute:f.get("minute"),second:f.get("second")});return T&&y.add(parseInt(m,10),w),h?y.format(h.substring(1).trim()):y.format(i)}).replace(/{{\s*title\s*}}/gi,r).replace(/{{\s*time\s*}}/gi,window.moment().format("HH:mm")).replace(/{{\s*(sunday|monday|tuesday|wednesday|thursday|friday|saturday)\s*:(.*?)}}/gi,(u,p,T)=>{let m=Ct(p);return s.weekday(m).format(T.trim())}));return window.app.foldManager.save(g,a),g}catch(g){console.error(`Failed to create file: '${l}'`,g),new b.Notice("无法创建新文件。")}}function St(s,e){var t;return(t=e[S(s,"week")])!=null?t:null}function At(){let s={};if(!Ge())return s;let{vault:e}=window.app,{folder:t}=W(),i=e.getAbstractFileByPath(b.normalizePath(t));if(!i)throw new be("Failed to find weekly notes folder");return b.Vault.recurseChildren(i,n=>{if(n instanceof b.TFile){let o=E(n,"week");if(o){let a=S(o,"week");s[a]=n}}}),s}var we=class extends Error{};async function qe(s){let{vault:e}=window.app,{template:t,format:i,folder:n}=J(),[o,a]=await M(t),r=s.format(i),l=await K(n,r);try{let g=await e.create(l,o.replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi,(u,p,T,m,w,h)=>{let f=window.moment(),y=s.clone().set({hour:f.get("hour"),minute:f.get("minute"),second:f.get("second")});return T&&y.add(parseInt(m,10),w),h?y.format(h.substring(1).trim()):y.format(i)}).replace(/{{\s*date\s*}}/gi,r).replace(/{{\s*time\s*}}/gi,window.moment().format("HH:mm")).replace(/{{\s*title\s*}}/gi,r));return window.app.foldManager.save(g,a),g}catch(g){console.error(`Failed to create file: '${l}'`,g),new b.Notice("无法创建新文件。")}}function Ft(s,e){var t;return(t=e[S(s,"month")])!=null?t:null}function kt(){let s={};if(!We())return s;let{vault:e}=window.app,{folder:t}=J(),i=e.getAbstractFileByPath(b.normalizePath(t));if(!i)throw new we("Failed to find monthly notes folder");return b.Vault.recurseChildren(i,n=>{if(n instanceof b.TFile){let o=E(n,"month");if(o){let a=S(o,"month");s[a]=n}}}),s}var ye=class extends Error{};async function Nt(s){let{vault:e}=window.app,{template:t,format:i,folder:n}=Q(),[o,a]=await M(t),r=s.format(i),l=await K(n,r);try{let g=await e.create(l,o.replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi,(u,p,T,m,w,h)=>{let f=window.moment(),y=s.clone().set({hour:f.get("hour"),minute:f.get("minute"),second:f.get("second")});return T&&y.add(parseInt(m,10),w),h?y.format(h.substring(1).trim()):y.format(i)}).replace(/{{\s*date\s*}}/gi,r).replace(/{{\s*time\s*}}/gi,window.moment().format("HH:mm")).replace(/{{\s*title\s*}}/gi,r));return window.app.foldManager.save(g,a),g}catch(g){console.error(`Failed to create file: '${l}'`,g),new b.Notice("无法创建新文件。")}}function xt(s,e){var t;return(t=e[S(s,"quarter")])!=null?t:null}function Lt(){let s={};if(!Je())return s;let{vault:e}=window.app,{folder:t}=Q(),i=e.getAbstractFileByPath(b.normalizePath(t));if(!i)throw new ye("Failed to find quarterly notes folder");return b.Vault.recurseChildren(i,n=>{if(n instanceof b.TFile){let o=E(n,"quarter");if(o){let a=S(o,"quarter");s[a]=n}}}),s}var ve=class extends Error{};async function Dt(s){let{vault:e}=window.app,{template:t,format:i,folder:n}=Z(),[o,a]=await M(t),r=s.format(i),l=await K(n,r);try{let g=await e.create(l,o.replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi,(u,p,T,m,w,h)=>{let f=window.moment(),y=s.clone().set({hour:f.get("hour"),minute:f.get("minute"),second:f.get("second")});return T&&y.add(parseInt(m,10),w),h?y.format(h.substring(1).trim()):y.format(i)}).replace(/{{\s*date\s*}}/gi,r).replace(/{{\s*time\s*}}/gi,window.moment().format("HH:mm")).replace(/{{\s*title\s*}}/gi,r));return window.app.foldManager.save(g,a),g}catch(g){console.error(`Failed to create file: '${l}'`,g),new b.Notice("无法创建新文件。")}}function Bt(s,e){var t;return(t=e[S(s,"year")])!=null?t:null}function Rt(){let s={};if(!Qe())return s;let{vault:e}=window.app,{folder:t}=Z(),i=e.getAbstractFileByPath(b.normalizePath(t));if(!i)throw new ve("Failed to find yearly notes folder");return b.Vault.recurseChildren(i,n=>{if(n instanceof b.TFile){let o=E(n,"year");if(o){let a=S(o,"year");s[a]=n}}}),s}function $t(){var i,n;let{app:s}=window,e=s.internalPlugins.plugins["daily-notes"];if(e&&e.enabled)return!0;let t=s.plugins.getPlugin("periodic-notes");return t&&((n=(i=t.settings)==null?void 0:i.daily)==null?void 0:n.enabled)}function Ge(){var t,i;let{app:s}=window;if(s.plugins.getPlugin("calendar"))return!0;let e=s.plugins.getPlugin("periodic-notes");return e&&((i=(t=e.settings)==null?void 0:t.weekly)==null?void 0:i.enabled)}function We(){var t,i;let{app:s}=window,e=s.plugins.getPlugin("periodic-notes");return e&&((i=(t=e.settings)==null?void 0:t.monthly)==null?void 0:i.enabled)}function Je(){var t,i;let{app:s}=window,e=s.plugins.getPlugin("periodic-notes");return e&&((i=(t=e.settings)==null?void 0:t.quarterly)==null?void 0:i.enabled)}function Qe(){var t,i;let{app:s}=window,e=s.plugins.getPlugin("periodic-notes");return e&&((i=(t=e.settings)==null?void 0:t.yearly)==null?void 0:i.enabled)}function Mt(s){let e={day:G,week:W,month:J,quarter:Q,year:Z}[s];return e()}function Et(s,e){return{day:Ye,month:qe,week:je}[s](e)}d.DEFAULT_DAILY_NOTE_FORMAT=pe;d.DEFAULT_MONTHLY_NOTE_FORMAT=Oe;d.DEFAULT_QUARTERLY_NOTE_FORMAT=Ve;d.DEFAULT_WEEKLY_NOTE_FORMAT=fe;d.DEFAULT_YEARLY_NOTE_FORMAT=Ue;d.appHasDailyNotesPluginLoaded=$t;d.appHasMonthlyNotesPluginLoaded=We;d.appHasQuarterlyNotesPluginLoaded=Je;d.appHasWeeklyNotesPluginLoaded=Ge;d.appHasYearlyNotesPluginLoaded=Qe;d.createDailyNote=Ye;d.createMonthlyNote=qe;d.createPeriodicNote=Et;d.createQuarterlyNote=Nt;d.createWeeklyNote=je;d.createYearlyNote=Dt;d.getAllDailyNotes=Tt;d.getAllMonthlyNotes=kt;d.getAllQuarterlyNotes=Lt;d.getAllWeeklyNotes=At;d.getAllYearlyNotes=Rt;d.getDailyNote=vt;d.getDailyNoteSettings=G;d.getDateFromFile=E;d.getDateFromPath=yt;d.getDateUID=S;d.getMonthlyNote=Ft;d.getMonthlyNoteSettings=J;d.getPeriodicNoteSettings=Mt;d.getQuarterlyNote=xt;d.getQuarterlyNoteSettings=Q;d.getTemplateInfo=M;d.getWeeklyNote=St;d.getWeeklyNoteSettings=W;d.getYearlyNote=Bt;d.getYearlyNoteSettings=Z});var It={};rt(It,{default:()=>ae});module.exports=gt(It);var et=require("obsidian");var ut=/^[v^~<>=]*?(\d+)(?:\.([x*]|\d+)(?:\.([x*]|\d+)(?:\.([x*]|\d+))?(?:-([\da-z\-]+(?:\.[\da-z\-]+)*))?(?:\+[\da-z\-]+(?:\.[\da-z\-]+)*)?)?)?$/i,le=s=>{if(typeof s!="string")throw new TypeError("Invalid argument expected string");let e=s.match(ut);if(!e)throw new Error(`Invalid argument not valid semver ('${s}' received)`);return e.shift(),e},Pe=s=>s==="*"||s==="x"||s==="X",Ce=s=>{let e=parseInt(s,10);return isNaN(e)?s:e},dt=(s,e)=>typeof s!=typeof e?[String(s),String(e)]:[s,e],ct=(s,e)=>{if(Pe(s)||Pe(e))return 0;let[t,i]=dt(Ce(s),Ce(e));return t>i?1:t<i?-1:0},ge=(s,e)=>{for(let t=0;t<Math.max(s.length,e.length);t++){let i=ct(s[t]||"0",e[t]||"0");if(i!==0)return i}return 0};var I=(s,e)=>{let t=le(s),i=le(e),n=t.pop(),o=i.pop(),a=ge(t,i);return a!==0?a:n&&o?ge(n.split("."),o.split(".")):n||o?n?-1:1:0};var A=require("obsidian");var P=class extends Error{constructor(t,i,n,o){let a=Math.ceil((n-Math.floor(Date.now()/1e3))/60);super(`GitHub API rate limit exceeded. Reset in ${a} minutes.`);this.limit=t;this.remaining=i;this.reset=n;this.requestUrl=o;this.name="GitHubRateLimitError"}getMinutesToReset(){return Math.ceil((this.reset-Math.floor(Date.now()/1e3))/60)}};var k=require("obsidian");var ue=async(s,e=!0,t="")=>{let i=`https://api.github.com/repos/${s}`;try{let n=await ce({url:i,headers:t?{Authorization:`Token ${t}`}:{}});return(await JSON.parse(n)).private}catch(n){if(n instanceof P)throw n;return e&&console.log("error in isPrivateRepo",i,n),!1}},Se=async(s,e=!0,t="")=>{let i=`https://api.github.com/repos/${s}/releases`;try{let n=await ce({url:i,headers:t?{Authorization:`Token ${t}`}:{}});return(await JSON.parse(n)).map(a=>({version:a.tag_name,prerelease:a.prerelease}))}catch(n){if(n instanceof P)throw n;return e&&console.log("error in fetchReleaseVersions",i,n),null}},O=async(s,e,t=!0,i=!1,n="")=>{try{let o=s.assets.find(l=>l.name===e);if(!o)return null;let a={Accept:"application/octet-stream"};(i&&n||n)&&(a.Authorization=`Token ${n}`);let r=await(0,k.request)({url:o.url,headers:a});return r==="Not Found"||r==='{"error":"Not Found"}'?null:r}catch(o){if(o instanceof P)throw o;return t&&console.log("GrabreleaseFileFromRepository中的错误",URL,o),null}},Ae=async(s=!0)=>{let e="https://raw.githubusercontent.com/obsidianmd/obsidian-releases/HEAD/community-plugins.json";try{let t=await(0,k.request)({url:e});return t==="404: Not Found"?null:await JSON.parse(t)}catch(t){return s&&console.log("error in grabCommmunityPluginList",t),null}},Fe=async(s=!0)=>{let e="https://raw.githubusercontent.com/obsidianmd/obsidian-releases/HEAD/community-css-themes.json";try{let t=await(0,k.request)({url:e});return t==="404: Not Found"?null:await JSON.parse(t)}catch(t){return s&&console.log("error in grabCommmunityThemesList",t),null}},B=async(s,e=!1,t=!1)=>{let i=`https://raw.githubusercontent.com/${s}/HEAD/theme${e?"-beta":""}.css`;try{let n=await(0,k.request)({url:i});return n==="404: Not Found"?null:n}catch(n){return t&&console.log("error in grabCommmunityThemeCssFile",n),null}},ke=async(s,e=!0)=>{let t=`https://raw.githubusercontent.com/${s}/HEAD/manifest.json`;try{let i=await(0,k.request)({url:t});return i==="404: Not Found"?null:i}catch(i){return e&&console.log("error in grabCommmunityThemeManifestFile",i),null}},mt=s=>{let e=0;for(let t=0;t<s.length;t++)e+=s.charCodeAt(t);return e},V=s=>mt(s).toString(),U=async(s,e,t)=>{let i=await B(s,e,t);return i?V(i):"0"},pt=async(s,e,t=!0)=>{let i=`https://api.github.com/repos/${s}/commits?path=${e}&page=1&per_page=1`;try{let n=await(0,k.request)({url:i});return n==="404: Not Found"?null:JSON.parse(n)}catch(n){return t&&console.log("error in grabLastCommitInfoForAFile",n),null}},Ne=async(s,e)=>{var i;let t=await pt(s,e);return t&&t.length>0&&((i=t[0].commit.committer)!=null&&i.date)?t[0].commit.committer.date:""},de=async(s,e,t=!1,i=!1,n=!1,o)=>{var a;try{let r=e&&e!=="latest"?`https://api.github.com/repos/${s}/releases/tags/${e}`:`https://api.github.com/repos/${s}/releases`,l={Accept:"application/vnd.github.v3+json"};(n&&o||o)&&(l.Authorization=`Token ${o}`);let g=await ce({url:r,headers:l});if(g==="404: Not Found")return null;let u=e&&e!=="latest"?[JSON.parse(g)]:JSON.parse(g);return i&&console.log(`grabReleaseFromRepository for ${s}:`,u),(a=u.sort((p,T)=>I(T.tag_name,p.tag_name)).filter(p=>t||!p.prerelease)[0])!=null?a:null}catch(r){if(r instanceof P)throw r;return i&&console.log(`Error in grabReleaseFromRepository for ${s}:`,r),null}},ce=async(s,e)=>{let t=0,i=0,n=0,o=Math.floor(Date.now()/1e3);try{return await(0,k.request)(s)}catch(a){let r=a,l=r.headers;if(l&&(t=Number.parseInt(l["x-ratelimit-limit"]),i=Number.parseInt(l["x-ratelimit-remaining"]),n=Number.parseInt(l["x-ratelimit-reset"])),r.status===403&&i===0){let g=new P(t,i,n,s.url);throw e&&console.error(`BRAT
GitHub API rate limit exceeded:`,`
Request: ${g.requestUrl}`,`
Rate limits - Remaining: ${g.remaining}`,`
Reset in: ${g.getMinutesToReset()} minutes`),g}throw r.status===404?new Error("404: Not Found"):r.status>=400?new Error(`GitHub API returned status ${r.status}`):(e&&console.log("GitHub request failed:",a),a)}};var me={pluginList:[],pluginSubListFrozenVersion:[],themesList:[],updateAtStartup:!0,updateThemesAtStartup:!0,enableAfterInstall:!0,loggingEnabled:!1,loggingPath:"BRAT-log",loggingVerboseEnabled:!1,debuggingMode:!1,notificationsEnabled:!0,personalAccessToken:""};function xe(s,e,t="latest",i=""){let n=!1;s.settings.pluginList.contains(e)||(s.settings.pluginList.unshift(e),n=!0),s.settings.pluginSubListFrozenVersion.filter(o=>o.repo===e).length===0&&(s.settings.pluginSubListFrozenVersion.unshift({repo:e,version:t,token:i||void 0}),n=!0),n&&s.saveSettings()}function Le(s,e){return s.settings.pluginList.contains(e)}function De(s,e,t){let i={repo:e,lastUpdate:V(t)};s.settings.themesList.unshift(i),s.saveSettings()}function Be(s,e){return!!s.settings.themesList.find(i=>i.repo===e)}function Re(s,e,t){for(let i of s.settings.themesList)i.repo===e&&(i.lastUpdate=t,s.saveSettings())}var N=require("obsidian");function H(s,e){let t=new DocumentFragment,i=document.createElement("a");if(i.textContent=s,i.href=`https://github.com/${s}`,i.target="_blank",t.appendChild(i),e){let n=document.createTextNode(e);t.appendChild(n)}return t}var te=require("obsidian");function c(s,e,t=10,i){if(!s.settings.notificationsEnabled)return;let n=i?te.Platform.isDesktop?"(click=dismiss, right-click=Info)":"(click=dismiss)":"",o=new te.Notice(`BRAT
${e}
${n}`,t*1e3);i&&(o.noticeEl.oncontextmenu=()=>{i()})}var R=(s,e=!0)=>{let t=s.createEl("div");t.style.float="right",e?(t.style.padding="15px",t.style.paddingLeft="15px",t.style.paddingRight="15px",t.style.marginLeft="15px"):(t.style.padding="10px",t.style.paddingLeft="15px",t.style.paddingRight="15px");let i=t.createDiv("coffee");i.addClass("ex-twitter-span"),i.style.paddingLeft="10px";let n=i.createDiv();n.innerText="了解更多关于我的工作：",i.appendChild(n);let o=i.createEl("a",{href:"https://tfthacker.com"});return o.innerText="https://tfthacker.com",t};var x=class extends N.Modal{constructor(e,t,i=!1,n=!1,o="",a="",r=""){super(e.app),this.plugin=e,this.betaPlugins=t,this.address=o,this.version=a,this.privateApiKey=r,this.openSettingsTabAfterwards=i,this.trackFixedVersion=n,this.enableAfterInstall=e.settings.enableAfterInstall,this.versionSetting=null,this.addPluginButton=null}async submitForm(){if(this.address==="")return;let e=this.address.replace("https://github.com/","");e.endsWith(".git")&&(e=e.slice(0,-4));let t=this.plugin.settings.pluginSubListFrozenVersion.find(n=>n.repo===e);if(t){t.version=this.version,this.privateApiKey&&(t.token=this.privateApiKey),await this.plugin.saveSettings(),await this.betaPlugins.addPlugin(e,!1,!1,!1,this.version,!0,this.enableAfterInstall,this.privateApiKey)&&this.close();return}if(!this.version&&Le(this.plugin,e)){c(this.plugin,"This plugin is already in the list for beta testing",10);return}await this.betaPlugins.addPlugin(e,!1,!1,!1,this.version,!1,this.enableAfterInstall,this.privateApiKey)&&this.close()}updateVersionDropdown(e,t,i=""){e.clear(),e.addDropdown(n=>{n.addOption("","Select a version"),n.addOption("latest","Latest Version");for(let o of t)n.addOption(o.version,`${o.version} ${o.prerelease?"(Prerelease)":""}`);n.setValue(i),n.onChange(o=>{this.version=o,this.addPluginButton&&(this.version!==""?this.addPluginButton.setDisabled(!1):this.addPluginButton.setDisabled(!0))}),n.selectEl.style.width="100%"})}onOpen(){let e=this.contentEl.createEl("h4");this.address?(e.appendText("Change plugin version: "),e.appendChild(H(this.address))):e.setText("Github repository for beta plugin:"),this.contentEl.createEl("form",{},t=>{if(t.addClass("brat-modal"),!this.address||!this.trackFixedVersion){let o=new N.Setting(t).setClass("repository-setting"),a=o.settingEl.createDiv("validation-status");a.style.color="var(--text-error)",a.style.marginTop="6px",a.style.fontSize="0.8em",o.then(r=>{r.addText(l=>{l.setPlaceholder("存储库（示例：https://github.com/GitubUserName/repository-name)"),l.setValue(this.address),l.onChange(g=>{var u,p;this.address=g.trim(),this.trackFixedVersion&&(!this.address||!this.isGitHubRepositoryMatch(this.address))&&this.versionSetting&&(this.updateVersionDropdown(this.versionSetting,[]),this.versionSetting.settingEl.classList.add("disabled-setting"),this.versionSetting.setDisabled(!0),l.inputEl.classList.remove("valid-repository"),l.inputEl.classList.remove("invalid-repository")),this.trackFixedVersion||(this.isGitHubRepositoryMatch(this.address)?(u=this.addPluginButton)==null||u.setDisabled(!1):(p=this.addPluginButton)==null||p.setDisabled(!0))}),l.inputEl.addEventListener("keydown",async g=>{g.key==="Enter"&&(this.address&&(this.trackFixedVersion&&this.version!==""||!this.trackFixedVersion)&&(g.preventDefault(),this.submitForm()),await this.updateRepositoryVersionInfo(this.version,l))}),this.trackFixedVersion&&l.inputEl.addEventListener("blur",async()=>{await this.updateRepositoryVersionInfo(this.version,l,a)}),l.inputEl.style.width="100%"})})}this.trackFixedVersion&&(new N.Setting(t).setClass("api-setting").addText(o=>{o.setPlaceholder("GitHub API key for private repository (optional)").setValue(this.privateApiKey).onChange(async a=>{this.privateApiKey=a.trim(),this.address&&await this.updateRepositoryVersionInfo(this.version,o)}),o.inputEl.type="password",o.inputEl.style.width="100%"}),this.versionSetting=new N.Setting(t).setClass("version-setting").setClass("disabled-setting"),this.updateVersionDropdown(this.versionSetting,[],this.version),this.versionSetting.setDisabled(!0)),t.createDiv("modal-button-container",o=>{var a;o.createEl("label",{cls:"mod-checkbox"},r=>{let l=r.createEl("input",{attr:{tabindex:-1},type:"checkbox"});l.checked=this.enableAfterInstall,l.addEventListener("click",()=>{this.enableAfterInstall=l.checked}),r.appendText("安装插件后启用")}),o.createEl("button",{attr:{type:"button"},text:"没关系"}).addEventListener("click",()=>{this.close()}),this.addPluginButton=new N.ButtonComponent(o).setButtonText(this.trackFixedVersion&&this.address?"Change Version":"Add Plugin").setClass("mod-cta").onClick(r=>{var l;r.preventDefault(),this.address!==""&&(this.trackFixedVersion&&this.version!==""||!this.trackFixedVersion)&&((l=this.addPluginButton)==null||l.setDisabled(!0),this.submitForm())}),(this.trackFixedVersion||this.address==="")&&((a=this.addPluginButton)==null||a.setDisabled(!0))});let i=t.createDiv();i.style.borderTop="1px solid #ccc",i.style.marginTop="30px";let n=i.createSpan();n.innerHTML="BRAT by <a href='https://bit.ly/o42-twitter'>TFTHacker</a>",n.style.fontStyle="italic",i.appendChild(n),R(i,!1),window.setTimeout(()=>{let o=t.querySelectorAll(".brat-modal .setting-item-info");for(let a of Array.from(o))a.remove()},50),t.addEventListener("submit",o=>{var a;o.preventDefault(),this.address!==""&&(this.trackFixedVersion&&this.version!==""||!this.trackFixedVersion)&&((a=this.addPluginButton)==null||a.setDisabled(!0),this.submitForm())})}),this.address&&window.setTimeout(async()=>{await this.updateRepositoryVersionInfo(this.version)},100)}async updateRepositoryVersionInfo(e="",t,i){if(this.plugin.settings.debuggingMode&&console.log(`[BRAT] Updating version dropdown for ${this.address} with selected version ${e}`),!this.address){i&&i.setText("Repository address is required.");return}this.versionSetting&&this.trackFixedVersion&&this.updateVersionDropdown(this.versionSetting,[],e);let n=this.address.replace("https://github.com/","");n.endsWith(".git")&&(n=n.slice(0,-4));try{let o=await Se(n,this.plugin.settings.debuggingMode,this.privateApiKey||this.plugin.settings.personalAccessToken);o&&o.length>0?(t==null||t.inputEl.classList.remove("invalid-repository"),t==null||t.inputEl.classList.add("valid-repository"),this.versionSetting&&(this.versionSetting.settingEl.classList.remove("disabled-setting"),this.versionSetting.setDisabled(!1),this.updateVersionDropdown(this.versionSetting,o,e))):(t==null||t.inputEl.classList.remove("valid-repository"),t==null||t.inputEl.classList.add("invalid-repository"),this.versionSetting&&(this.versionSetting.settingEl.classList.add("disabled-setting"),this.versionSetting.setDisabled(!0),this.addPluginButton&&(this.addPluginButton.disabled=!0)))}catch(o){o instanceof P&&(t==null||t.inputEl.classList.remove("valid-repository"),t==null||t.inputEl.classList.add("validation-error"),i==null||i.setText(`GitHub API rate limit exceeded. Try again in ${o.getMinutesToReset()} minutes.`),this.versionSetting&&(this.versionSetting.settingEl.classList.add("disabled-setting"),this.versionSetting.setDisabled(!0),this.addPluginButton&&(this.addPluginButton.disabled=!0)),c(this.plugin,`${o.message} Consider adding a personal access token in BRAT settings for higher limits. See documentation for details.`,20,()=>{window.open("https://github.com/TfTHacker/obsidian42-brat/blob/main/BRAT-DEVELOPER-GUIDE.md#github-api-rate-limits")}))}}onClose(){this.openSettingsTabAfterwards&&(this.plugin.app.setting.open(),this.plugin.app.setting.openTabById(this.plugin.APP_ID))}isGitHubRepositoryMatch(e){let t=e.trim().replace(/\.git$/,"");return/^(?:https:\/\/github\.com\/)?([a-zA-Z0-9._-]+)\/([a-zA-Z0-9._-]+)$/.test(t)}};var $e=require("obsidian");async function ie(){try{let s=await(0,$e.requestUrl)(`https://obsidian.md/?${Math.random()}`);return s.status>=200&&s.status<300}catch(s){return!1}}var _=class{constructor(e){this.plugin=e}displayAddNewPluginModal(e=!1,t=!1,i="",n="",o=""){new x(this.plugin,this,e,t,i,n,o).open()}async validateRepository(e,t=!1,i=!1,n="",o=""){try{let r=await ue(e,this.plugin.settings.debuggingMode,o||this.plugin.settings.personalAccessToken),l=await de(e,n,t,this.plugin.settings.debuggingMode,r,o||this.plugin.settings.personalAccessToken);if(!l)return i&&(c(this.plugin,`${e}
This does not seem to be an obsidian plugin with valid releases, as there are no releases available.`,15),console.error("BRAT: validateRepository",e,t,i)),null;let g=await O(l,"manifest.json",this.plugin.settings.debuggingMode,r,o||this.plugin.settings.personalAccessToken);if(!g)return i&&(c(this.plugin,`${e}
This does not seem to be an obsidian plugin, as there is no manifest.json file.`,15),console.error("BRAT: validateRepository",e,t,i)),null;let u=JSON.parse(g);return"id"in u?"version"in u?u:(i&&c(this.plugin,`${e}
The version attribute for the release is missing from the manifest file`,15),null):(i&&c(this.plugin,`${e}
The plugin id attribute for the release is missing from the manifest file`,15),null)}catch(r){if(r instanceof P){let l=`GitHub API rate limit exceeded. Reset in ${r.getMinutesToReset()} minutes.`;throw i&&c(this.plugin,l,15),console.error(`BRAT: validateRepository ${r}`),c(this.plugin,`${r.message} Consider adding a personal access token in BRAT settings for higher limits. See documentation for details.`,20,()=>{window.open("https://github.com/TfTHacker/obsidian42-brat/blob/main/BRAT-DEVELOPER-GUIDE.md#github-api-rate-limits")}),r}return i&&c(this.plugin,`${e}
Unspecified error encountered: ${r}, verify debug for more information.`,15),null}}async getAllReleaseFiles(e,t,i,n="",o=""){let a=await ue(e,this.plugin.settings.debuggingMode,o),r=await de(e,n,i,this.plugin.settings.debuggingMode,a,o||this.plugin.settings.personalAccessToken);if(!r)return Promise.reject("No release found");let l=i||n!=="";return console.log({reallyGetManifestOrNot:l,version:r.tag_name}),{mainJs:await O(r,"main.js",this.plugin.settings.debuggingMode,a,o||this.plugin.settings.personalAccessToken),manifest:l?await O(r,"manifest.json",this.plugin.settings.debuggingMode,a,o||this.plugin.settings.personalAccessToken):"",styles:await O(r,"styles.css",this.plugin.settings.debuggingMode,a,o||this.plugin.settings.personalAccessToken)}}async writeReleaseFilesToPluginFolder(e,t){var o,a;let i=`${(0,A.normalizePath)(`${this.plugin.app.vault.configDir}/plugins/${e}`)}/`,{adapter:n}=this.plugin.app.vault;await n.exists(i)||await n.mkdir(i),await n.write(`${i}main.js`,(o=t.mainJs)!=null?o:""),await n.write(`${i}manifest.json`,(a=t.manifest)!=null?a:""),t.styles&&await n.write(`${i}styles.css`,t.styles)}async addPlugin(e,t=!1,i=!1,n=!1,o="",a=!1,r=this.plugin.settings.enableAfterInstall,l=""){this.plugin.settings.debuggingMode&&console.log("BRAT: addPlugin",e,t,i,n,o,a,r,l?"private":"public");let g=10,u=await this.validateRepository(e,!0,!1,o,l),p=!!u;if(p||(u=await this.validateRepository(e,!1,!0,o,l)),u===null){let m=`${e}
A manifest.json file does not exist in the latest release of the repository. This plugin cannot be installed.`;return await this.plugin.log(m,!0),c(this.plugin,m,g),!1}if(!Object.hasOwn(u,"version")){let m=`${e}
The manifest.json file in the latest release or pre-release of the repository does not have a version number in the file. This plugin cannot be installed.`;return await this.plugin.log(m,!0),c(this.plugin,m,g),!1}if(!Object.hasOwn(u,"minAppVersion")&&!(0,A.requireApiVersion)(u.minAppVersion)){let m=`Plugin: ${e}

The manifest.json for this plugin indicates that the Obsidian version of the app needs to be ${u.minAppVersion}, but this installation of Obsidian is ${A.apiVersion}. 

You will need to update your Obsidian to use this plugin or contact the plugin developer for more information.`;return await this.plugin.log(m,!0),c(this.plugin,m,30),!1}let T=async()=>{let m=await this.getAllReleaseFiles(e,u,p,o,l);if(console.log("rFiles",m),(p||m.manifest==="")&&(m.manifest=JSON.stringify(u)),this.plugin.settings.debuggingMode&&console.log("BRAT: rFiles.manifest",p,m),m.mainJs===null){let w=`${e}
The release is not complete and cannot be download. main.js is missing from the Release`;return await this.plugin.log(w,!0),c(this.plugin,w,g),null}return m};if(!t||a){let m=await T();if(m===null)return!1;if(await this.writeReleaseFilesToPluginFolder(u.id,m),a||xe(this.plugin,e,o,l),r){let{plugins:w}=this.plugin.app,h=(0,A.normalizePath)(`${w.getPluginFolder()}/${u.id}`);await w.loadManifest(h),await w.enablePluginAndSave(u.id)}if(await this.plugin.app.plugins.loadManifests(),a)await this.reloadPlugin(u.id),await this.plugin.log(`${e} reinstalled`,!0),c(this.plugin,`${e}
Plugin has been reinstalled and reloaded with version ${u.version}`,g);else{let w=o===""?"":` (version: ${o})`,h=`${e}${w}
The plugin has been registered with BRAT.`;r||(h+=" You may still need to enable it the Community Plugin List."),await this.plugin.log(h,!0),c(this.plugin,h,g)}}else{let m=`${this.plugin.app.vault.configDir}/plugins/${u.id}/`,w="";try{w=await this.plugin.app.vault.adapter.read(`${m}manifest.json`)}catch(f){if(f.errno===-4058||f.errno===-2)return await this.addPlugin(e,!1,p,!1,o,!1,r,l),!0;console.log("BRAT - Local Manifest Load",u.id,JSON.stringify(f,null,2))}if(o!==""&&o!=="latest")return c(this.plugin,`The version of ${e} is frozen, not updating.`,3),!1;let h=await JSON.parse(w);if(I(h.version,u.version)===-1){let f=await T();if(f===null)return!1;if(i){let D=`There is an update available for ${u.id} from version ${h.version} to ${u.version}. `;return await this.plugin.log(`${D}[Release Info](https://github.com/${e}/releases/tag/${u.version})`,!0),c(this.plugin,D,30,()=>{u&&window.open(`https://github.com/${e}/releases/tag/${u.version}`)}),!1}await this.writeReleaseFilesToPluginFolder(u.id,f),await this.plugin.app.plugins.loadManifests(),await this.reloadPlugin(u.id);let y=`${u.id}
Plugin has been updated from version ${h.version} to ${u.version}. `;return await this.plugin.log(`${y}[Release Info](https://github.com/${e}/releases/tag/${u.version})`,!0),c(this.plugin,y,30,()=>{u&&window.open(`https://github.com/${e}/releases/tag/${u.version}`)}),!0}return n&&c(this.plugin,`No update available for ${e}`,3),!0}return!0}async reloadPlugin(e){let{plugins:t}=this.plugin.app;try{await t.disablePlugin(e),await t.enablePlugin(e)}catch(i){this.plugin.settings.debuggingMode&&console.log("reload plugin",i)}}async updatePlugin(e,t=!1,i=!1,n=!1,o=""){let a=await this.addPlugin(e,!0,t,i,"",n,!1,o);return!a&&!t&&c(this.plugin,`${e}
Update of plugin failed.`),a}async checkForPluginUpdatesAndInstallUpdates(e=!1,t=!1){var r,l;if(!await ie()){console.log("BRAT：未检测到互联网。");return}let i,n="Checking for plugin updates STARTED";await this.plugin.log(n,!0),e&&this.plugin.settings.notificationsEnabled&&(i=new A.Notice(`BRAT
${n}`,3e4));let o=new Map(this.plugin.settings.pluginSubListFrozenVersion.map(g=>[g.repo,{version:g.version,token:g.token}]));for(let g of this.plugin.settings.pluginList)o.has(g)&&((r=o.get(g))==null?void 0:r.version)!=="latest"||await this.updatePlugin(g,t,!1,!1,(l=o.get(g))==null?void 0:l.token);let a="Checking for plugin updates COMPLETED";await this.plugin.log(a,!0),e&&(i&&i.hide(),c(this.plugin,a,10))}deletePlugin(e){let t=`Removed ${e} from BRAT plugin list`;this.plugin.log(t,!0),this.plugin.settings.pluginList=this.plugin.settings.pluginList.filter(i=>i!==e),this.plugin.settings.pluginSubListFrozenVersion=this.plugin.settings.pluginSubListFrozenVersion.filter(i=>i.repo!==e),this.plugin.saveSettings()}getEnabledDisabledPlugins(e){let t=this.plugin.app.plugins,i=Object.values(t.manifests),n=Object.values(t.plugins).map(o=>o.manifest);return e?i.filter(o=>n.find(a=>o.id===a.id)):i.filter(o=>!n.find(a=>o.id===a.id))}};var L=require("obsidian");var z=async(s,e,t)=>{let i=await B(e,!0,s.settings.debuggingMode);if(i||(i=await B(e,!1,s.settings.debuggingMode)),!i)return c(s,"There is no theme.css or theme-beta.css file in the root path of this repository, so there is no theme to install."),!1;let n=await ke(e,s.settings.debuggingMode);if(!n)return c(s,"There is no manifest.json file in the root path of this repository, so theme cannot be installed."),!1;let o=await JSON.parse(n),a=(0,L.normalizePath)(ft(s)+o.name),{adapter:r}=s.app.vault;await r.exists(a)||await r.mkdir(a),await r.write((0,L.normalizePath)(`${a}/theme.css`),i),await r.write((0,L.normalizePath)(`${a}/manifest.json`),n),Re(s,e,V(i));let l="";return t?(De(s,e,i),l=`${o.name} theme installed from ${e}. `,setTimeout(()=>{s.app.customCss.setTheme(o.name)},500)):l=`${o.name} theme updated from ${e}.`,s.log(`${l}[Theme Info](https://github.com/${e})`,!1),c(s,l,20,()=>{window.open(`https://github.com/${e}`)}),!0},$=async(s,e)=>{if(!await ie()){console.log("BRAT：未检测到互联网。");return}let t,i="Checking for beta theme updates STARTED";await s.log(i,!0),e&&s.settings.notificationsEnabled&&(t=new L.Notice(`BRAT
${i}`,3e4));for(let o of s.settings.themesList){let a=await U(o.repo,!0,s.settings.debuggingMode);a==="0"&&(a=await U(o.repo,!1,s.settings.debuggingMode)),console.log("BRAT：最新在线更新",a),a!==o.lastUpdate&&await z(s,o.repo,!1)}let n="Checking for beta theme updates COMPLETED";(async()=>await s.log(n,!0))(),e&&(s.settings.notificationsEnabled&&t&&t.hide(),c(s,n))},ne=(s,e)=>{s.settings.themesList=s.settings.themesList.filter(i=>i.repo!==e),s.saveSettings();let t=`Removed ${e} from BRAT themes list and will no longer be updated. However, the theme files still exist in the vault. To remove them, go into Settings > Appearance and remove the theme.`;s.log(t,!0),c(s,t)},ft=s=>`${(0,L.normalizePath)(`${s.app.vault.configDir}/themes`)}/`;var se=require("obsidian");var F=class extends se.Modal{constructor(e,t=!1){super(e.app),this.plugin=e,this.address="",this.openSettingsTabAfterwards=t}async submitForm(){if(this.address==="")return;let e=this.address.replace("https://github.com/","");if(Be(this.plugin,e)){c(this.plugin,"This theme is already in the list for beta testing",10);return}await z(this.plugin,e,!0)&&this.close()}onOpen(){this.contentEl.createEl("h4",{text:"Github beta主题仓库："}),this.contentEl.createEl("form",{},e=>{e.addClass("brat-modal"),new se.Setting(e).addText(n=>{n.setPlaceholder("存储库（示例：https://github.com/GitubUserName/repository-name"),n.setValue(this.address),n.onChange(o=>{this.address=o.trim()}),n.inputEl.addEventListener("keydown",o=>{o.key==="Enter"&&this.address!==" "&&(o.preventDefault(),this.submitForm())}),n.inputEl.style.width="100%",window.setTimeout(()=>{let o=document.querySelector(".setting-item-info");o&&o.remove(),n.inputEl.focus()},10)}),e.createDiv("modal-button-container",n=>{n.createEl("button",{attr:{type:"button"},text:"没关系"}).addEventListener("click",()=>{this.close()}),n.createEl("button",{attr:{type:"submit"},cls:"mod-cta",text:"添加主题"})});let t=e.createDiv();t.style.borderTop="1px solid #ccc",t.style.marginTop="30px";let i=t.createSpan();i.innerHTML="BRAT by <a href='https://bit.ly/o42-twitter'>TFTHacker</a>",i.style.fontStyle="italic",t.appendChild(i),R(t,!1),window.setTimeout(()=>{let n=e.querySelectorAll(".brat-modal .setting-item-info");for(let o of Array.from(n))o.remove()},50),e.addEventListener("submit",n=>{n.preventDefault(),this.address!==""&&this.submitForm()})})}onClose(){this.openSettingsTabAfterwards&&(this.plugin.app.setting.openTab(),this.plugin.app.setting.openTabById(this.plugin.APP_ID))}};var Me=require("obsidian"),C=class extends Me.FuzzySuggestModal{constructor(t){super(t.app);this.data=[];this.scope.register(["Shift"],"Enter",i=>{this.enterTrigger(i)}),this.scope.register(["Ctrl"],"Enter",i=>{this.enterTrigger(i)})}setSuggesterData(t){this.data=t}display(t){this.callbackFunction=t,this.open()}getItems(){return this.data}getItemText(t){return t.display}onChooseItem(){}renderSuggestion(t,i){i.createEl("div",{text:t.item.display})}enterTrigger(t){var o;let i=(o=document.querySelector(".suggestion-item.is-selected div"))==null?void 0:o.textContent,n=this.data.find(a=>a.display===i);n&&(this.invokeCallback(n,t),this.close())}onChooseSuggestion(t,i){this.invokeCallback(t.item,i)}invokeCallback(t,i){typeof this.callbackFunction=="function"&&this.callbackFunction(t,i)}};var Y=class{constructor(e){this.bratCommands=[{id:"AddBetaPlugin",icon:"BratIcon",name:"Plugins: Add a beta plugin for testing (with or without version)",showInRibbon:!0,callback:()=>{this.plugin.betaPlugins.displayAddNewPluginModal(!1,!0)}},{id:"checkForUpdatesAndUpdate",icon:"BratIcon",name:"插件：检查所有测试版插件的更新并更新",showInRibbon:!0,callback:async()=>{await this.plugin.betaPlugins.checkForPluginUpdatesAndInstallUpdates(!0,!1)}},{id:"checkForUpdatesAndDontUpdate",icon:"BratIcon",name:"插件：只检查测试版插件的更新，但不更新",showInRibbon:!0,callback:async()=>{await this.plugin.betaPlugins.checkForPluginUpdatesAndInstallUpdates(!0,!0)}},{id:"updateOnePlugin",icon:"BratIcon",name:"插件：选择一个插件版本进行更新",showInRibbon:!0,callback:()=>{let e=new Map(this.plugin.settings.pluginSubListFrozenVersion.map(n=>[n.repo,{version:n.version,token:n.token}])),t=Object.values(this.plugin.settings.pluginList).filter(n=>{let o=e.get(n);return!(o!=null&&o.version)||o.version==="latest"}).map(n=>{let o=e.get(n);return{display:n,info:n}}),i=new C(this.plugin);i.setSuggesterData(t),i.display(n=>{let o=`Checking for updates for ${n.info}`,a=e.get(n.info);this.plugin.log(o,!0),c(this.plugin,`
${o}`,3),this.plugin.betaPlugins.updatePlugin(n.info,!1,!0,!1,a==null?void 0:a.token)})}},{id:"reinstallOnePlugin",icon:"BratIcon",name:"插件：选择一个插件重新安装",showInRibbon:!0,callback:()=>{let e=new Set(this.plugin.settings.pluginSubListFrozenVersion.map(n=>n.repo)),t=Object.values(this.plugin.settings.pluginList).filter(n=>!e.has(n)).map(n=>({display:n,info:n})),i=new C(this.plugin);i.setSuggesterData(t),i.display(n=>{let o=`Reinstalling ${n.info}`;c(this.plugin,`
${o}`,3),this.plugin.log(o,!0),this.plugin.betaPlugins.updatePlugin(n.info,!1,!1,!0)})}},{id:"restartPlugin",icon:"BratIcon",name:"插件：重新启动已安装的插件",showInRibbon:!0,callback:()=>{let e=Object.values(this.plugin.app.plugins.manifests).map(i=>({display:i.id,info:i.id})),t=new C(this.plugin);t.setSuggesterData(e),t.display(i=>{c(this.plugin,`${i.info}
Plugin reloading .....`,5),this.plugin.betaPlugins.reloadPlugin(i.info)})}},{id:"disablePlugin",icon:"BratIcon",name:"插件：禁用插件-将其关闭",showInRibbon:!0,callback:()=>{let e=this.plugin.betaPlugins.getEnabledDisabledPlugins(!0).map(i=>({display:`${i.name} (${i.id})`,info:i.id})),t=new C(this.plugin);t.setSuggesterData(e),t.display(i=>{this.plugin.log(`${i.display} plugin disabled`,!1),this.plugin.settings.debuggingMode&&console.log(i.info),this.plugin.app.plugins.disablePluginAndSave(i.info)})}},{id:"enablePlugin",icon:"BratIcon",name:"插件：启用插件-打开它",showInRibbon:!0,callback:()=>{let e=this.plugin.betaPlugins.getEnabledDisabledPlugins(!1).map(i=>({display:`${i.name} (${i.id})`,info:i.id})),t=new C(this.plugin);t.setSuggesterData(e),t.display(i=>{this.plugin.log(`${i.display} plugin enabled`,!1),this.plugin.app.plugins.enablePluginAndSave(i.info)})}},{id:"openGitHubZRepository",icon:"BratIcon",name:"插件：打开插件的GitHub存储库",showInRibbon:!0,callback:async()=>{let e=await Ae(this.plugin.settings.debuggingMode);if(e){let t=Object.values(e).map(o=>({display:`Plugin: ${o.name}  (${o.repo})`,info:o.repo})),i=Object.values(this.plugin.settings.pluginList).map(o=>({display:`BRAT: ${o}`,info:o}));for(let o of t)i.push(o);let n=new C(this.plugin);n.setSuggesterData(i),n.display(o=>{o.info&&window.open(`https://github.com/${o.info}`)})}}},{id:"openGitHubRepoTheme",icon:"BratIcon",name:"主题：打开主题的GitHub存储库（外观）",showInRibbon:!0,callback:async()=>{let e=await Fe(this.plugin.settings.debuggingMode);if(e){let t=Object.values(e).map(n=>({display:`Theme: ${n.name}  (${n.repo})`,info:n.repo})),i=new C(this.plugin);i.setSuggesterData(t),i.display(n=>{n.info&&window.open(`https://github.com/${n.info}`)})}}},{id:"opentPluginSettings",icon:"BratIcon",name:"插件：打开插件设置选项卡",showInRibbon:!0,callback:()=>{let e=this.plugin.app.setting,t=Object.values(e.pluginTabs).map(o=>({display:`Plugin: ${o.name}`,info:o.id})),i=new C(this.plugin),n=Object.values(e.settingTabs).map(o=>({display:`Core: ${o.name}`,info:o.id}));for(let o of t)n.push(o);i.setSuggesterData(n),i.display(o=>{e.open(),e.openTabById(o.info)})}},{id:"GrabBetaTheme",icon:"BratIcon",name:"主题：从Github仓库中获取测试主题进行测试",showInRibbon:!0,callback:()=>{new F(this.plugin).open()}},{id:"updateBetaThemes",icon:"BratIcon",name:"主题：更新测试版主题",showInRibbon:!0,callback:async()=>{await $(this.plugin,!0)}},{id:"allCommands",icon:"BratIcon",name:"所有命令列表",showInRibbon:!1,callback:()=>{this.ribbonDisplayCommands()}}];this.plugin=e;for(let t of this.bratCommands)this.plugin.addCommand({id:t.id,name:t.name,icon:t.icon,callback:()=>{t.callback()}})}ribbonDisplayCommands(){let e=[];for(let a of this.bratCommands)a.showInRibbon&&e.push({display:a.name,info:a.callback});let t=new C(this.plugin),i=this.plugin.app.setting,n=Object.values(i.settingTabs).map(a=>({display:`Core: ${a.name}`,info:()=>{i.open(),i.openTabById(a.id)}})),o=Object.values(i.pluginTabs).map(a=>({display:`Plugin: ${a.name}`,info:()=>{i.open(),i.openTabById(a.id)}}));e.push({display:"---- Core Plugin Settings ----",info:()=>{this.ribbonDisplayCommands()}});for(let a of n)e.push(a);e.push({display:"---- Plugin Settings ----",info:()=>{this.ribbonDisplayCommands()}});for(let a of o)e.push(a);t.setSuggesterData(e),t.display(a=>{typeof a.info=="function"&&a.info()})}};var v=require("obsidian");var oe=class extends v.PluginSettingTab{constructor(e,t){super(e,t),this.plugin=t}display(){let{containerEl:e}=this;e.empty(),new v.Setting(e).setName("安装后自动启用插件").setDesc('如果启用了测试版插件，默认情况下安装后会自动启用。注意：您可以在“添加插件”窗体中为每个插件打开和关闭此选项。').addToggle(i=>{i.setValue(this.plugin.settings.enableAfterInstall),i.onChange(async n=>{this.plugin.settings.enableAfterInstall=n,await this.plugin.saveSettings()})}),new v.Setting(e).setName("启动时自动更新插件").setDesc("如果启用，每次Obsidian启动时都会检查所有测试版插件的更新情况。注意：这不会更新冻结版本的插件。").addToggle(i=>{i.setValue(this.plugin.settings.updateAtStartup),i.onChange(async n=>{this.plugin.settings.updateAtStartup=n,await this.plugin.saveSettings()})}),new v.Setting(e).setName("启动时自动更新主题").setDesc("如果启用，每次黑曜石启动时都会检查所有测试版主题是否有更新。").addToggle(i=>{i.setValue(this.plugin.settings.updateThemesAtStartup),i.onChange(async n=>{this.plugin.settings.updateThemesAtStartup=n,await this.plugin.saveSettings()})}),R(e,!0),e.createEl("hr"),new v.Setting(e).setName("Beta插件列表").setHeading(),e.createEl("div",{text:'The following is a list of beta plugins added via the command "Add a beta plugin for testing". You can chose to add the latest version or a frozen version. A frozen version is a specific release of a plugin based on its release tag.'}),e.createEl("p"),e.createEl("div",{text:"Click the 'Edit' button next to a plugin to change the installed version and the x button next to a plugin to remove it from the list."}),e.createEl("p"),e.createEl("span").createEl("b",{text:"注："}),e.createSpan({text:"Removing from the list does not delete the plugin, this should be done from the Community Plugins tab in Settings."}),new v.Setting(e).addButton(i=>{i.setButtonText("添加Beta插件"),i.onClick(()=>{this.plugin.betaPlugins.displayAddNewPluginModal(!0,!0)})});let t=new Map(this.plugin.settings.pluginSubListFrozenVersion.map(i=>[i.repo,{version:i.version,token:i.token}]));for(let i of this.plugin.settings.pluginList){let n=t.get(i),o=new v.Setting(e).setName(H(i)).setDesc(n!=null&&n.version?` Tracked version: ${n.version} ${n.version==="latest"?"":"(frozen)"}`:"");(!(n!=null&&n.version)||n.version==="latest")&&o.addButton(a=>{a.setIcon("sync").setTooltip("Check and update plugin").onClick(async()=>{let r=await this.plugin.betaPlugins.updatePlugin(i,!1,!0,!1,n==null?void 0:n.token)})}),o.addButton(a=>{a.setIcon("edit"),a.setTooltip("Change version"),a.onClick(()=>{this.plugin.betaPlugins.displayAddNewPluginModal(!0,!0,i,n==null?void 0:n.version,n==null?void 0:n.token),this.plugin.app.setting.updatePluginSection()})}).addButton(a=>{a.setIcon("cross"),a.setTooltip("删除此测试版插件"),a.onClick(()=>{if(a.buttonEl.textContent==="")a.setButtonText("再次单击以确认删除");else{let{buttonEl:r}=a,{parentElement:l}=r;l!=null&&l.parentElement&&(l.parentElement.remove(),this.plugin.betaPlugins.deletePlugin(i))}})})}new v.Setting(e).setName("Beta主题列表").setHeading(),new v.Setting(e).addButton(i=>{i.setButtonText("添加Beta主题"),i.onClick(()=>{this.plugin.app.setting.close(),new F(this.plugin).open()})});for(let i of this.plugin.settings.themesList)new v.Setting(e).setName(H(i.repo)).addButton(n=>{n.setIcon("cross"),n.setTooltip("删除此测试版主题"),n.onClick(()=>{if(n.buttonEl.textContent==="")n.setButtonText("再次单击以确认删除");else{let{buttonEl:o}=n,{parentElement:a}=o;a!=null&&a.parentElement&&(a.parentElement.remove(),ne(this.plugin,i.repo))}})});new v.Setting(e).setName("监视").setHeading(),new v.Setting(e).setName("启用通知").setDesc("BRAT将为其各种活动提供弹出通知。关闭此选项意味着BRAT不会发出通知。").addToggle(i=>{i.setValue(this.plugin.settings.notificationsEnabled),i.onChange(async n=>{this.plugin.settings.notificationsEnabled=n,await this.plugin.saveSettings()})}),new v.Setting(e).setName("启用日志记录").setDesc("插件更新将记录到日志文件中的一个文件中。").addToggle(i=>{i.setValue(this.plugin.settings.loggingEnabled),i.onChange(async n=>{this.plugin.settings.loggingEnabled=n,await this.plugin.saveSettings()})}),new v.Setting(this.containerEl).setName("BRAT日志文件位置").setDesc("日志将保存到此文件中。不要在文件名中添加.md。").addSearch(i=>{i.setPlaceholder("示例：BRAT日志").setValue(this.plugin.settings.loggingPath).onChange(async n=>{this.plugin.settings.loggingPath=n,await this.plugin.saveSettings()})}),new v.Setting(e).setName("启用详细日志记录").setDesc("在日志中获取更多信息。").addToggle(i=>{i.setValue(this.plugin.settings.loggingVerboseEnabled),i.onChange(async n=>{this.plugin.settings.loggingVerboseEnabled=n,await this.plugin.saveSettings()})}),new v.Setting(e).setName("调试模式").setDesc("原子弹级控制台日志记录。可用于故障排除和开发。").addToggle(i=>{i.setValue(this.plugin.settings.debuggingMode),i.onChange(async n=>{this.plugin.settings.debuggingMode=n,await this.plugin.saveSettings()})}),new v.Setting(e).setName("个人访问令牌").setDesc("如果您需要访问私有存储库，请在此处输入个人访问令牌。").addText(i=>{var n;i.setPlaceholder("输入您的个人访问令牌").setValue((n=this.plugin.settings.personalAccessToken)!=null?n:"").onChange(async o=>{this.plugin.settings.personalAccessToken=o,await this.plugin.saveSettings()})})}};var Ee=require("obsidian");function Ie(){(0,Ee.addIcon)("BratIcon",'<path fill="currentColor" stroke="currentColor"  d="M 41.667969 41.667969 C 41.667969 39.367188 39.800781 37.5 37.5 37.5 C 35.199219 37.5 33.332031 39.367188 33.332031 41.667969 C 33.332031 43.96875 35.199219 45.832031 37.5 45.832031 C 39.800781 45.832031 41.667969 43.96875 41.667969 41.667969 Z M 60.417969 58.582031 C 59.460938 58.023438 58.320312 57.867188 57.25 58.148438 C 56.179688 58.429688 55.265625 59.125 54.707031 60.082031 C 53.746094 61.777344 51.949219 62.820312 50 62.820312 C 48.050781 62.820312 46.253906 61.777344 45.292969 60.082031 C 44.734375 59.125 43.820312 58.429688 42.75 58.148438 C 41.679688 57.867188 40.539062 58.023438 39.582031 58.582031 C 37.597656 59.726562 36.910156 62.257812 38.042969 64.25 C 40.5 68.53125 45.0625 71.171875 50 71.171875 C 54.9375 71.171875 59.5 68.53125 61.957031 64.25 C 63.089844 62.257812 62.402344 59.726562 60.417969 58.582031 Z M 62.5 37.5 C 60.199219 37.5 58.332031 39.367188 58.332031 41.667969 C 58.332031 43.96875 60.199219 45.832031 62.5 45.832031 C 64.800781 45.832031 66.667969 43.96875 66.667969 41.667969 C 66.667969 39.367188 64.800781 37.5 62.5 37.5 Z M 50 8.332031 C 26.988281 8.332031 8.332031 26.988281 8.332031 50 C 8.332031 73.011719 26.988281 91.667969 50 91.667969 C 73.011719 91.667969 91.667969 73.011719 91.667969 50 C 91.667969 26.988281 73.011719 8.332031 50 8.332031 Z M 50 83.332031 C 33.988281 83.402344 20.191406 72.078125 17.136719 56.363281 C 14.078125 40.644531 22.628906 24.976562 37.5 19.042969 C 37.457031 19.636719 37.457031 20.238281 37.5 20.832031 C 37.5 27.738281 43.097656 33.332031 50 33.332031 C 52.300781 33.332031 54.167969 31.46875 54.167969 29.167969 C 54.167969 26.867188 52.300781 25 50 25 C 47.699219 25 45.832031 23.132812 45.832031 20.832031 C 45.832031 18.53125 47.699219 16.667969 50 16.667969 C 68.410156 16.667969 83.332031 31.589844 83.332031 50 C 83.332031 68.410156 68.410156 83.332031 50 83.332031 Z M 50 83.332031 " />')}var j=class{constructor(e){this.console=(e,...t)=>{console.log(`BRAT: ${e}`,...t)};this.themes={themeseCheckAndUpates:async e=>{await $(this.plugin,e)},themeInstallTheme:async e=>{let t=e.replace("https://github.com/","");await z(this.plugin,t,!0)},themesDelete:e=>{let t=e.replace("https://github.com/","");ne(this.plugin,t)},grabCommmunityThemeCssFile:async(e,t=!1)=>await B(e,t,this.plugin.settings.debuggingMode),grabChecksumOfThemeCssFile:async(e,t=!1)=>await U(e,t,this.plugin.settings.debuggingMode),grabLastCommitDateForFile:async(e,t)=>await Ne(e,t)};this.plugin=e}};var X=require("obsidian"),Ke=lt(Ze());async function Xe(s,e,t=!1){if(s.settings.debuggingMode&&console.log(`BRAT: ${e}`),s.settings.loggingEnabled){if(!s.settings.loggingVerboseEnabled&&t)return;let i=`${s.settings.loggingPath}.md`,n=`[[${(0,X.moment)().format((0,Ke.getDailyNoteSettings)().format).toString()}]] ${(0,X.moment)().format("HH:mm")}`,o=window.require("os"),a=X.Platform.isDesktop?o.hostname():"MOBILE",r=`${n} ${a} ${e.replace(`
`," ")}
`,l=s.app.vault.getAbstractFileByPath(i);l?await s.app.vault.append(l,r):l=await s.app.vault.create(i,r)}}var ae=class extends et.Plugin{constructor(){super(...arguments);this.APP_NAME="BRAT";this.APP_ID="obsidian42-brat";this.settings=me;this.betaPlugins=new _(this);this.commands=new Y(this);this.bratApi=new j(this);this.obsidianProtocolHandler=t=>{if(!t.plugin&&!t.theme){c(this,"Could not locate the repository from the URL.",10);return}for(let i of["plugin","theme"])if(t[i]){let n=i==="plugin"?new x(this,this.betaPlugins):new F(this);n.address=t[i],n.open();return}}}onload(){console.log(`正在加载${this.APP_NAME}`),Ie(),this.addRibbonIcon("BratIcon","BRAT",()=>{this.commands.ribbonDisplayCommands()}),this.loadSettings().then(()=>{this.app.workspace.onLayoutReady(()=>{this.addSettingTab(new oe(this.app,this)),this.registerObsidianProtocolHandler("brat",this.obsidianProtocolHandler),this.settings.updateAtStartup&&setTimeout(()=>{this.betaPlugins.checkForPluginUpdatesAndInstallUpdates(!1)},6e4),this.settings.updateThemesAtStartup&&setTimeout(()=>{$(this,!1)},12e4),setTimeout(()=>{window.bratAPI=this.bratApi},500)})}).catch(t=>{console.error("Failed to load settings:",t)})}async log(t,i=!1){await Xe(this,t,i)}onunload(){console.log(`卸载${this.APP_NAME}`)}async loadSettings(){this.settings=Object.assign({},me,await this.loadData())}async saveSettings(){await this.saveData(this.settings)}};

/* nosourcemap */