import requests								#导入requests模块
url = 'http://www.pythontab.com/'		#定义url字符串
#定义代理IP列表
proxies = [
    {'http': 'http://*************:8080'},
    {'http': 'http://***********:8800'},
    {'http': 'http://**************:8443'}
]
for proxy in proxies:						#遍历列表
    #异常判断
    try:
        #设置代理IP，发送HTTP请求
        r = requests.get(url, proxies=proxy)
    except:
        #请求错误，输出验证结果“此代理IP不可用”
        print('此代理IP不可用')
    else:
        #请求成功，输出验证结果“此代理IP可用”和代理IP
        print('此代理IP可用', proxy)
        print('响应状态码', r.status_code)	#输出响应状态码





