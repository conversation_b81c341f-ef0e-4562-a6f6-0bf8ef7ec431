{"manifest": {"translationVersion": 1729952596261, "pluginVersion": "0.22.1"}, "description": {"original": "Improved table navigation, formatting, manipulation, and formulas", "translation": "改进的表格导航、格式化、操作和公式"}, "dict": {"Notice(\"Advanced Tables: Cursor must be in a table.\")": "Notice(\"高级表格：光标必须位于表格中。\")", "Notice(\"Advanced Tables: Next row also bound to enter. Possibly producing double actions. See Advanced Tables settings.\")": "Notice(\"高级表格：下一行也绑定到回车键，可能会产生双重操作。请查看高级表格设置。\")", "Notice(\"Advanced Tables: Next cell also bound to tab. Possibly producing double actions. See Advanced Tables settings.\")": "Notice(\"高级表格：下一个单元格也绑定到 Tab 键，可能会产生双重操作。请查看高级表格设置。\")", "Notice(\"Advanced Tables: Previous cell also bound to shift+tab. Possibly producing double actions. See Advanced Tables settings.\")": "Notice(\"高级表格：上一个单元格也绑定到 Shift+Tab 键，可能会产生双重操作。请查看高级表格设置。\")", ".log(l+\" NOT RESOLVED FROM \"+r)": ".log(l+\" 未从中解析 \"+r)", ".log(\"reberia restar \"+$+\" a \"+D)": ".log(\"重新启动 \"+$+\" a \"+D)", ".log(\"loading markdown-table-editor plugin\")": ".log(\"正在加载 markdown-table-editor 插件\")", "name:\"Go to next row\"": "name:\"转到下一行\"", "name:\"Go to next cell\"": "name:\"转到下一个单元格\"", "name:\"Go to previous cell\"": "name:\"转到上一个单元格\"", "name:\"Format table at the cursor\"": "name:\"格式化光标处的表格\"", "name:\"Format all tables in this file\"": "name:\"格式化此文件中的所有表格\"", "name:\"Insert column before current\"": "name:\"在当前列之前插入列\"", "name:\"Insert row before current\"": "name:\"在当前行之前插入行\"", "name:\"Move cursor out of table\"": "name:\"将光标移出表格\"", "name:\"Left align column\"": "name:\"左对齐列\"", "name:\"Center align column\"": "name:\"居中对齐列\"", "name:\"Right align column\"": "name:\"右对齐列\"", "name:\"Move column left\"": "name:\"向左移动列\"", "name:\"Move column right\"": "name:\"向右移动列\"", "name:\"Move row up\"": "name:\"向上移动行\"", "name:\"Move row down\"": "name:\"向下移动行\"", "name:\"Delete column\"": "name:\"删除列\"", "name:\"Delete row\"": "name:\"删除行\"", "name:\"Sort rows ascending\"": "name:\"按升序排序行\"", "name:\"Sort rows descending\"": "name:\"按降序排序行\"", "name:\"Transpose\"": "name:\"转置\"", "name:\"Evaluate table formulas\"": "name:\"计算表格公式\"", "name:\"Open table controls toolbar\"": "name:\"打开表格控制工具栏\"", ".setText(\"Align:\")": ".setText(\"Align:\")", ".setText(\"Move:\")": ".setText(\"Move:\")", ".setText(\"Edit:\")": ".setText(\"Edit:\")", ".setText(\"Sort/F:\")": ".setText(\"Sort/F:\")", ".setText(\"Misc:\")": ".setText(\"Misc:\")", ".setName(\"Bind enter to table navigation\")": ".setName(\"将回车键绑定到表格导航\")", ".setName(\"Bind tab to table navigation\")": ".setName(\"将 Tab 键绑定到表格导航\")", ".setName(\"Pad cell width using spaces\")": ".setName(\"使用空格填充单元格宽度\")", ".setName(\"Show icon in sidebar\")": ".setName(\"在侧边栏中显示图标\")", ".setDesc('Requires restart of Obsidian. If enabled, when the cursor is in a table, enter advances to the next row. Disabling this can help avoid conflicting with tag or CJK autocompletion. If disabling, bind \"Go to ...\" in the Obsidian Hotkeys settings.')": ".setDesc('需要重启 Obsidian。如果启用，当光标位于表格中时，回车键将跳转到下一行。禁用此功能可以帮助避免与标签或 CJK 自动补全冲突。如果禁用，请在 Obsidian 热键设置中绑定“转到...”功能。')", ".setDesc('Requires restart of Obsidian. If enabled, when the cursor is in a table, tab/shift+tab navigate between cells. Disabling this can help avoid conflicting with tag or CJK autocompletion. If disabling, bind \"Go to ...\" in the Obsidian Hotkeys settings.')": ".setDesc('需要重启 Obsidian。如果启用，当光标位于表格中时，Tab/Shift+Tab 将在单元格之间导航。禁用此功能可以帮助避免与标签或 CJK 自动补全冲突。如果禁用，请在 Obsidian 热键设置中绑定“转到...”功能。')", ".setDesc(\"If enabled, table cells will have spaces added to match the width of the longest cell in the column.\")": ".setDesc(\"如果启用，表格单元格将添加空格以匹配该列中最长单元格的宽度。\")", ".setDesc(\"If enabled, a button which opens the table controls toolbar will be added to the Obsidian sidebar. The toolbar can also be opened with a Hotkey. Changes only take effect on reload.\")": ".setDesc(\"如果启用，将在 Obsidian 侧边栏添加一个按钮，以打开表格控制工具栏。该工具栏也可以通过热键打开。更改仅在重新加载后生效。\")", ".appendText(\"If this plugin adds value for you and you would like to help support continued development, please use the buttons below:\")": ".appendText(\"如果此插件对您有帮助，并且您希望支持其持续开发，请使用以下按钮：\")"}}