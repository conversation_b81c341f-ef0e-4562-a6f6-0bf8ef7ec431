{"manifest": {"translationVersion": 1742967549133, "pluginVersion": "0.6.4"}, "description": {"original": "Rename, merge, toggle, and search tags from the tag pane", "translation": "Rename, merge, toggle, and search tags from the tag pane"}, "dict": {"Notice(u+\"; skipping frontmatter\")": "Notice(u+\"；跳过前置属性（frontmatter）\")", "Notice(\"Unchanged or empty tag: No changes made.\")": "Notice(\"未更改或空标签：未进行任何更改。\")", "Notice(`Operation ${u.aborted?\"cancelled\":\"complete\"}: ${f} file(s)": "Notice(`Operation ${u.aborted?\"已取消“：”完成\"}: ${f} file(s)", "Notice(`\"${t}\" is not a valid Obsidian tag name`)": "Notice(`“${t}”不是有效的Obsidian 标签名`)", "Notice(\"error: \"+i)": "Notice(\"错误：\"+i)", "name:\"all\"": "name:\"所有\"", ".setTitle(`Renaming #${s} (and any sub-tags)`)": ".setTitle(`Renaming #${s} (and any sub-tags)`)", ".setTitle(\"WARNING: No Undo!\")": ".setTitle(\"警告：不能撤消！\")", ".setTitle(\"Create Tag Page\")": ".setTitle(\"创建标签页\")", "create-new": "新建", "Create tag page": "创建标签页", "Rename #": "重命名 #", "New search for #": "搜索 #", "Exclude #": "排除 #", " from search": " 从搜索", "Collapse tags at this level": "折叠此级别的标签", "Expand tags at this level": "展开此级别的标签", "Require #": "追加 #", " in search": " 到搜索", "Open tag page": "打开标签页", "This <b>cannot</b> be undone.  Do you wish to proceed?`": "这 <b>不能</b> 撤销。  你想继续吗？`", "WARNING: No Undo!,`Renaming <code>${t}</code> to <code>${n}</code> will merge ${s.canonical===t.canonical?\"these tags\":`multiple tags": "警告: 无法撤消！,`重命名 <code>${t}</code> 到 <code>${n}</code> 将合并 ${s.canonical===t.canonical?\"这些标签\":`multiple tags", "ok:\"OK\"": "ok:\"确定\"", "cancel:\"Cancel\"": "cancel:\"取消\"", "`Renaming #${s} (and any sub-tags)`,`Enter new name (must be a valid Obsidian tag):": "`重命名 #${s} (以及任何子标签)`,`输入新名称 (必须是有效的Obsidian标签):", ".setTitle(`Renaming #${n} (and any sub-tags)`)": ".setTitle(`重命名 #${n} (包含子标签)`)", ".setContent(`Enter new name (must be a valid Obsidian tag name)": ".setContent(`输入新名称 (必须是有效的 Obsidian 标签名称)", ".Notice(`Operation ${f.aborted?\"cancelled\":\"complete\"}: ${p} file(s) updated`)": ".Notice(`活动 ${f.aborted?\"终止\":\"\"}: ${p} 个文件已更新`)", ".onInvalidEntry(t=>new ye.Notice(`\"${t}\" is not a valid Obsidian tag name`))": ".onInvalidEntry(t=>new ye.Notice(`\"${t}\" 不是有效的 Obsidian 标签名称`))"}}