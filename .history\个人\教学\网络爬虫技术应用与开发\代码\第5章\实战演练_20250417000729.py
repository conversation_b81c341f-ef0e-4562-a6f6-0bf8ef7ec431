import time								#导入time模块
import random							#导入random模块
import requests							#导入requests模块
from urllib.parse import urlencode	#导入urlencode模块
from bs4 import BeautifulSoup			#导入BeautifulSoup模块
import csv								#导入csv模块
#定义base_url字符串
base_url = 'https://search.bilibili.com/all?'
headersvalue = {
    "Referer": "https://www.bilibili.com/",     # 防盗链，用于告诉服务器我是从哪个链接来的。
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.97 Safari/537.36'
}											#设置请求头User-Agent信息
proxiesvalue = [
    {'http': 'http://*************:8080'},
    {'http': 'http://***********:8800'},
    {'http': 'http://**************:8443'}
]											#定义代理IP列表
#定义解析内容函数
def parse_content(content):
    items = []							#定义items列表
    #创建BeautifulSoup对象，并设置使用lxml解析器
    soup = BeautifulSoup(content, 'lxml')
    #查找包含视频信息的div节点 (Updated selector)
    video_list = soup.select('div.bili-video-card')
    #遍历video_list,依次获取每一个视频信息
    for video in video_list:
        try:
            # 获取视频标题 (Updated to use title attribute)
            title_tag = video.select_one('h3.bili-video-card__info--tit')
            title = title_tag['title'].strip() if title_tag and title_tag.has_attr('title') else 'N/A'
            # 获取视频链接 (Updated selector)
            link_tag = video.select_one('div.bili-video-card__wrap > a')
            video_link = 'https:' + link_tag['href'] if link_tag and link_tag.has_attr('href') else 'N/A'
            # 获取视频时长
            duration_tag = video.select_one('span.bili-video-card__stats__duration')
            video_time = duration_tag.text.strip() if duration_tag else 'N/A'
            # 获取观看次数
            view_count_tag = video.select_one('.bili-video-card__stats--left .bili-video-card__stats--item:nth-of-type(1) span')
            view_count = view_count_tag.text.strip() if view_count_tag else 'N/A'
            # 获取上传时间
            up_time_tag = video.select_one('span.bili-video-card__info--date')
            # Handle potential missing date or different formats
            up_time_text = up_time_tag.text.strip() if up_time_tag else 'N/A'
            up_time = up_time_text.replace('· ', '').strip() if up_time_text != 'N/A' else 'N/A'
            # 获取up主
            up_master_tag = video.select_one('span.bili-video-card__info--author')
            up_master = up_master_tag.text.strip() if up_master_tag else 'N/A'

            item = {
                '视频标题': title,
                '视频时长': video_time,
                '观看次数': view_count,
                '上传时间': up_time,
                'up主': up_master,
                '视频链接': video_link
            }                                       #添加到字典中
            items.append(item)                      #添加到items列表中
        except Exception as e:
            print(f"Error parsing video item: {e}") # 输出错误信息，方便调试
            # 可以选择跳过这个项目或者填充默认值
            continue
    return items								#返回items
#定义获取每一页信息的函数
def get_one_page(kw,page):
    items = []                              # 初始化items为空列表
    params = {
        'keyword': kw,
        'page': str(page)
    }											#定义params字典
    url = base_url + urlencode(params)	#合并URL
    #异常判断
    try:
        #设置请求头和代理IP，发送HTTP请求
        r = requests.get(url, headers=headersvalue, proxies=random.choice(proxiesvalue), timeout=10) # Added timeout
    except requests.exceptions.RequestException as e: # More specific exception handling
        print(f'请求失败: {e}')						#请求错误，输出“请求失败”
        items = [] # Ensure items is empty on failure
    else:
        if r.status_code == 200:			#判断状态码
            print('请求成功')				#请求成功，输出“请求成功”
            #解析内容
            #r.encoding = r.apparent_encoding	#设置编码格式 (Optional)
            # print(r.text)					#输出响应内容 (Commented out for cleaner output)
            items = parse_content(r.text)	#调用parse_content
        else:
            print(f'请求失败，状态码: {r.status_code}') # Print status code on failure
            items = [] # Ensure items is empty on non-200 status
    #设置随机休眠时间
    sleep_time = random.randint(2, 5) + random.random()
    print(f"Sleeping for {sleep_time:.2f} seconds...") # Added sleep message
    time.sleep(sleep_time)					#程序休眠sleep_time
    return items								#返回items
if __name__=='__main__':
    keyword = input('请输入搜索关键字：')	#输入搜索关键字
    #打开data.csv文件写入数据
    # Ensure filename is valid
    safe_keyword = "".join(c for c in keyword if c.isalnum() or c in (' ', '_')).rstrip()
    filename = (safe_keyword if safe_keyword else 'search_results') + '.csv'
    print(f"Saving results to {filename}")

    try:
        with open(filename, 'w', newline='', encoding='utf-8-sig') as file: # Use utf-8-sig for better Excel compatibility
            names = ['视频标题', '视频时长', '观看次数', '上传时间', 'up主', '视频链接']									#定义表头
            #初始化writer对象
            writer = csv.DictWriter(file, fieldnames=names)
            writer.writeheader()					#写入表头
            total_items_written = 0
            for i in range(1, 51): # Limit to 50 pages as per original code
                #输出正在爬取的页码提示
                print(f'--- 正在爬取第 {i} 页的视频信息 ---')
                items = get_one_page(keyword, i)	#调用get_one_page
                if items:
                    try:
                        writer.writerows(items)			#写入items
                        total_items_written += len(items)
                        print(f"写入 {len(items)} 条数据")
                    except Exception as e:
                        print(f"Error writing rows for page {i}: {e}")
                else:
                    print(f"第 {i} 页未获取到数据或请求失败，可能已到达末页或被阻止。")
                    # Optional: break here if no items are found on a page
                    # break
            print(f"--- 爬取完成，共写入 {total_items_written} 条数据到 {filename} ---")
    except IOError as e:
        print(f"无法写入文件 {filename}: {e}")
    except Exception as e:
        print(f"发生未预料的错误: {e}")

