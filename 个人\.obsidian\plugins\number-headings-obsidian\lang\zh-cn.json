{"manifest": {"translationVersion": 1741183571643, "pluginVersion": "1.16.0"}, "description": {"original": "Automatically number or re-number headings in an Obsidian document", "translation": "给笔记中的标题自动编号，以及动态目录；使用 Number headings 的命令或者开启自动编号后，我们就可以编辑文档了。无论你怎么编辑文档，它都会自动更新章节标题。"}, "dict": {".log(\"Unexpected heading format: '\" + lineText + \"'\")": ".log(\"Unexpected heading format: '\" + lineText + \"'\")", ".log(\"Unexpected heading format: '\" + headingLineString + \"'\")": ".log(\"Unexpected heading format: '\" + headingLineString + \"'\")", ".log('Number Headings Plugin: Applying headings numbering changes:', changes.length)": ".log('Number Headings Plugin: 应用标题编号更改:', changes.length)", ".log('Number Headings Plugin: Applying table of contents changes:', changes.length)": ".log('Number Headings Plugin: 应用目录更改:', changes.length)", ".log('Number Headings Plugin: Automatically numbered document')": ".log('Number Headings Plugin: 文档自动编号')", "name: 'Number all headings in document (and show options)'": "name: '对文档中的所有标题进行编号（并显示选项）'", "name: 'Number all headings in document'": "name: '对文档中的所有标题进行编号'", "name: 'Remove numbering from all headings in document'": "name: '删除文档中所有标题的编号'", "name: 'Save settings to front matter'": "name: '将设置保存到 front matter 中'", "text: \"Do you want to save these settings in the document's front matter?\"": "text: \"您想将这些设置保存在文档的 front matter 中吗?\"", "text: 'Number Headings - Settings'": "text: '标题编号-设置'", "text: 'To add numbering to your document, bring up the command window (on Mac, type CMD+P), and then type \"Number Headings\" to see a list of available commands.'": "text: '要为你的文档添加编号，打开命令窗口（在 Mac 上，输入 CMD+P），然后输入‘Number Headings（编号标题）’以查看可用命令列表。'", "text: 'If the document has front matter defined with the below settings, the project-wide settings defined on this screen will be ignored. You can define front matter like this:'": "text: '如果文档具有使用以下设置定义的 front matter, 此屏幕上定义的项目范围设置将被忽略. 你可以这样定义 front matter :'", "text: `    ---\r\n    alias:\r\n    - Example Alias\r\n    tags:\r\n    - example-tag\r\n    number headings: first-level 1, start-at 2, max 6, 1.1, auto, contents ^toc\r\n    ---`": "text: `    ---\r\n    alias:\r\n    - Example <PERSON><PERSON>\r\n    tags:\r\n    - example-tag\r\n    number headings: first-level 1, start-at 2, max 6, 1.1, auto, contents ^toc\r\n    ---`", "text: `\r\n      The 'number headings' front matter key is used to store numbering settings specific to the file. There are four possible options\r\n      in the value to the right of the colon, separated by commas.\r\n    `": "text: `\n       'number headings' 前置内容键用于存储特定于文件的编号设置。冒号右侧的值中有四个可能的选项，用逗号分隔.\n    `", "text: 'Automatic numbering'": "text: '自动编号'", "text: ': If \\'": "text: ': If \\'", "text: 'First level to number'": "text: '从…… 开始为第一个标题编号'", "text: 'Start numbering first heading at'": "text: '从以下位置开始编号第一个标题'", "text: 'Maximum level to number'": "text: '要编号的最大级别'", "text: 'Table of contents anchor'": "text: '目录锚点'", "text: 'Skip headings anchor'": "text: '跳过标题锚点'", "text: 'Numbering style'": "text: '编号样式'", "text: `      \r\n      For example, '1.1' means both top level and other headings will be numbered starting from '1'.\r\n    `": "text: `      \n      例如,'1.1'表示顶层和其他标题都将从'1'开始编号.\n    `", "text: `      \r\n      For example, 'A.1' means top level headings will be numbered starting from 'A'.\r\n    `": "text: `      \n      例如, 'A.1' 表示顶层标题将从 'A' 开始编号.\n    `", "text: `      \r\n      For example, '_.A.1' means top level headings will NOT be numbered, but the next levels will be numbered with letters and numbers.\r\n    `": "text: `      \n      例如, '_.A.1' 表示顶层标题将不编号，但下一级别将用字母和数字编号.\n    `", "text: `      \r\n      For example, '1.1:' means headings will look like '## 2.4: Example Heading'\r\n    `": "text: `      \n      例如, '1.1:' 表示标题将看起来像 '## 2.4: 示例标题'\n    `", "text: `      \r\n      For example, 'A.1-' means headings will look like '## B.5- Example Heading'\r\n    `": "text: `      \n      例如, 'A.1-' 表示标题将看起来像 '## B.5- 示例标题'\n    `", "text: `      \r\n      For example, 'I.A —' means headings will look like '## IV.A — Example Heading' (with Roman numerals)\r\n    `": "text: `      \n      例如, 'I.A —' 表示标题将看起来像 '## IV.A — 示例标题' (使用罗马数字)\n    `", "text: 'Numbering off'": "text: '编号关闭'", ".setText('Number Headings - Successfully Completed')": ".setText('标题编号-已成功完成')", ".setText('No')": ".setText('不')", ".setText('Yes, save settings in document')": ".setText('是，将设置保存在文档中')", ".setText('Yes, save settings in document, and automatically number')": ".setText('是，将设置保存在文档中，并自动编号')", ".setName('Skip top heading level')": ".setName('跳过顶级标题级别')", ".setName('First heading level')": ".set<PERSON>ame('第一标题级别')", ".setName('Start numbering at')": ".set<PERSON>ame('从…… 开始编号')", ".setName('Maximum heading level')": ".setName('最大标题级别')", ".setName('Style for level 1 headings')": ".<PERSON><PERSON><PERSON>('一级标题的样式')", ".setName('Style for lower level headings (below level 1)')": ".setName('较低级别标题样式（低于级别1）')", ".setName('Automatic numbering')": ".setName('自动编号')", ".setName('Separator style')": ".setName('分隔符样式')", ".setName('Table of Contents Anchor')": ".setName('目录锚点')", ".setName('Skip Headings Anchor')": ".setName('跳过标题锚点')", ".setDesc('If selected, numbering will not be applied to the top heading level.')": ".setDesc('如果选中，编号将不会应用于顶级标题级别。')", ".setDesc('First heading level to number.')": ".setDesc('要编号的第一个标题级别。')", ".setDesc('Start numbering the first heading level from this value.')": ".setDesc('从这个值开始对第一个标题级别进行编号。')", ".setDesc('Maximum heading level to number.')": ".setDesc('要编号的最大标题级别。')", ".setDesc('Defines the numbering style for level one headings. Valid values are 1 (for numbers) or A (for capital letters) or I (for Roman numerals).')": ".setDesc('定义一级以下标题的编号样式。有效值为 1（表示数字）、A（表示大写字母）或 I（表示罗马数字）。')", ".setDesc('Defines the numbering style for headings below level one. Valid values are 1 (for numbers) or A (for capital letters) or I (for Roman numerals).')": ".setDesc('定义一级以下标题的编号样式。有效值为1（表示数字）或A（表示大写字母）或I（表示罗马数字）。')", ".setDesc('Turns on automatic numbering of documents.')": ".setDesc('开启文档的自动编号')", ".setDesc('Defines the separator style between the heading number and the heading text. Valid values are : (colon) or . (dot) or - (dash) or — (emdash) or ) (a right parenthesis). You can also leave it blank for no separator, or have a space before the separator.')": ".setDesc('定义标题编号和标题文本之间的分隔符样式。有效值为：冒号（:）、点（.）、破折号（-）、长破折号（—）或右括号（)）。你也可以留空表示没有分隔符，或者在分隔符前有一个空格。')", ".setDesc('Anchor which labels the header where a table of contents should be inserted. The anchor should be added at the end of a header. For example, ^toc.')": ".setDesc('用于标记应插入目录的标题的锚点。该锚点应添加在标题的末尾。例如，^toc.')", ".setDesc('Anchor which labels the headers that should not be numbered. The anchor should be added at the end of a header. For example, ^skipped.')": ".setDesc('用于标记不应编号的标题的锚点。该锚点应添加在标题的末尾。例如，^skipped.')", ".setTooltip('Skip top heading level')": ".setTooltip('跳过顶级级别标题')", ".setTooltip('Turn on automatic numbering')": ".setTooltip('启用自动编号')"}}