{"manifest": {"translationVersion": 1744633280876, "pluginVersion": "1.11.0"}, "description": {"original": "Export your notes to PDF, support export preview, add bookmarks outline and header/footer.", "translation": "将笔记导出为 PDF，支持导出预览，添加书签大纲和页眉/页脚。"}, "dict": {"Notice(\"data is empty!\")": "Notice(\"数据为空！\")", "text: \"Support the continued development of this plugin.\"": "text: \"支持此插件的持续开发\"", ".setText(\"Export to PDF\")": ".setText(\"导出为PDF\")", ".setButtonText(\"Export\")": ".setButtonText(\"导出\")", ".setButtonText(\"Refresh\")": ".setButtonText(\"刷新预览\")", ".setButtonText(\"Debug\")": ".setButtonText(\"调试\")", ".setName(\"Width/Height\")": ".setName(\"宽度/高度\")", ".setName(\"Top/Bottom\")": ".set<PERSON>ame(\"顶部/底部\")", ".setName(\"Left/Right\")": ".setName(\"左/右\")", ".setName(\"Advanced\")": ".<PERSON><PERSON><PERSON>(\"高级\")", ".setDesc(\"The unit is millimeters.\")": ".setDesc(\"单位是毫米\")", ".setDesc(\"Whether to print background graphics\")": ".setDesc(\"是否打印背景图形\")", ".setDesc(\"HTML template for the print footer. Should use the same format as the headerTemplate.\")": ".setDesc(\"页脚的HTML模板。应使用与页眉模版相同的格式。\")", ".setDesc(\"Add timestamp to output file name\")": ".setDesc(\"在输出文件名中添加时间戳\")", ".setDesc(\"Select the css snippet that are not enabled\")": ".setDesc(\"选择未启用的css片段\")", ".setDesc(\"This is useful for troubleshooting.\")": ".setDesc(\"用于故障排除\")", ".setPlaceholder(\"width\")": ".setPlaceholder(\"宽度\")", ".setPlaceholder(\"height\")": ".setPlaceholder(\"高度\")", ".setPlaceholder(\"margin top\")": ".setPlaceholder(\"页边距上\")", ".setPlaceholder(\"margin bottom\")": ".setPlaceholder(\"页边距下\")", ".setPlaceholder(\"margin left\")": ".setPlaceholder(\"页边距左\")", ".setPlaceholder(\"margin right\")": ".setPlaceholder(\"页边距右\")", ".setDesc(\n      'HTML template for the print header. Should be valid HTML markup with following classes used to inject printing values into them: date (formatted print date), title (document title), url (document location), pageNumber (current page number) and totalPages (total pages in the document). For example, <span class=\"title\"></span> would generate span containing the title.'\n    )": ".setDesc(\n      '页眉的 HTML 模板。应为有效的 HTML 标记，并使用以下类将打印值注入其中：date（格式化的打印日期）、title（文档标题）、url（文档位置）、pageNumber（当前页码）和 totalPages（文档的总页数）。例如，<span class=\"title\"></span> 会生成一个包含标题的 span 元素。'\n    )", "\"Export folder to PDF\"": "\"将文件夹导出为PDF\"", "\"Export each file to PDF\"": "\"将每个文件单独导出为PDF\"", ".setName(\"Generate tagged PDF\")": ".setName(\"生成带标签的 PDF\")", ".setName(\"Debug\")": ".setName(\"调试\")", ".setDesc(\n      \"Whether or not to generate a tagged (accessible) PDF. Defaults to false. As this property is experimental, the generated PDF may not adhere fully to PDF/UA and WCAG standards.\"\n    )": ".setDesc(\n      \"即是否生成一个带标签的（可访问的）PDF，默认值为 false ,这个特性是实验性的，生成的带标签的 PDF 可能无法完全符合 PDF/UA 和 WCAG 标准\"\n    )", ".setDesc(\"Add frontMatter(title, author, keywords, subject creator, etc) to pdf metadata\")": ".setDesc(\"将Front tMatter（标题、作者、关键字、主题创建者等）添加到pdf元数据\")", ".log(`md render time:${(/* @__PURE__ */ new Date()": ".log(`md render time:${(/* @__PURE__ */ new Date()", "name: \"PDF\"": "name: \"PDF\"", "text: \"xxxxx\"": "text: \"xxxxx\"", ".setTooltip(\"Include file name as title\")": ".setTooltip(\"包含文件名作为标题\")", "\"Better Export PDF\"": "\"导出为 PDF (Better Export PDF)\"", ".log(\"output pdf:\", outputFile)": ".log(\"输出pdf:\", outputFile)", "alog({\n    title: \"Export to PDF\",\n    defaultPath: filename + (isTimestamp ? \"-\" + Date.now()": "alog({\n    title: \"导出为PDF\",\n    defaultPath: filename + (isTimestamp ? \"-\" + Date.now()", "alog({\n    title: \"Export to PDF\",\n    defaultPath: filename,\n    properties: [\"openDirectory\"]\n  })": "alog({\n    title: \"导出为PDF\",\n    defaultPath: filename,\n    properties: [\"openDirectory\"]\n  })", ".log(\"output:\", outputPath)": ".log(\"输出:\", outputPath)", " error(\"Invalid rotation: \" + JSON.stringify(rotation)": " error(\"无效的旋转参数: \" + JSON.stringify(rotation)", " error(\"Invalid color: \" + JSON.stringify(color)": " error(\"无效的颜色参数: \" + JSON.stringify(color)", "name: \"' + name + '\"": "name: \"' + 名称+ '\"", "name: \"All Files\"": "name: \"所有文件\"", ".setTooltip(\"landscape\")": ".setTooltip(\"横向排版\")", ".setTooltip(\"Display header\")": ".setTooltip(\"显示页眉\")", ".setTooltip(\"Display footer\")": ".setTooltip(\"显示页脚\")", ".setTooltip(\"Open the exported file after exporting.\")": ".setTooltip(\"导出后自动打开文件\")", ".addOption(\"0\", \"None\")": ".addOption(\"0\", \"无\")", ".addOption(\"1\", \"Default\")": ".addOption(\"1\", \"默认\")", ".addOption(\"2\", \"Small\")": ".addOption(\"2\", \"窄页边距\")", ".addOption(\"3\", \"Custom\")": ".addOption(\"3\", \"自定义\")", ".addOption(\"0\", \"Not select\")": ".addOption(\"0\", \"不选择\")", "\"Legal\",": "\"法律专用纸(Legal)\",", "\"Letter\",": "\"信纸(Letter)\",", "\"Tabloid\",": "\"Tabloid\",", "\"Ledger\",": "\"Ledger\",", "\"Custom\"": "\"自定义\""}}