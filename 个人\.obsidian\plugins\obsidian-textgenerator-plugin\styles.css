/* @tailwind base; */
.plug-tg-container {
  width: 100%;
}
@media (min-width: 640px) {
  .plug-tg-container {
    max-width: 640px;
  }
}
@media (min-width: 768px) {
  .plug-tg-container {
    max-width: 768px;
  }
}
@media (min-width: 1024px) {
  .plug-tg-container {
    max-width: 1024px;
  }
}
@media (min-width: 1280px) {
  .plug-tg-container {
    max-width: 1280px;
  }
}
@media (min-width: 1536px) {
  .plug-tg-container {
    max-width: 1536px;
  }
}
@media (hover:hover) {
  .plug-tg-checkbox-primary:hover {
    --tw-border-opacity: 1;
    border-color: hsl(var(--p) / var(--tw-border-opacity));
  }
  .plug-tg-menu li > *:not(ul):not(.plug-tg-menu-title):not(details):active,
.plug-tg-menu li > *:not(ul):not(.plug-tg-menu-title):not(details).plug-tg-active,
.plug-tg-menu li > details > summary:active {
    --tw-bg-opacity: 1;
    background-color: hsl(var(--n) / var(--tw-bg-opacity));
    --tw-text-opacity: 1;
    color: hsl(var(--nc) / var(--tw-text-opacity));
  }
}
.plug-tg-btn {
  display: inline-flex;
  flex-shrink: 0;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  border-color: transparent;
  border-color: hsl(var(--b2) / var(--tw-border-opacity));
  text-align: center;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  transition-duration: 200ms;
  border-radius: var(--rounded-btn, 0.5rem);
  height: 3rem;
  padding-left: 1rem;
  padding-right: 1rem;
  min-height: 3rem;
  font-size: 0.875rem;
  line-height: 1em;
  gap: 0.5rem;
  font-weight: 600;
  text-decoration-line: none;
  border-width: var(--border-btn, 1px);
  animation: button-pop var(--animation-btn, 0.25s) ease-out;
  text-transform: var(--btn-text-case, uppercase);
  --tw-border-opacity: 1;
  --tw-bg-opacity: 1;
  background-color: hsl(var(--b2) / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: hsl(var(--bc) / var(--tw-text-opacity));
  outline-color: hsl(var(--bc) / 1);
}
.plug-tg-btn-disabled,
  .plug-tg-btn[disabled],
  .plug-tg-btn:disabled {
  pointer-events: none;
}
.plug-tg-btn-group > input[type="radio"].plug-tg-btn {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.plug-tg-btn-group > input[type="radio"].plug-tg-btn:before {
  content: attr(data-title);
}
.plug-tg-btn:is(input[type="checkbox"]),
.plug-tg-btn:is(input[type="radio"]) {
  width: auto;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.plug-tg-btn:is(input[type="checkbox"]):after,
.plug-tg-btn:is(input[type="radio"]):after {
  --tw-content: attr(aria-label);
  content: var(--tw-content);
}
.plug-tg-checkbox {
  flex-shrink: 0;
  --chkbg: var(--bc);
  --chkfg: var(--b1);
  height: 1.5rem;
  width: 1.5rem;
  cursor: pointer;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border-width: 1px;
  border-color: hsl(var(--bc) / var(--tw-border-opacity));
  --tw-border-opacity: 0.2;
  border-radius: var(--rounded-btn, 0.5rem);
}
.plug-tg-collapse:not(td):not(tr):not(colgroup) {
  visibility: visible;
}
.plug-tg-collapse {
  position: relative;
  display: grid;
  overflow: hidden;
  grid-template-rows: auto 0fr;
  transition: grid-template-rows 0.2s;
  width: 100%;
  border-radius: var(--rounded-box, 1rem);
}
.plug-tg-collapse-title,
.plug-tg-collapse > input[type="checkbox"],
.plug-tg-collapse > input[type="radio"],
.plug-tg-collapse-content {
  grid-column-start: 1;
  grid-row-start: 1;
}
.plug-tg-collapse > input[type="checkbox"],
.plug-tg-collapse > input[type="radio"] {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  opacity: 0;
}
.plug-tg-collapse-content {
  visibility: hidden;
  grid-column-start: 1;
  grid-row-start: 2;
  min-height: 0px;
  transition: visibility 0.2s;
  transition: padding 0.2s ease-out,
    background-color 0.2s ease-out;
  padding-left: 1rem;
  padding-right: 1rem;
  cursor: unset;
}
.plug-tg-collapse[open],
.plug-tg-collapse-open,
.plug-tg-collapse:focus:not(.plug-tg-collapse-close) {
  grid-template-rows: auto 1fr;
}
.plug-tg-collapse:not(.plug-tg-collapse-close):has(> input[type="checkbox"]:checked),
.plug-tg-collapse:not(.plug-tg-collapse-close):has(> input[type="radio"]:checked) {
  grid-template-rows: auto 1fr;
}
.plug-tg-collapse[open] > .plug-tg-collapse-content,
.plug-tg-collapse-open > .plug-tg-collapse-content,
.plug-tg-collapse:focus:not(.plug-tg-collapse-close) > .plug-tg-collapse-content,
.plug-tg-collapse:not(.plug-tg-collapse-close) > input[type="checkbox"]:checked ~ .plug-tg-collapse-content,
.plug-tg-collapse:not(.plug-tg-collapse-close) > input[type="radio"]:checked ~ .plug-tg-collapse-content {
  visibility: visible;
  min-height: -moz-fit-content;
  min-height: fit-content;
}
.plug-tg-dropdown {
  position: relative;
  display: inline-block;
}
.plug-tg-dropdown > *:not(summary):focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.plug-tg-dropdown .plug-tg-dropdown-content {
  position: absolute;
}
.plug-tg-dropdown:is(:not(details)) .plug-tg-dropdown-content {
  visibility: hidden;
  opacity: 0;
  transform-origin: top;
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  transition-duration: 200ms;
}
.plug-tg-dropdown-end .plug-tg-dropdown-content {
  right: 0px;
}
.plug-tg-dropdown-end.plug-tg-dropdown-right .plug-tg-dropdown-content {
  bottom: 0px;
  top: auto;
}
.plug-tg-dropdown-end.plug-tg-dropdown-left .plug-tg-dropdown-content {
  bottom: 0px;
  top: auto;
}
.plug-tg-dropdown.plug-tg-dropdown-open .plug-tg-dropdown-content,
.plug-tg-dropdown:not(.plug-tg-dropdown-hover):focus .plug-tg-dropdown-content,
.plug-tg-dropdown:focus-within .plug-tg-dropdown-content {
  visibility: visible;
  opacity: 1;
}
@media (hover: hover) {
  .plug-tg-dropdown.plug-tg-dropdown-hover:hover .plug-tg-dropdown-content {
    visibility: visible;
    opacity: 1;
  }
  .plug-tg-btn:hover {
    --tw-border-opacity: 1;
    border-color: hsl(var(--b3) / var(--tw-border-opacity));
    --tw-bg-opacity: 1;
    background-color: hsl(var(--b3) / var(--tw-bg-opacity));
  }
  .plug-tg-btn.plug-tg-glass:hover {
    --glass-opacity: 25%;
    --glass-border-opacity: 15%;
  }
  .plug-tg-btn-disabled:hover,
    .plug-tg-btn[disabled]:hover,
    .plug-tg-btn:disabled:hover {
    --tw-border-opacity: 0;
    background-color: hsl(var(--n) / var(--tw-bg-opacity));
    --tw-bg-opacity: 0.2;
    color: hsl(var(--bc) / var(--tw-text-opacity));
    --tw-text-opacity: 0.2;
  }
  .plug-tg-btn:is(input[type="checkbox"]:checked):hover, .plug-tg-btn:is(input[type="radio"]:checked):hover {
    --tw-border-opacity: 1;
    border-color: hsl(var(--pf) / var(--tw-border-opacity));
    --tw-bg-opacity: 1;
    background-color: hsl(var(--pf) / var(--tw-bg-opacity));
  }
  .plug-tg-dropdown.plug-tg-dropdown-hover:hover .plug-tg-dropdown-content {
    --tw-scale-x: 1;
    --tw-scale-y: 1;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }
  :where(.plug-tg-menu li:not(.plug-tg-menu-title):not(.plug-tg-disabled) > *:not(ul):not(details):not(.plug-tg-menu-title)):not(.plug-tg-active):hover, :where(.plug-tg-menu li:not(.plug-tg-menu-title):not(.plug-tg-disabled) > details > summary:not(.plug-tg-menu-title)):not(.plug-tg-active):hover {
    cursor: pointer;
    background-color: hsl(var(--bc) / 0.1);
    --tw-text-opacity: 1;
    color: hsl(var(--bc) / var(--tw-text-opacity));
    outline: 2px solid transparent;
    outline-offset: 2px;
  }
}
.plug-tg-dropdown:is(details) summary::-webkit-details-marker {
  display: none;
}
.plug-tg-input {
  flex-shrink: 1;
  height: 3rem;
  padding-left: 1rem;
  padding-right: 1rem;
  font-size: 1rem;
  line-height: 2;
  line-height: 1.5rem;
  border-width: 1px;
  border-color: hsl(var(--bc) / var(--tw-border-opacity));
  --tw-border-opacity: 0;
  --tw-bg-opacity: 1;
  background-color: hsl(var(--b1) / var(--tw-bg-opacity));
  border-radius: var(--rounded-btn, 0.5rem);
}
.plug-tg-input-group > .plug-tg-input {
  isolation: isolate;
}
.plug-tg-input-group > *,
  .plug-tg-input-group > .plug-tg-input,
  .plug-tg-input-group > .plug-tg-textarea,
  .plug-tg-input-group > .plug-tg-select {
  border-radius: 0px;
}
.plug-tg-join .plug-tg-dropdown .plug-tg-join-item:first-child:not(:last-child),
  .plug-tg-join *:first-child:not(:last-child) .plug-tg-dropdown .plug-tg-join-item {
  border-start-end-radius: inherit;
  border-end-end-radius: inherit;
}
.plug-tg-menu {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  font-size: 0.875rem;
  line-height: 1.25rem;
  padding: 0.5rem;
}
.plug-tg-menu :where(li ul) {
  position: relative;
  white-space: nowrap;
  margin-left: 1rem;
  padding-left: 0.5rem;
}
.plug-tg-menu :where(li:not(.plug-tg-menu-title) > *:not(ul):not(details):not(.plug-tg-menu-title)),
  .plug-tg-menu :where(li:not(.plug-tg-menu-title) > details > summary:not(.plug-tg-menu-title)) {
  display: grid;
  grid-auto-flow: column;
  align-content: flex-start;
  align-items: center;
  gap: 0.5rem;
  grid-auto-columns: minmax(auto, max-content) auto max-content;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.plug-tg-menu li.plug-tg-disabled {
  cursor: not-allowed;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  color: hsl(var(--bc) / 0.3);
}
.plug-tg-menu :where(li > .plug-tg-menu-dropdown:not(.plug-tg-menu-dropdown-show)) {
  display: none;
}
:where(.plug-tg-menu li) {
  position: relative;
  display: flex;
  flex-shrink: 0;
  flex-direction: column;
  flex-wrap: wrap;
  align-items: stretch;
}
:where(.plug-tg-menu li) .plug-tg-badge {
  justify-self: end;
}
.plug-tg-radio {
  flex-shrink: 0;
  --chkbg: var(--bc);
  height: 1.5rem;
  width: 1.5rem;
  cursor: pointer;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border-radius: 9999px;
  border-width: 1px;
  border-color: hsl(var(--bc) / var(--tw-border-opacity));
  --tw-border-opacity: 0.2;
}
.plug-tg-select {
  display: inline-flex;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  height: 3rem;
  padding-left: 1rem;
  padding-right: 2.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  line-height: 2;
  min-height: 3rem;
  border-width: 1px;
  border-color: hsl(var(--bc) / var(--tw-border-opacity));
  --tw-border-opacity: 0;
  --tw-bg-opacity: 1;
  background-color: hsl(var(--b1) / var(--tw-bg-opacity));
  border-radius: var(--rounded-btn, 0.5rem);
  background-image: linear-gradient(45deg, transparent 50%, currentColor 50%),
    linear-gradient(135deg, currentColor 50%, transparent 50%);
  background-position: calc(100% - 20px) calc(1px + 50%),
    calc(100% - 16.1px) calc(1px + 50%);
  background-size: 4px 4px,
    4px 4px;
  background-repeat: no-repeat;
}
.plug-tg-select[multiple] {
  height: auto;
}
.plug-tg-textarea {
  flex-shrink: 1;
  min-height: 3rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  line-height: 2;
  border-width: 1px;
  border-color: hsl(var(--bc) / var(--tw-border-opacity));
  --tw-border-opacity: 0;
  --tw-bg-opacity: 1;
  background-color: hsl(var(--b1) / var(--tw-bg-opacity));
  border-radius: var(--rounded-btn, 0.5rem);
}
.plug-tg-toggle {
  flex-shrink: 0;
  --tglbg: hsl(var(--b1));
  --handleoffset: 1.5rem;
  --handleoffsetcalculator: calc(var(--handleoffset) * -1);
  --togglehandleborder: 0 0;
  height: 1.5rem;
  width: 3rem;
  cursor: pointer;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border-width: 1px;
  border-color: hsl(var(--bc) / var(--tw-border-opacity));
  --tw-border-opacity: 0.2;
  background-color: hsl(var(--bc) / var(--tw-bg-opacity));
  --tw-bg-opacity: 0.5;
  border-radius: var(--rounded-badge, 1.9rem);
  transition: background,
    box-shadow var(--animation-input, 0.2s) ease-out;
  box-shadow: var(--handleoffsetcalculator) 0 0 2px var(--tglbg) inset,
    0 0 0 2px var(--tglbg) inset,
    var(--togglehandleborder);
}
.plug-tg-btn:active:hover,
  .plug-tg-btn:active:focus {
  animation: button-pop 0s ease-out;
  transform: scale(var(--btn-focus-scale, 0.97));
}
.plug-tg-btn:focus-visible {
  outline-style: solid;
  outline-width: 2px;
  outline-offset: 2px;
}
.plug-tg-btn.plug-tg-glass {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  outline-color: currentColor;
}
.plug-tg-btn.plug-tg-glass.plug-tg-btn-active {
  --glass-opacity: 25%;
  --glass-border-opacity: 15%;
}
.plug-tg-btn.plug-tg-btn-disabled,
  .plug-tg-btn[disabled],
  .plug-tg-btn:disabled {
  --tw-border-opacity: 0;
  background-color: hsl(var(--n) / var(--tw-bg-opacity));
  --tw-bg-opacity: 0.2;
  color: hsl(var(--bc) / var(--tw-text-opacity));
  --tw-text-opacity: 0.2;
}
.plug-tg-btn-group > input[type="radio"]:checked.plug-tg-btn,
  .plug-tg-btn-group > .plug-tg-btn-active {
  --tw-border-opacity: 1;
  border-color: hsl(var(--p) / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: hsl(var(--p) / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: hsl(var(--pc) / var(--tw-text-opacity));
}
.plug-tg-btn-group > input[type="radio"]:checked.plug-tg-btn:focus-visible, .plug-tg-btn-group > .plug-tg-btn-active:focus-visible {
  outline-style: solid;
  outline-width: 2px;
  outline-color: hsl(var(--p) / 1);
}
.plug-tg-btn:is(input[type="checkbox"]:checked),
.plug-tg-btn:is(input[type="radio"]:checked) {
  --tw-border-opacity: 1;
  border-color: hsl(var(--p) / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: hsl(var(--p) / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: hsl(var(--pc) / var(--tw-text-opacity));
}
.plug-tg-btn:is(input[type="checkbox"]:checked):focus-visible, .plug-tg-btn:is(input[type="radio"]:checked):focus-visible {
  outline-color: hsl(var(--p) / 1);
}
@keyframes button-pop {
  0% {
    transform: scale(var(--btn-focus-scale, 0.98));
  }
  40% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}
.plug-tg-checkbox:focus-visible {
  outline-style: solid;
  outline-width: 2px;
  outline-offset: 2px;
  outline-color: hsl(var(--bc) / 1);
}
.plug-tg-checkbox:checked,
  .plug-tg-checkbox[checked="true"],
  .plug-tg-checkbox[aria-checked="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--bc) / var(--tw-bg-opacity));
  background-repeat: no-repeat;
  animation: checkmark var(--animation-input, 0.2s) ease-out;
  background-image: linear-gradient(-45deg, transparent 65%, hsl(var(--chkbg)) 65.99%),
      linear-gradient(45deg, transparent 75%, hsl(var(--chkbg)) 75.99%),
      linear-gradient(-45deg, hsl(var(--chkbg)) 40%, transparent 40.99%),
      linear-gradient(
        45deg,
        hsl(var(--chkbg)) 30%,
        hsl(var(--chkfg)) 30.99%,
        hsl(var(--chkfg)) 40%,
        transparent 40.99%
      ),
      linear-gradient(-45deg, hsl(var(--chkfg)) 50%, hsl(var(--chkbg)) 50.99%);
}
.plug-tg-checkbox:indeterminate {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--bc) / var(--tw-bg-opacity));
  background-repeat: no-repeat;
  animation: checkmark var(--animation-input, 0.2s) ease-out;
  background-image: linear-gradient(90deg, transparent 80%, hsl(var(--chkbg)) 80%),
      linear-gradient(-90deg, transparent 80%, hsl(var(--chkbg)) 80%),
      linear-gradient(
        0deg,
        hsl(var(--chkbg)) 43%,
        hsl(var(--chkfg)) 43%,
        hsl(var(--chkfg)) 57%,
        hsl(var(--chkbg)) 57%
      );
}
.plug-tg-checkbox-primary {
  --chkbg: var(--p);
  --chkfg: var(--pc);
  --tw-border-opacity: 1;
  border-color: hsl(var(--p) / var(--tw-border-opacity));
}
.plug-tg-checkbox-primary:focus-visible {
  outline-color: hsl(var(--p) / 1);
}
.plug-tg-checkbox-primary:checked,
    .plug-tg-checkbox-primary[checked="true"],
    .plug-tg-checkbox-primary[aria-checked="true"] {
  --tw-border-opacity: 1;
  border-color: hsl(var(--p) / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: hsl(var(--p) / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: hsl(var(--pc) / var(--tw-text-opacity));
}
.plug-tg-checkbox:disabled {
  cursor: not-allowed;
  border-color: transparent;
  --tw-bg-opacity: 1;
  background-color: hsl(var(--bc) / var(--tw-bg-opacity));
  opacity: 0.2;
}
@keyframes checkmark {
  0% {
    background-position-y: 5px;
  }
  50% {
    background-position-y: -2px;
  }
  100% {
    background-position-y: 0;
  }
}
[dir="rtl"] .plug-tg-checkbox:checked,
    [dir="rtl"] .plug-tg-checkbox[checked="true"],
    [dir="rtl"] .plug-tg-checkbox[aria-checked="true"] {
  background-image: linear-gradient(45deg, transparent 65%, hsl(var(--chkbg)) 65.99%),
        linear-gradient(-45deg, transparent 75%, hsl(var(--chkbg)) 75.99%),
        linear-gradient(45deg, hsl(var(--chkbg)) 40%, transparent 40.99%),
        linear-gradient(
          -45deg,
          hsl(var(--chkbg)) 30%,
          hsl(var(--chkfg)) 30.99%,
          hsl(var(--chkfg)) 40%,
          transparent 40.99%
        ),
        linear-gradient(45deg, hsl(var(--chkfg)) 50%, hsl(var(--chkbg)) 50.99%);
}
details.plug-tg-collapse {
  width: 100%;
}
details.plug-tg-collapse summary {
  position: relative;
  display: block;
  outline: 2px solid transparent;
  outline-offset: 2px;
}
details.plug-tg-collapse summary::-webkit-details-marker {
  display: none;
}
.plug-tg-collapse:focus-visible {
  outline-style: solid;
  outline-width: 2px;
  outline-offset: 2px;
  outline-color: hsl(var(--bc) / 1);
}
.plug-tg-collapse:has(.plug-tg-collapse-title:focus-visible),
.plug-tg-collapse:has(> input[type="checkbox"]:focus-visible),
.plug-tg-collapse:has(> input[type="radio"]:focus-visible) {
  outline-style: solid;
  outline-width: 2px;
  outline-offset: 2px;
  outline-color: hsl(var(--bc) / 1);
}
.plug-tg-collapse-arrow > .plug-tg-collapse-title:after {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  position: absolute;
  display: block;
  height: 0.5rem;
  width: 0.5rem;
  --tw-translate-y: -100%;
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  transition-duration: 150ms;
  transition-duration: 0.2s;
  top: 50%;
  right: 1.4rem;
  content: "";
  transform-origin: 75% 75%;
  box-shadow: 2px 2px;
  pointer-events: none;
}
[dir="rtl"] .plug-tg-collapse-arrow > .plug-tg-collapse-title:after {
  --tw-rotate: -45deg;
}
.plug-tg-collapse-plus > .plug-tg-collapse-title:after {
  position: absolute;
  display: block;
  height: 0.5rem;
  width: 0.5rem;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  transition-duration: 300ms;
  top: 0.9rem;
  right: 1.4rem;
  content: "+";
  pointer-events: none;
}
.plug-tg-collapse:not(.plug-tg-collapse-open):not(.plug-tg-collapse-close) > input[type="checkbox"],
.plug-tg-collapse:not(.plug-tg-collapse-open):not(.plug-tg-collapse-close) > input[type="radio"]:not(:checked),
.plug-tg-collapse:not(.plug-tg-collapse-open):not(.plug-tg-collapse-close) > .plug-tg-collapse-title {
  cursor: pointer;
}
.plug-tg-collapse:focus:not(.plug-tg-collapse-open):not(.plug-tg-collapse-close):not(.plug-tg-collapse[open]) > .plug-tg-collapse-title {
  cursor: unset;
}
.plug-tg-collapse-title {
  position: relative;
}
:where(.plug-tg-collapse > input[type="checkbox"]),
:where(.plug-tg-collapse > input[type="radio"]) {
  z-index: 1;
}
.plug-tg-collapse-title,
:where(.plug-tg-collapse > input[type="checkbox"]),
:where(.plug-tg-collapse > input[type="radio"]) {
  width: 100%;
  padding: 1rem;
  padding-right: 3rem;
  min-height: 3.75rem;
  transition: background-color 0.2s ease-out;
}
.plug-tg-collapse[open] > :where(.plug-tg-collapse-content),
.plug-tg-collapse-open > :where(.plug-tg-collapse-content),
.plug-tg-collapse:focus:not(.plug-tg-collapse-close) > :where(.plug-tg-collapse-content),
.plug-tg-collapse:not(.plug-tg-collapse-close) > :where(input[type="checkbox"]:checked ~ .plug-tg-collapse-content),
.plug-tg-collapse:not(.plug-tg-collapse-close) > :where(input[type="radio"]:checked ~ .plug-tg-collapse-content) {
  padding-bottom: 1rem;
  transition: padding 0.2s ease-out,
    background-color 0.2s ease-out;
}
.plug-tg-collapse[open].plug-tg-collapse-arrow > .plug-tg-collapse-title:after,
.plug-tg-collapse-open.plug-tg-collapse-arrow > .plug-tg-collapse-title:after,
.plug-tg-collapse-arrow:focus:not(.plug-tg-collapse-close) > .plug-tg-collapse-title:after,
.plug-tg-collapse-arrow:not(.plug-tg-collapse-close) > input[type="checkbox"]:checked ~ .plug-tg-collapse-title:after,
.plug-tg-collapse-arrow:not(.plug-tg-collapse-close) > input[type="radio"]:checked ~ .plug-tg-collapse-title:after {
  --tw-translate-y: -50%;
  --tw-rotate: 225deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
[dir="rtl"] .plug-tg-collapse[open].plug-tg-collapse-arrow > .plug-tg-collapse-title:after,
[dir="rtl"] .plug-tg-collapse-open.plug-tg-collapse-arrow > .plug-tg-collapse-title:after,
[dir="rtl"] .plug-tg-collapse-arrow:focus:not(.plug-tg-collapse-close) .plug-tg-collapse-title:after,
[dir="rtl"]
  .plug-tg-collapse-arrow:not(.plug-tg-collapse-close)
  input[type="checkbox"]:checked
  ~ .plug-tg-collapse-title:after {
  --tw-rotate: 135deg;
}
.plug-tg-collapse[open].plug-tg-collapse-plus > .plug-tg-collapse-title:after,
.plug-tg-collapse-open.plug-tg-collapse-plus > .plug-tg-collapse-title:after,
.plug-tg-collapse-plus:focus:not(.plug-tg-collapse-close) > .plug-tg-collapse-title:after,
.plug-tg-collapse-plus:not(.plug-tg-collapse-close) > input[type="checkbox"]:checked ~ .plug-tg-collapse-title:after,
.plug-tg-collapse-plus:not(.plug-tg-collapse-close) > input[type="radio"]:checked ~ .plug-tg-collapse-title:after {
  content: "−";
}
.plug-tg-dropdown.plug-tg-dropdown-open .plug-tg-dropdown-content,
.plug-tg-dropdown:focus .plug-tg-dropdown-content,
.plug-tg-dropdown:focus-within .plug-tg-dropdown-content {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.plug-tg-input input:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.plug-tg-input[list]::-webkit-calendar-picker-indicator {
  line-height: 1em;
}
.plug-tg-input:focus,
  .plug-tg-input:focus-within {
  outline-style: solid;
  outline-width: 2px;
  outline-offset: 2px;
  outline-color: hsl(var(--bc) / 0.2);
}
.plug-tg-input-error {
  --tw-border-opacity: 1;
  border-color: hsl(var(--er) / var(--tw-border-opacity));
}
.plug-tg-input-error:focus,
    .plug-tg-input-error:focus-within {
  outline-color: hsl(var(--er) / 1);
}
.plug-tg-input-disabled,
  .plug-tg-input:disabled,
  .plug-tg-input[disabled] {
  cursor: not-allowed;
  --tw-border-opacity: 1;
  border-color: hsl(var(--b2) / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: hsl(var(--b2) / var(--tw-bg-opacity));
  --tw-text-opacity: 0.2;
}
.plug-tg-input-disabled::-moz-placeholder, .plug-tg-input:disabled::-moz-placeholder, .plug-tg-input[disabled]::-moz-placeholder {
  color: hsl(var(--bc) / var(--tw-placeholder-opacity));
  --tw-placeholder-opacity: 0.2;
}
.plug-tg-input-disabled::placeholder,
  .plug-tg-input:disabled::placeholder,
  .plug-tg-input[disabled]::placeholder {
  color: hsl(var(--bc) / var(--tw-placeholder-opacity));
  --tw-placeholder-opacity: 0.2;
}
.plug-tg-loading {
  pointer-events: none;
  display: inline-block;
  aspect-ratio: 1 / 1;
  width: 1.5rem;
  background-color: currentColor;
  -webkit-mask-size: 100%;
          mask-size: 100%;
  -webkit-mask-repeat: no-repeat;
          mask-repeat: no-repeat;
  -webkit-mask-position: center;
          mask-position: center;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg width='24' height='24' stroke='%23000' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cstyle%3E.spinner_V8m1%7Btransform-origin:center;animation:spinner_zKoa 2s linear infinite%7D.spinner_V8m1 circle%7Bstroke-linecap:round;animation:spinner_YpZS 1.5s ease-out infinite%7D%40keyframes spinner_zKoa%7B100%25%7Btransform:rotate(360deg)%7D%7D%40keyframes spinner_YpZS%7B0%25%7Bstroke-dasharray:0 150;stroke-dashoffset:0%7D47.5%25%7Bstroke-dasharray:42 150;stroke-dashoffset:-16%7D95%25%2C100%25%7Bstroke-dasharray:42 150;stroke-dashoffset:-59%7D%7D%3C%2Fstyle%3E%3Cg class='spinner_V8m1'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3'%3E%3C%2Fcircle%3E%3C%2Fg%3E%3C%2Fsvg%3E");
          mask-image: url("data:image/svg+xml,%3Csvg width='24' height='24' stroke='%23000' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cstyle%3E.spinner_V8m1%7Btransform-origin:center;animation:spinner_zKoa 2s linear infinite%7D.spinner_V8m1 circle%7Bstroke-linecap:round;animation:spinner_YpZS 1.5s ease-out infinite%7D%40keyframes spinner_zKoa%7B100%25%7Btransform:rotate(360deg)%7D%7D%40keyframes spinner_YpZS%7B0%25%7Bstroke-dasharray:0 150;stroke-dashoffset:0%7D47.5%25%7Bstroke-dasharray:42 150;stroke-dashoffset:-16%7D95%25%2C100%25%7Bstroke-dasharray:42 150;stroke-dashoffset:-59%7D%7D%3C%2Fstyle%3E%3Cg class='spinner_V8m1'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3'%3E%3C%2Fcircle%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}
:where(.plug-tg-menu li:empty) {
  background-color: hsl(var(--bc) / 0.1);
  margin: 0.5rem 1rem;
  height: 1px;
}
.plug-tg-menu :where(li ul):before {
  position: absolute;
  bottom: 0.75rem;
  left: 0px;
  top: 0.75rem;
  width: 1px;
  background-color: hsl(var(--bc) / 0.1);
  content: "";
}
.plug-tg-menu :where(li:not(.plug-tg-menu-title) > *:not(ul):not(details):not(.plug-tg-menu-title)),
.plug-tg-menu :where(li:not(.plug-tg-menu-title) > details > summary:not(.plug-tg-menu-title)) {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  text-align: left;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
  transition-duration: 200ms;
  border-radius: var(--rounded-btn, 0.5rem);
  text-wrap: balance;
}
:where(.plug-tg-menu li:not(.plug-tg-menu-title):not(.plug-tg-disabled) > *:not(ul):not(details):not(.plug-tg-menu-title)):not(summary):not(.plug-tg-active).plug-tg-focus,
  :where(.plug-tg-menu li:not(.plug-tg-menu-title):not(.plug-tg-disabled) > *:not(ul):not(details):not(.plug-tg-menu-title)):not(summary):not(.plug-tg-active):focus,
  :where(.plug-tg-menu li:not(.plug-tg-menu-title):not(.plug-tg-disabled) > *:not(ul):not(details):not(.plug-tg-menu-title)):is(summary):not(.plug-tg-active):focus-visible,
  :where(.plug-tg-menu li:not(.plug-tg-menu-title):not(.plug-tg-disabled) > details > summary:not(.plug-tg-menu-title)):not(summary):not(.plug-tg-active).plug-tg-focus,
  :where(.plug-tg-menu li:not(.plug-tg-menu-title):not(.plug-tg-disabled) > details > summary:not(.plug-tg-menu-title)):not(summary):not(.plug-tg-active):focus,
  :where(.plug-tg-menu li:not(.plug-tg-menu-title):not(.plug-tg-disabled) > details > summary:not(.plug-tg-menu-title)):is(summary):not(.plug-tg-active):focus-visible {
  cursor: pointer;
  background-color: hsl(var(--bc) / 0.1);
  --tw-text-opacity: 1;
  color: hsl(var(--bc) / var(--tw-text-opacity));
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.plug-tg-menu li > *:not(ul):not(.plug-tg-menu-title):not(details):active,
.plug-tg-menu li > *:not(ul):not(.plug-tg-menu-title):not(details).plug-tg-active,
.plug-tg-menu li > details > summary:active {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--n) / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: hsl(var(--nc) / var(--tw-text-opacity));
}
.plug-tg-menu :where(li > details > summary)::-webkit-details-marker {
  display: none;
}
.plug-tg-menu :where(li > details > summary):after,
.plug-tg-menu :where(li > .plug-tg-menu-dropdown-toggle):after {
  justify-self: end;
  display: block;
  margin-top: -0.5rem;
  height: 0.5rem;
  width: 0.5rem;
  transform: rotate(45deg);
  transition-property: transform, margin-top;
  transition-duration: 0.3s;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  content: "";
  transform-origin: 75% 75%;
  box-shadow: 2px 2px;
  pointer-events: none;
}
.plug-tg-menu :where(li > details[open] > summary):after,
.plug-tg-menu :where(li > .plug-tg-menu-dropdown-toggle.plug-tg-menu-dropdown-show):after {
  transform: rotate(225deg);
  margin-top: 0;
}
.plug-tg-mockup-browser .plug-tg-mockup-browser-toolbar .plug-tg-input {
  position: relative;
  margin-left: auto;
  margin-right: auto;
  display: block;
  height: 1.75rem;
  width: 24rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  --tw-bg-opacity: 1;
  background-color: hsl(var(--b2) / var(--tw-bg-opacity));
  padding-left: 2rem;
}
.plug-tg-mockup-browser .plug-tg-mockup-browser-toolbar .plug-tg-input:before {
  content: "";
  position: absolute;
  left: 0.5rem;
  top: 50%;
  aspect-ratio: 1 / 1;
  height: 0.75rem;
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  border-radius: 9999px;
  border-width: 2px;
  border-color: currentColor;
  opacity: 0.6;
}
.plug-tg-mockup-browser .plug-tg-mockup-browser-toolbar .plug-tg-input:after {
  content: "";
  position: absolute;
  left: 1.25rem;
  top: 50%;
  height: 0.5rem;
  --tw-translate-y: 25%;
  --tw-rotate: -45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  border-radius: 9999px;
  border-width: 1px;
  border-color: currentColor;
  opacity: 0.6;
}
@keyframes modal-pop {
  0% {
    opacity: 0;
  }
}
@keyframes progress-loading {
  50% {
    background-position-x: -115%;
  }
}
.plug-tg-radio:focus-visible {
  outline-style: solid;
  outline-width: 2px;
  outline-offset: 2px;
  outline-color: hsl(var(--bc) / 1);
}
.plug-tg-radio:checked,
  .plug-tg-radio[aria-checked="true"] {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--bc) / var(--tw-bg-opacity));
  animation: radiomark var(--animation-input, 0.2s) ease-out;
  box-shadow: 0 0 0 4px hsl(var(--b1)) inset,
      0 0 0 4px hsl(var(--b1)) inset;
}
.plug-tg-radio:disabled {
  cursor: not-allowed;
  opacity: 0.2;
}
@keyframes radiomark {
  0% {
    box-shadow: 0 0 0 12px hsl(var(--b1)) inset,
      0 0 0 12px hsl(var(--b1)) inset;
  }
  50% {
    box-shadow: 0 0 0 3px hsl(var(--b1)) inset,
      0 0 0 3px hsl(var(--b1)) inset;
  }
  100% {
    box-shadow: 0 0 0 4px hsl(var(--b1)) inset,
      0 0 0 4px hsl(var(--b1)) inset;
  }
}
@keyframes rating-pop {
  0% {
    transform: translateY(-0.125em);
  }
  40% {
    transform: translateY(-0.125em);
  }
  100% {
    transform: translateY(0);
  }
}
.plug-tg-select:focus {
  outline-style: solid;
  outline-width: 2px;
  outline-offset: 2px;
  outline-color: hsl(var(--bc) / 0.2);
}
.plug-tg-select-disabled,
  .plug-tg-select:disabled,
  .plug-tg-select[disabled] {
  cursor: not-allowed;
  --tw-border-opacity: 1;
  border-color: hsl(var(--b2) / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: hsl(var(--b2) / var(--tw-bg-opacity));
  --tw-text-opacity: 0.2;
}
.plug-tg-select-disabled::-moz-placeholder, .plug-tg-select:disabled::-moz-placeholder, .plug-tg-select[disabled]::-moz-placeholder {
  color: hsl(var(--bc) / var(--tw-placeholder-opacity));
  --tw-placeholder-opacity: 0.2;
}
.plug-tg-select-disabled::placeholder,
  .plug-tg-select:disabled::placeholder,
  .plug-tg-select[disabled]::placeholder {
  color: hsl(var(--bc) / var(--tw-placeholder-opacity));
  --tw-placeholder-opacity: 0.2;
}
.plug-tg-select-multiple,
  .plug-tg-select[multiple],
  .plug-tg-select[size].plug-tg-select:not([size="1"]) {
  background-image: none;
  padding-right: 1rem;
}
[dir="rtl"] .plug-tg-select {
  background-position: calc(0% + 12px) calc(1px + 50%),
    calc(0% + 16px) calc(1px + 50%);
}
.plug-tg-textarea:focus {
  outline-style: solid;
  outline-width: 2px;
  outline-offset: 2px;
  outline-color: hsl(var(--bc) / 0.2);
}
.plug-tg-textarea-disabled,
  .plug-tg-textarea:disabled,
  .plug-tg-textarea[disabled] {
  cursor: not-allowed;
  --tw-border-opacity: 1;
  border-color: hsl(var(--b2) / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: hsl(var(--b2) / var(--tw-bg-opacity));
  --tw-text-opacity: 0.2;
}
.plug-tg-textarea-disabled::-moz-placeholder, .plug-tg-textarea:disabled::-moz-placeholder, .plug-tg-textarea[disabled]::-moz-placeholder {
  color: hsl(var(--bc) / var(--tw-placeholder-opacity));
  --tw-placeholder-opacity: 0.2;
}
.plug-tg-textarea-disabled::placeholder,
  .plug-tg-textarea:disabled::placeholder,
  .plug-tg-textarea[disabled]::placeholder {
  color: hsl(var(--bc) / var(--tw-placeholder-opacity));
  --tw-placeholder-opacity: 0.2;
}
@keyframes toast-pop {
  0% {
    transform: scale(0.9);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
[dir="rtl"] .plug-tg-toggle {
  --handleoffsetcalculator: calc(var(--handleoffset) * 1);
}
.plug-tg-toggle:focus-visible {
  outline-style: solid;
  outline-width: 2px;
  outline-offset: 2px;
  outline-color: hsl(var(--bc) / 0.2);
}
.plug-tg-toggle:checked,
  .plug-tg-toggle[checked="true"],
  .plug-tg-toggle[aria-checked="true"] {
  --handleoffsetcalculator: var(--handleoffset);
  --tw-border-opacity: 1;
  --tw-bg-opacity: 1;
}
[dir="rtl"] .plug-tg-toggle:checked, [dir="rtl"] .plug-tg-toggle[checked="true"], [dir="rtl"] .plug-tg-toggle[aria-checked="true"] {
  --handleoffsetcalculator: calc(var(--handleoffset) * -1);
}
.plug-tg-toggle:indeterminate {
  --tw-border-opacity: 1;
  --tw-bg-opacity: 1;
  box-shadow: calc(var(--handleoffset) / 2) 0 0 2px var(--tglbg) inset,
      calc(var(--handleoffset) / -2) 0 0 2px var(--tglbg) inset,
      0 0 0 2px var(--tglbg) inset;
}
[dir="rtl"] .plug-tg-toggle:indeterminate {
  box-shadow: calc(var(--handleoffset) / 2) 0 0 2px var(--tglbg) inset,
        calc(var(--handleoffset) / -2) 0 0 2px var(--tglbg) inset,
        0 0 0 2px var(--tglbg) inset;
}
.plug-tg-toggle:disabled {
  cursor: not-allowed;
  --tw-border-opacity: 1;
  border-color: hsl(var(--bc) / var(--tw-border-opacity));
  background-color: transparent;
  opacity: 0.3;
  --togglehandleborder: 0 0 0 3px hsl(var(--bc)) inset,
      var(--handleoffsetcalculator) 0 0 3px hsl(var(--bc)) inset;
}
.plug-tg-min-h-16 {
  min-height: 4rem;
}
.plug-tg-btn-xs {
  height: 1.5rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  min-height: 1.5rem;
  font-size: 0.75rem;
}
.plug-tg-btn-square:where(.plug-tg-btn-xs) {
  height: 1.5rem;
  width: 1.5rem;
  padding: 0px;
}
.plug-tg-btn-circle:where(.plug-tg-btn-xs) {
  height: 1.5rem;
  width: 1.5rem;
  border-radius: 9999px;
  padding: 0px;
}
.plug-tg-input-sm {
  height: 2rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  font-size: 0.875rem;
  line-height: 2rem;
}
.plug-tg-tooltip {
  position: relative;
  display: inline-block;
  --tooltip-offset: calc(100% + 1px + var(--tooltip-tail, 0px));
}
.plug-tg-tooltip:before {
  position: absolute;
  pointer-events: none;
  z-index: 1;
  content: var(--tw-content);
  --tw-content: attr(data-tip);
}
.plug-tg-tooltip:before, .plug-tg-tooltip-top:before {
  transform: translateX(-50%);
  top: auto;
  left: 50%;
  right: auto;
  bottom: var(--tooltip-offset);
}
.plug-tg-tooltip-bottom:before {
  transform: translateX(-50%);
  top: var(--tooltip-offset);
  left: 50%;
  right: auto;
  bottom: auto;
}
.plug-tg-btn-group .plug-tg-btn:not(:first-child):not(:last-child) {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
.plug-tg-btn-group .plug-tg-btn:first-child:not(:last-child) {
  margin-left: -1px;
  margin-top: -0px;
  border-top-left-radius: var(--rounded-btn, 0.5rem);
  border-top-right-radius: 0;
  border-bottom-left-radius: var(--rounded-btn, 0.5rem);
  border-bottom-right-radius: 0;
}
.plug-tg-btn-group .plug-tg-btn:last-child:not(:first-child) {
  border-top-left-radius: 0;
  border-top-right-radius: var(--rounded-btn, 0.5rem);
  border-bottom-left-radius: 0;
  border-bottom-right-radius: var(--rounded-btn, 0.5rem);
}
.plug-tg-btn-group-horizontal .plug-tg-btn:not(:first-child):not(:last-child) {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
.plug-tg-btn-group-horizontal .plug-tg-btn:first-child:not(:last-child) {
  margin-left: -1px;
  margin-top: -0px;
  border-top-left-radius: var(--rounded-btn, 0.5rem);
  border-top-right-radius: 0;
  border-bottom-left-radius: var(--rounded-btn, 0.5rem);
  border-bottom-right-radius: 0;
}
.plug-tg-btn-group-horizontal .plug-tg-btn:last-child:not(:first-child) {
  border-top-left-radius: 0;
  border-top-right-radius: var(--rounded-btn, 0.5rem);
  border-bottom-left-radius: 0;
  border-bottom-right-radius: var(--rounded-btn, 0.5rem);
}
.plug-tg-btn-group-vertical .plug-tg-btn:first-child:not(:last-child) {
  margin-left: -0px;
  margin-top: -1px;
  border-top-left-radius: var(--rounded-btn, 0.5rem);
  border-top-right-radius: var(--rounded-btn, 0.5rem);
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
.plug-tg-btn-group-vertical .plug-tg-btn:last-child:not(:first-child) {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-bottom-left-radius: var(--rounded-btn, 0.5rem);
  border-bottom-right-radius: var(--rounded-btn, 0.5rem);
}
.plug-tg-tooltip {
  position: relative;
  display: inline-block;
  text-align: center;
  --tooltip-tail: 0.1875rem;
  --tooltip-color: hsl(var(--n));
  --tooltip-text-color: hsl(var(--nc));
  --tooltip-tail-offset: calc(100% + 0.0625rem - var(--tooltip-tail));
}
.plug-tg-tooltip:before,
.plug-tg-tooltip:after {
  opacity: 0;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-delay: 100ms;
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.plug-tg-tooltip:after {
  position: absolute;
  content: "";
  border-style: solid;
  border-width: var(--tooltip-tail, 0);
  width: 0;
  height: 0;
  display: block;
}
.plug-tg-tooltip:before {
  max-width: 20rem;
  border-radius: 0.25rem;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  background-color: var(--tooltip-color);
  color: var(--tooltip-text-color);
  width: -moz-max-content;
  width: max-content;
}
.plug-tg-tooltip.plug-tg-tooltip-open:before,
.plug-tg-tooltip.plug-tg-tooltip-open:after,
.plug-tg-tooltip:hover:before,
.plug-tg-tooltip:hover:after {
  opacity: 1;
  transition-delay: 75ms;
}
.plug-tg-tooltip:has(:focus-visible):after,
.plug-tg-tooltip:has(:focus-visible):before {
  opacity: 1;
  transition-delay: 75ms;
}
.plug-tg-tooltip:not([data-tip]):hover:before,
.plug-tg-tooltip:not([data-tip]):hover:after {
  visibility: hidden;
  opacity: 0;
}
.plug-tg-tooltip:after, .plug-tg-tooltip-top:after {
  transform: translateX(-50%);
  border-color: var(--tooltip-color) transparent transparent transparent;
  top: auto;
  left: 50%;
  right: auto;
  bottom: var(--tooltip-tail-offset);
}
.plug-tg-tooltip-bottom:after {
  transform: translateX(-50%);
  border-color: transparent transparent var(--tooltip-color) transparent;
  top: var(--tooltip-tail-offset);
  left: 50%;
  right: auto;
  bottom: auto;
}
.plug-tg-form-checkbox,.plug-tg-form-radio {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  padding: 0;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
  display: inline-block;
  vertical-align: middle;
  background-origin: border-box;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  flex-shrink: 0;
  height: 1rem;
  width: 1rem;
  color: #2563eb;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  --tw-shadow: 0 0 #0000;
}
.plug-tg-form-checkbox {
  border-radius: 0px;
}
.plug-tg-form-checkbox:focus,.plug-tg-form-radio:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #2563eb;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}
.plug-tg-form-checkbox:checked,.plug-tg-form-radio:checked {
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}
.plug-tg-form-checkbox:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
}
@media (forced-colors: active)  {
  .plug-tg-form-checkbox:checked {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}
.plug-tg-form-checkbox:checked:hover,.plug-tg-form-checkbox:checked:focus,.plug-tg-form-radio:checked:hover,.plug-tg-form-radio:checked:focus {
  border-color: transparent;
  background-color: currentColor;
}
.plug-tg-form-checkbox:indeterminate {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e");
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}
@media (forced-colors: active)  {
  .plug-tg-form-checkbox:indeterminate {
    -webkit-appearance: auto;
       -moz-appearance: auto;
            appearance: auto;
  }
}
.plug-tg-form-checkbox:indeterminate:hover,.plug-tg-form-checkbox:indeterminate:focus {
  border-color: transparent;
  background-color: currentColor;
}
.plug-tg-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.plug-tg-pointer-events-none {
  pointer-events: none;
}
.plug-tg-collapse {
  visibility: collapse;
}
.plug-tg-absolute {
  position: absolute;
}
.plug-tg-relative {
  position: relative;
}
.plug-tg-bottom-0 {
  bottom: 0px;
}
.plug-tg-bottom-\[calc\(-30px\)\] {
  bottom: calc(-30px);
}
.plug-tg-right-0 {
  right: 0px;
}
.plug-tg-top-full {
  top: 100%;
}
.plug-tg-z-20 {
  z-index: 20;
}
.plug-tg-m-0 {
  margin: 0px;
}
.plug-tg-mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.plug-tg-my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.plug-tg-my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.plug-tg-my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.plug-tg-my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}
.plug-tg-mb-1 {
  margin-bottom: 0.25rem;
}
.plug-tg-mb-2 {
  margin-bottom: 0.5rem;
}
.plug-tg-mb-2\.5 {
  margin-bottom: 0.625rem;
}
.plug-tg-mb-4 {
  margin-bottom: 1rem;
}
.plug-tg-ml-1 {
  margin-left: 0.25rem;
}
.plug-tg-ml-2 {
  margin-left: 0.5rem;
}
.plug-tg-ml-6 {
  margin-left: 1.5rem;
}
.plug-tg-ml-auto {
  margin-left: auto;
}
.plug-tg-mr-1 {
  margin-right: 0.25rem;
}
.plug-tg-mr-3 {
  margin-right: 0.75rem;
}
.plug-tg-mt-1 {
  margin-top: 0.25rem;
}
.plug-tg-mt-2 {
  margin-top: 0.5rem;
}
.plug-tg-mt-4 {
  margin-top: 1rem;
}
.plug-tg-block {
  display: block;
}
.plug-tg-inline-block {
  display: inline-block;
}
.plug-tg-flex {
  display: flex;
}
.plug-tg-inline-flex {
  display: inline-flex;
}
.plug-tg-flow-root {
  display: flow-root;
}
.plug-tg-grid {
  display: grid;
}
.plug-tg-hidden {
  display: none;
}
.plug-tg-h-2 {
  height: 0.5rem;
}
.plug-tg-h-24 {
  height: 6rem;
}
.plug-tg-h-3 {
  height: 0.75rem;
}
.plug-tg-h-8 {
  height: 2rem;
}
.plug-tg-h-auto {
  height: auto;
}
.plug-tg-h-fit {
  height: -moz-fit-content;
  height: fit-content;
}
.plug-tg-h-full {
  height: 100%;
}
.plug-tg-h-px {
  height: 1px;
}
.plug-tg-max-h-16 {
  max-height: 4rem;
}
.plug-tg-max-h-32 {
  max-height: 8rem;
}
.plug-tg-min-h-16 {
  min-height: 4rem;
}
.plug-tg-min-h-\[200px\] {
  min-height: 200px;
}
.plug-tg-w-1\/2 {
  width: 50%;
}
.plug-tg-w-1\/4 {
  width: 25%;
}
.plug-tg-w-3 {
  width: 0.75rem;
}
.plug-tg-w-3\/4 {
  width: 75%;
}
.plug-tg-w-48 {
  width: 12rem;
}
.plug-tg-w-\[800px\] {
  width: 800px;
}
.plug-tg-w-auto {
  width: auto;
}
.plug-tg-w-full {
  width: 100%;
}
.plug-tg-min-w-full {
  min-width: 100%;
}
.plug-tg-max-w-\[300px\] {
  max-width: 300px;
}
.plug-tg-max-w-\[330px\] {
  max-width: 330px;
}
.plug-tg-max-w-\[360px\] {
  max-width: 360px;
}
.plug-tg-max-w-\[800px\] {
  max-width: 800px;
}
.plug-tg-max-w-full {
  max-width: 100%;
}
.plug-tg-max-w-sm {
  max-width: 24rem;
}
.plug-tg-max-w-xs {
  max-width: 20rem;
}
.plug-tg-flex-none {
  flex: none;
}
.plug-tg-shrink-0 {
  flex-shrink: 0;
}
.-plug-tg-rotate-180 {
  --tw-rotate: -180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes plug-tg-pulse {
  50% {
    opacity: .5;
  }
}
.plug-tg-animate-pulse {
  animation: plug-tg-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes plug-tg-spin {
  to {
    transform: rotate(360deg);
  }
}
.plug-tg-animate-spin {
  animation: plug-tg-spin 1s linear infinite;
}
.plug-tg-cursor-not-allowed {
  cursor: not-allowed;
}
.plug-tg-cursor-pointer {
  cursor: pointer;
}
.plug-tg-select-none {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.plug-tg-select-text {
  -webkit-user-select: text;
     -moz-user-select: text;
          user-select: text;
}
.plug-tg-select-all {
  -webkit-user-select: all;
     -moz-user-select: all;
          user-select: all;
}
.plug-tg-resize-none {
  resize: none;
}
.plug-tg-resize-y {
  resize: vertical;
}
.plug-tg-list-inside {
  list-style-position: inside;
}
.plug-tg-list-none {
  list-style-type: none;
}
.plug-tg-flex-col {
  flex-direction: column;
}
.plug-tg-flex-wrap {
  flex-wrap: wrap;
}
.plug-tg-items-center {
  align-items: center;
}
.plug-tg-justify-end {
  justify-content: flex-end;
}
.plug-tg-justify-center {
  justify-content: center;
}
.plug-tg-justify-between {
  justify-content: space-between;
}
.plug-tg-justify-items-center {
  justify-items: center;
}
.plug-tg-gap-1 {
  gap: 0.25rem;
}
.plug-tg-gap-1\.5 {
  gap: 0.375rem;
}
.plug-tg-gap-10 {
  gap: 2.5rem;
}
.plug-tg-gap-2 {
  gap: 0.5rem;
}
.plug-tg-gap-3 {
  gap: 0.75rem;
}
.plug-tg-gap-4 {
  gap: 1rem;
}
.plug-tg-gap-5 {
  gap: 1.25rem;
}
.plug-tg-gap-6 {
  gap: 1.5rem;
}
.plug-tg-gap-8 {
  gap: 2rem;
}
.plug-tg-space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.plug-tg-space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.plug-tg-space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.plug-tg-divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}
.plug-tg-divide-gray-200 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-divide-opacity, 1));
}
.plug-tg-overflow-hidden {
  overflow: hidden;
}
.plug-tg-overflow-y-auto {
  overflow-y: auto;
}
.plug-tg-overflow-x-hidden {
  overflow-x: hidden;
}
.plug-tg-overflow-y-scroll {
  overflow-y: scroll;
}
.plug-tg-whitespace-nowrap {
  white-space: nowrap;
}
.plug-tg-break-words {
  overflow-wrap: break-word;
}
.plug-tg-rounded {
  border-radius: 0.25rem;
}
.plug-tg-rounded-full {
  border-radius: 9999px;
}
.plug-tg-rounded-lg {
  border-radius: 0.5rem;
}
.plug-tg-rounded-md {
  border-radius: 0.375rem;
}
.plug-tg-rounded-none {
  border-radius: 0px;
}
.plug-tg-rounded-t {
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.plug-tg-border {
  border-width: 1px;
}
.plug-tg-border-0 {
  border-width: 0px;
}
.plug-tg-border-t {
  border-top-width: 1px;
}
.plug-tg-border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.plug-tg-border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));
}
.plug-tg-border-red-700 {
  --tw-border-opacity: 1;
  border-color: rgb(185 28 28 / var(--tw-border-opacity, 1));
}
.plug-tg-bg-\[var\(--background-modifier-form-field\)\] {
  background-color: var(--background-modifier-form-field);
}
.plug-tg-bg-\[var\(--background-primary\)\] {
  background-color: var(--background-primary);
}
.plug-tg-bg-\[var\(--interactive-accent\)\] {
  background-color: var(--interactive-accent);
}
.plug-tg-bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.plug-tg-bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.plug-tg-bg-gray-100\/10 {
  background-color: rgb(243 244 246 / 0.1);
}
.plug-tg-bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.plug-tg-bg-gray-200\/25 {
  background-color: rgb(229 231 235 / 0.25);
}
.plug-tg-bg-gray-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));
}
.plug-tg-bg-gray-400\/10 {
  background-color: rgb(156 163 175 / 0.1);
}
.plug-tg-bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}
.plug-tg-bg-primary {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--p) / var(--tw-bg-opacity, 1));
}
.plug-tg-bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}
.plug-tg-bg-red-300 {
  --tw-bg-opacity: 1;
  background-color: rgb(252 165 165 / var(--tw-bg-opacity, 1));
}
.plug-tg-bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}
.plug-tg-bg-transparent {
  background-color: transparent;
}
.plug-tg-bg-none {
  background-image: none;
}
.plug-tg-p-0 {
  padding: 0px;
}
.plug-tg-p-1 {
  padding: 0.25rem;
}
.plug-tg-p-2 {
  padding: 0.5rem;
}
.plug-tg-p-3 {
  padding: 0.75rem;
}
.plug-tg-p-4 {
  padding: 1rem;
}
.plug-tg-p-\[3px\] {
  padding: 3px;
}
.plug-tg-px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.plug-tg-px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.plug-tg-px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.plug-tg-px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.plug-tg-py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.plug-tg-py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.plug-tg-py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.plug-tg-py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.plug-tg-py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.plug-tg-py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.plug-tg-pb-2 {
  padding-bottom: 0.5rem;
}
.plug-tg-pb-3 {
  padding-bottom: 0.75rem;
}
.plug-tg-pb-8 {
  padding-bottom: 2rem;
}
.plug-tg-pl-0 {
  padding-left: 0px;
}
.plug-tg-pr-0 {
  padding-right: 0px;
}
.plug-tg-pr-2 {
  padding-right: 0.5rem;
}
.plug-tg-pr-3 {
  padding-right: 0.75rem;
}
.plug-tg-pr-9 {
  padding-right: 2.25rem;
}
.plug-tg-text-left {
  text-align: left;
}
.plug-tg-text-center {
  text-align: center;
}
.plug-tg-font-sans {
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}
.plug-tg-text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.plug-tg-text-\[16px\] {
  font-size: 16px;
}
.plug-tg-text-\[8px\] {
  font-size: 8px;
}
.plug-tg-text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}
.plug-tg-text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.plug-tg-text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.plug-tg-text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.plug-tg-text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.plug-tg-font-bold {
  font-weight: 700;
}
.plug-tg-font-medium {
  font-weight: 500;
}
.plug-tg-font-normal {
  font-weight: 400;
}
.plug-tg-font-semibold {
  font-weight: 600;
}
.plug-tg-font-thin {
  font-weight: 100;
}
.plug-tg-uppercase {
  text-transform: uppercase;
}
.plug-tg-lowercase {
  text-transform: lowercase;
}
.plug-tg-leading-tight {
  line-height: 1.25;
}
.plug-tg-tracking-wider {
  letter-spacing: 0.05em;
}
.plug-tg-text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}
.plug-tg-text-blue-300 {
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}
.plug-tg-text-blue-700 {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}
.plug-tg-text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}
.plug-tg-text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.plug-tg-text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.plug-tg-text-primary {
  --tw-text-opacity: 1;
  color: hsl(var(--p) / var(--tw-text-opacity, 1));
}
.plug-tg-text-red-300 {
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}
.plug-tg-text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.plug-tg-text-red-950 {
  --tw-text-opacity: 1;
  color: rgb(69 10 10 / var(--tw-text-opacity, 1));
}
.plug-tg-text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.plug-tg-opacity-100 {
  opacity: 1;
}
.plug-tg-opacity-40 {
  opacity: 0.4;
}
.plug-tg-opacity-50 {
  opacity: 0.5;
}
.plug-tg-opacity-60 {
  opacity: 0.6;
}
.plug-tg-opacity-70 {
  opacity: 0.7;
}
.plug-tg-opacity-95 {
  opacity: 0.95;
}
.plug-tg-shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.plug-tg-shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.plug-tg-outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.plug-tg-outline {
  outline-style: solid;
}
.plug-tg-outline-1 {
  outline-width: 1px;
}
.plug-tg-outline-2 {
  outline-width: 2px;
}
.plug-tg-outline-red-400 {
  outline-color: #f87171;
}
.plug-tg-ring-1 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.plug-tg-transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.plug-tg-duration-700 {
  transition-duration: 700ms;
}
@keyframes enter {
  from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}
@keyframes exit {
  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}
.plug-tg-duration-700 {
  animation-duration: 700ms;
}
:root,
[data-theme] {
  background-color: inherit;
  color: inherit;
}
.path {
  font-size: xx-small;
  margin-left: 25px;
}
.plug-tg-packageManager {
  /* @apply plug-tg-w-[800px]; */
}
.plug-tg-upsidedown {
  transform: rotate(-90deg);
}
/* .model {
	width: auto;
} */
.plug-tg-update {
  background-color: var(--background-modifier-success) !important;
}
/* --- Loading spinner --- */
#plug-tg-loading {
  display: inline-block;
  overflow: hidden;
  height: 1.3em;
  margin-top: -0.3em;
  line-height: 1.5em;
  vertical-align: text-bottom;
}
#plug-tg-loading::after {
  content: "⠋\A⠙\A⠹\A⠸\A⠼\A⠴\A⠦\A⠧\A⠇\A⠏";
  display: inline-table;
  text-align: left;
  animation: spin10 1s steps(10) infinite;
}
@keyframes spin10 {
  to {
    transform: translateY(-15em);
  }
}
/* ---  --- */
.cm-embed-block:hover>.plug-tg-tgmenu {
  opacity: 1;
}
.plug-tg-tgmenu {
  opacity: 0;
  display: flex;
  gap: 5px;
  position: absolute;
  bottom: 0;
  right: 0;
  z-index: 100;
}
.block-language-tg {
  padding: 10px;
  min-height: 60px;
}
/* TG BLOCK */
.callout[data-callout="ai"] {
  --callout-icon: bot;
}
.callout[data-callout="ai"]>.callout-title {
  margin-left: -1rem;
}
.callout[data-callout="ai"]>.callout-content {
  margin-left: 2rem;
}
.plug-tg-summary {
  padding: 10px;
  font-family: Arial;
  display: flex;
  flex-direction: column;
  background-color: var(--background-primary);
}
.plug-tg-summary table {
  width: 100%;
}
.plug-tg-summary td {
  color: var(--text-normal);
}
.plug-tg-summary .price {
  background-color: var(--text-highlight-bg);
  color: var(--text-normal);
  font-size: 18px;
}
/* -------------------- React json input css ---------------------------- */
.mantine-1fzet7j {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}
@media (prefers-color-scheme: dark) {
  .mantine-1fzet7j {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  }
}
.mantine-hgcj3k {
  background-color: var(--background-modifier-form-field);
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity, 1));
}
@media (prefers-color-scheme: dark) {
  .mantine-hgcj3k {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  }
}
/* -------------------- React confirm css ---------------------------- */
body.react-confirm-alert-body-element {
  overflow: hidden;
}
.react-confirm-alert-blur {
  filter: url(#gaussian-blur);
  filter: blur(2px);
  -webkit-filter: blur(2px);
}
.react-confirm-alert-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99;
  background: rgba(0, 0, 0, 0.7);
  display: -moz-flex;
  display: -ms-flex;
  display: -o-flex;
  display: flex;
  justify-content: center;
  -ms-align-items: center;
  align-items: center;
  opacity: 0;
  animation: react-confirm-alert-fadeIn 0.5s 0.2s forwards;
}
.react-confirm-alert-body {
  font-family: Arial, Helvetica, sans-serif;
  width: 400px;
  padding: 30px;
  text-align: left;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 20px 75px rgba(0, 0, 0, 0.13);
  color: #666;
}
.react-confirm-alert-svg {
  position: absolute;
  top: 0;
  left: 0;
}
.react-confirm-alert-body>h1 {
  margin-top: 0;
}
.react-confirm-alert-body>h3 {
  margin: 0;
  font-size: 16px;
}
.react-confirm-alert-button-group {
  display: -moz-flex;
  display: -ms-flex;
  display: -o-flex;
  display: flex;
  justify-content: flex-start;
  margin-top: 20px;
}
.react-confirm-alert-button-group>button {
  outline: none;
  background: #333;
  border: none;
  display: inline-block;
  padding: 6px 18px;
  color: #eee;
  margin-right: 10px;
  border-radius: 5px;
  font-size: 12px;
  cursor: pointer;
}
@keyframes react-confirm-alert-fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}
/* confettie */
.plug-tg-confetti {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  width: 100%;
  height: 300px;
  overflow: hidden;
}
.plug-tg-confetti-piece {
  position: absolute;
  width: 8px;
  height: 16px;
  background: #ffd300;
  top: 0;
  opacity: 0;
}
.plug-tg-confetti-piece:nth-child(1) {
  left: 7%;
  transform: rotate(-79deg);
  animation: makeItRain 5000ms infinite ease-out;
  animation-delay: 1s;
  animation-duration: 2s;
}
.plug-tg-confetti-piece:nth-child(2) {
  left: 14%;
  transform: rotate(-42deg);
  animation: makeItRain 5000ms infinite ease-out;
  animation-delay: 2s;
  animation-duration: 2.5s;
}
.plug-tg-confetti-piece:nth-child(3) {
  left: 21%;
  transform: rotate(-25deg);
  animation: makeItRain 5000ms infinite ease-out;
  animation-delay: 3s;
  animation-duration: 3.5s;
}
.plug-tg-confetti-piece:nth-child(4) {
  left: 28%;
  transform: rotate(-10deg);
  animation: makeItRain 5000ms infinite ease-out;
  animation-delay: 4s;
  animation-duration: 3.5s;
}
.plug-tg-confetti-piece:nth-child(5) {
  left: 35%;
  transform: rotate(24deg);
  animation: makeItRain 5000ms infinite ease-out;
  animation-delay: 5s;
  animation-duration: 3.5s;
}
.plug-tg-confetti-piece:nth-child(6) {
  left: 42%;
  transform: rotate(-9deg);
  animation: makeItRain 5000ms infinite ease-out;
  animation-delay: 1s;
  animation-duration: 2s;
}
.plug-tg-confetti-piece:nth-child(7) {
  left: 49%;
  transform: rotate(-28deg);
  animation: makeItRain 5000ms infinite ease-out;
  animation-delay: 2s;
  animation-duration: 2.5s;
}
.plug-tg-confetti-piece:nth-child(8) {
  left: 56%;
  transform: rotate(-49deg);
  animation: makeItRain 5000ms infinite ease-out;
  animation-delay: 3s;
  animation-duration: 3.5s;
}
.plug-tg-confetti-piece:nth-child(9) {
  left: 63%;
  transform: rotate(-11deg);
  animation: makeItRain 5000ms infinite ease-out;
  animation-delay: 4s;
  animation-duration: 3s;
}
.plug-tg-confetti-piece:nth-child(10) {
  left: 70%;
  transform: rotate(63deg);
  animation: makeItRain 5000ms infinite ease-out;
  animation-delay: 5s;
  animation-duration: 3.5s;
}
.plug-tg-confetti-piece:nth-child(11) {
  left: 77%;
  transform: rotate(27deg);
  animation: makeItRain 5000ms infinite ease-out;
  animation-delay: 1s;
  animation-duration: 2s;
}
.plug-tg-confetti-piece:nth-child(12) {
  left: 84%;
  transform: rotate(24deg);
  animation: makeItRain 5000ms infinite ease-out;
  animation-delay: 2s;
  animation-duration: 2.5s;
}
.plug-tg-confetti-piece:nth-child(13) {
  left: 91%;
  transform: rotate(54deg);
  animation: makeItRain 5000ms infinite ease-out;
  animation-delay: 3s;
  animation-duration: 3s;
}
.plug-tg-confetti-piece:nth-child(odd) {
  background: #17d3ff;
}
.plug-tg-confetti-piece:nth-child(even) {
  z-index: 1;
}
.plug-tg-confetti-piece:nth-child(4n) {
  width: 5px;
  height: 12px;
  animation-duration: 5000ms;
}
.plug-tg-confetti-piece:nth-child(3n) {
  width: 3px;
  height: 10px;
  animation-duration: 5000ms;
  animation-delay: 2000ms;
}
.plug-tg-confetti-piece:nth-child(4n-7) {
  background: #ff4e91;
}
@keyframes makeItRain {
  from {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  to {
    transform: translateY(300px);
  }
}
.focus-within\:plug-tg-ring-red-300:focus-within {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(252 165 165 / var(--tw-ring-opacity, 1));
}
.hover\:plug-tg-bg-blue-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}
.hover\:plug-tg-bg-blue-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}
.hover\:plug-tg-bg-gray-100\/10:hover {
  background-color: rgb(243 244 246 / 0.1);
}
.hover\:plug-tg-bg-gray-500:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}
.hover\:plug-tg-bg-green-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}
.hover\:plug-tg-bg-primary\/90:hover {
  background-color: hsl(var(--p) / 0.9);
}
.hover\:plug-tg-bg-red-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}
.hover\:plug-tg-bg-red-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));
}
.hover\:plug-tg-text-blue-300:hover {
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}
.hover\:plug-tg-opacity-100:hover {
  opacity: 1;
}
.focus\:plug-tg-border-blue-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));
}
.focus\:plug-tg-border-primary:focus {
  --tw-border-opacity: 1;
  border-color: hsl(var(--p) / var(--tw-border-opacity, 1));
}
.focus\:plug-tg-outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus\:plug-tg-ring-0:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:plug-tg-ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:plug-tg-ring-4:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:plug-tg-ring-blue-300\/50:focus {
  --tw-ring-color: rgb(147 197 253 / 0.5);
}
.focus\:plug-tg-ring-green-300\/50:focus {
  --tw-ring-color: rgb(134 239 172 / 0.5);
}
.plug-tg-group:hover .group-hover\:plug-tg-block {
  display: block;
}
@media (min-width: 640px) {
  .sm\:plug-tg-py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }
}
@media (min-width: 768px) {
  .md\:plug-tg-flex-row {
    flex-direction: row;
  }
  .md\:plug-tg-text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
  }
}
@media (min-width: 1024px) {
  .lg\:plug-tg-w-1\/4 {
    width: 25%;
  }
  .lg\:plug-tg-w-3\/4 {
    width: 75%;
  }
  .lg\:plug-tg-w-auto {
    width: auto;
  }
}
@media (prefers-color-scheme: dark) {
  .dark\:plug-tg-divide-gray-700 > :not([hidden]) ~ :not([hidden]) {
    --tw-divide-opacity: 1;
    border-color: rgb(55 65 81 / var(--tw-divide-opacity, 1));
  }
  .dark\:plug-tg-bg-gray-300\/25 {
    background-color: rgb(209 213 219 / 0.25);
  }
  .dark\:plug-tg-bg-gray-300\/30 {
    background-color: rgb(209 213 219 / 0.3);
  }
  .dark\:plug-tg-text-blue-300 {
    --tw-text-opacity: 1;
    color: rgb(147 197 253 / var(--tw-text-opacity, 1));
  }
  .dark\:plug-tg-text-white {
    --tw-text-opacity: 1;
    color: rgb(255 255 255 / var(--tw-text-opacity, 1));
  }
}