"use strict";var V=Object.defineProperty;var q=(t,e,n)=>e in t?V(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var L=(t,e,n)=>q(t,typeof e!="symbol"?e+"":e,n);Object.defineProperties(exports,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}});const obsidian=require("obsidian"),equalFn=(t,e)=>t===e,$PROXY=Symbol("solid-proxy"),$TRACK=Symbol("solid-track"),signalOptions={equals:equalFn};let runEffects=runQueue;const STALE=1,PENDING=2,UNOWNED={owned:null,cleanups:null,context:null,owner:null};var Owner=null;let Transition=null,ExternalSourceConfig=null,Listener=null,Updates=null,Effects=null,ExecCount=0;function createRoot(t,e){const n=Listener,o=Owner,s=t.length===0,i=e===void 0?o:e,a=s?UNOWNED:{owned:null,cleanups:null,context:i?i.context:null,owner:i},c=s?t:()=>t(()=>untrack(()=>cleanNode(a)));Owner=a,Listener=null;try{return runUpdates(c,!0)}finally{Listener=n,Owner=o}}function createSignal(t,e){e=e?Object.assign({},signalOptions,e):signalOptions;const n={value:t,observers:null,observerSlots:null,comparator:e.equals||void 0},o=s=>(typeof s=="function"&&(s=s(n.value)),writeSignal(n,s));return[readSignal.bind(n),o]}function createRenderEffect(t,e,n){const o=createComputation(t,e,!1,STALE);updateComputation(o)}function createEffect(t,e,n){runEffects=runUserEffects;const o=createComputation(t,e,!1,STALE);o.user=!0,Effects?Effects.push(o):updateComputation(o)}function createMemo(t,e,n){n=n?Object.assign({},signalOptions,n):signalOptions;const o=createComputation(t,e,!0,0);return o.observers=null,o.observerSlots=null,o.comparator=n.equals||void 0,updateComputation(o),readSignal.bind(o)}function batch(t){return runUpdates(t,!1)}function untrack(t){if(Listener===null)return t();const e=Listener;Listener=null;try{return t()}finally{Listener=e}}function onMount(t){createEffect(()=>untrack(t))}function onCleanup(t){return Owner===null||(Owner.cleanups===null?Owner.cleanups=[t]:Owner.cleanups.push(t)),t}function getListener(){return Listener}function readSignal(){if(this.sources&&this.state)if(this.state===STALE)updateComputation(this);else{const t=Updates;Updates=null,runUpdates(()=>lookUpstream(this),!1),Updates=t}if(Listener){const t=this.observers?this.observers.length:0;Listener.sources?(Listener.sources.push(this),Listener.sourceSlots.push(t)):(Listener.sources=[this],Listener.sourceSlots=[t]),this.observers?(this.observers.push(Listener),this.observerSlots.push(Listener.sources.length-1)):(this.observers=[Listener],this.observerSlots=[Listener.sources.length-1])}return this.value}function writeSignal(t,e,n){let o=t.value;return(!t.comparator||!t.comparator(o,e))&&(t.value=e,t.observers&&t.observers.length&&runUpdates(()=>{for(let s=0;s<t.observers.length;s+=1){const i=t.observers[s],a=Transition&&Transition.running;a&&Transition.disposed.has(i),(a?!i.tState:!i.state)&&(i.pure?Updates.push(i):Effects.push(i),i.observers&&markDownstream(i)),a||(i.state=STALE)}if(Updates.length>1e6)throw Updates=[],new Error},!1)),e}function updateComputation(t){if(!t.fn)return;cleanNode(t);const e=ExecCount;runComputation(t,t.value,e)}function runComputation(t,e,n){let o;const s=Owner,i=Listener;Listener=Owner=t;try{o=t.fn(e)}catch(a){return t.pure&&(t.state=STALE,t.owned&&t.owned.forEach(cleanNode),t.owned=null),t.updatedAt=n+1,handleError(a)}finally{Listener=i,Owner=s}(!t.updatedAt||t.updatedAt<=n)&&(t.updatedAt!=null&&"observers"in t?writeSignal(t,o):t.value=o,t.updatedAt=n)}function createComputation(t,e,n,o=STALE,s){const i={fn:t,state:o,updatedAt:null,owned:null,sources:null,sourceSlots:null,cleanups:null,value:e,owner:Owner,context:Owner?Owner.context:null,pure:n};return Owner===null||Owner!==UNOWNED&&(Owner.owned?Owner.owned.push(i):Owner.owned=[i]),i}function runTop(t){if(t.state===0)return;if(t.state===PENDING)return lookUpstream(t);if(t.suspense&&untrack(t.suspense.inFallback))return t.suspense.effects.push(t);const e=[t];for(;(t=t.owner)&&(!t.updatedAt||t.updatedAt<ExecCount);)t.state&&e.push(t);for(let n=e.length-1;n>=0;n--)if(t=e[n],t.state===STALE)updateComputation(t);else if(t.state===PENDING){const o=Updates;Updates=null,runUpdates(()=>lookUpstream(t,e[0]),!1),Updates=o}}function runUpdates(t,e){if(Updates)return t();let n=!1;e||(Updates=[]),Effects?n=!0:Effects=[],ExecCount++;try{const o=t();return completeUpdates(n),o}catch(o){n||(Effects=null),Updates=null,handleError(o)}}function completeUpdates(t){if(Updates&&(runQueue(Updates),Updates=null),t)return;const e=Effects;Effects=null,e.length&&runUpdates(()=>runEffects(e),!1)}function runQueue(t){for(let e=0;e<t.length;e++)runTop(t[e])}function runUserEffects(t){let e,n=0;for(e=0;e<t.length;e++){const o=t[e];o.user?t[n++]=o:runTop(o)}for(e=0;e<n;e++)runTop(t[e])}function lookUpstream(t,e){t.state=0;for(let n=0;n<t.sources.length;n+=1){const o=t.sources[n];if(o.sources){const s=o.state;s===STALE?o!==e&&(!o.updatedAt||o.updatedAt<ExecCount)&&runTop(o):s===PENDING&&lookUpstream(o,e)}}}function markDownstream(t){for(let e=0;e<t.observers.length;e+=1){const n=t.observers[e];n.state||(n.state=PENDING,n.pure?Updates.push(n):Effects.push(n),n.observers&&markDownstream(n))}}function cleanNode(t){let e;if(t.sources)for(;t.sources.length;){const n=t.sources.pop(),o=t.sourceSlots.pop(),s=n.observers;if(s&&s.length){const i=s.pop(),a=n.observerSlots.pop();o<s.length&&(i.sourceSlots[a]=o,s[o]=i,n.observerSlots[o]=a)}}if(t.owned){for(e=t.owned.length-1;e>=0;e--)cleanNode(t.owned[e]);t.owned=null}if(t.cleanups){for(e=t.cleanups.length-1;e>=0;e--)t.cleanups[e]();t.cleanups=null}t.state=0}function castError(t){return t instanceof Error?t:new Error(typeof t=="string"?t:"Unknown error",{cause:t})}function handleError(t,e=Owner){throw castError(t)}const FALLBACK=Symbol("fallback");function dispose(t){for(let e=0;e<t.length;e++)t[e]()}function mapArray(t,e,n={}){let o=[],s=[],i=[],a=0,c=e.length>1?[]:null;return onCleanup(()=>dispose(i)),()=>{let m=t()||[],l=m.length,f,d;return m[$TRACK],untrack(()=>{let C,_,S,A,T,u,w,b,E;if(l===0)a!==0&&(dispose(i),i=[],o=[],s=[],a=0,c&&(c=[])),n.fallback&&(o=[FALLBACK],s[0]=createRoot(x=>(i[0]=x,n.fallback())),a=1);else if(a===0){for(s=new Array(l),d=0;d<l;d++)o[d]=m[d],s[d]=createRoot($);a=l}else{for(S=new Array(l),A=new Array(l),c&&(T=new Array(l)),u=0,w=Math.min(a,l);u<w&&o[u]===m[u];u++);for(w=a-1,b=l-1;w>=u&&b>=u&&o[w]===m[b];w--,b--)S[b]=s[w],A[b]=i[w],c&&(T[b]=c[w]);for(C=new Map,_=new Array(b+1),d=b;d>=u;d--)E=m[d],f=C.get(E),_[d]=f===void 0?-1:f,C.set(E,d);for(f=u;f<=w;f++)E=o[f],d=C.get(E),d!==void 0&&d!==-1?(S[d]=s[f],A[d]=i[f],c&&(T[d]=c[f]),d=_[d],C.set(E,d)):i[f]();for(d=u;d<l;d++)d in S?(s[d]=S[d],i[d]=A[d],c&&(c[d]=T[d],c[d](d))):s[d]=createRoot($);s=s.slice(0,a=l),o=m.slice(0)}return s});function $(C){if(i[d]=C,c){const[_,S]=createSignal(d);return c[d]=S,e(m[d],_)}return e(m[d])}}}function createComponent(t,e){return untrack(()=>t(e||{}))}const narrowedError=t=>`Stale read from <${t}>.`;function For(t){const e="fallback"in t&&{fallback:()=>t.fallback};return createMemo(mapArray(()=>t.each,t.children,e||void 0))}function Show(t){const e=t.keyed,n=createMemo(()=>t.when,void 0,{equals:(o,s)=>e?o===s:!o==!s});return createMemo(()=>{const o=n();if(o){const s=t.children;return typeof s=="function"&&s.length>0?untrack(()=>s(e?o:()=>{if(!untrack(n))throw narrowedError("Show");return t.when})):s}return t.fallback},void 0,void 0)}function reconcileArrays(t,e,n){let o=n.length,s=e.length,i=o,a=0,c=0,m=e[s-1].nextSibling,l=null;for(;a<s||c<i;){if(e[a]===n[c]){a++,c++;continue}for(;e[s-1]===n[i-1];)s--,i--;if(s===a){const f=i<o?c?n[c-1].nextSibling:n[i-c]:m;for(;c<i;)t.insertBefore(n[c++],f)}else if(i===c)for(;a<s;)(!l||!l.has(e[a]))&&e[a].remove(),a++;else if(e[a]===n[i-1]&&n[c]===e[s-1]){const f=e[--s].nextSibling;t.insertBefore(n[c++],e[a++].nextSibling),t.insertBefore(n[--i],f),e[s]=n[i]}else{if(!l){l=new Map;let d=c;for(;d<i;)l.set(n[d],d++)}const f=l.get(e[a]);if(f!=null)if(c<f&&f<i){let d=a,$=1,C;for(;++d<s&&d<i&&!((C=l.get(e[d]))==null||C!==f+$);)$++;if($>f-c){const _=e[a];for(;c<f;)t.insertBefore(n[c++],_)}else t.replaceChild(n[c++],e[a++])}else a++;else e[a++].remove()}}}const $$EVENTS="_$DX_DELEGATE";function render(t,e,n,o={}){let s;return createRoot(i=>{s=i,e===document?t():insert(e,t(),e.firstChild?null:void 0,n)},o.owner),()=>{s(),e.textContent=""}}function template(t,e,n){let o;const s=()=>{const a=document.createElement("template");return a.innerHTML=t,a.content.firstChild},i=()=>(o||(o=s())).cloneNode(!0);return i.cloneNode=i,i}function delegateEvents(t,e=window.document){const n=e[$$EVENTS]||(e[$$EVENTS]=new Set);for(let o=0,s=t.length;o<s;o++){const i=t[o];n.has(i)||(n.add(i),e.addEventListener(i,eventHandler))}}function setAttribute(t,e,n){t.removeAttribute(e)}function className(t,e){e==null?t.removeAttribute("class"):t.className=e}function addEventListener(t,e,n,o){Array.isArray(n)?(t[`$$${e}`]=n[0],t[`$$${e}Data`]=n[1]):t[`$$${e}`]=n}function style(t,e,n){if(!e)return n?setAttribute(t,"style"):e;const o=t.style;if(typeof e=="string")return o.cssText=e;typeof n=="string"&&(o.cssText=n=void 0),n||(n={}),e||(e={});let s,i;for(i in n)e[i]==null&&o.removeProperty(i),delete n[i];for(i in e)s=e[i],s!==n[i]&&(o.setProperty(i,s),n[i]=s);return n}function use(t,e,n){return untrack(()=>t(e,n))}function insert(t,e,n,o){if(n!==void 0&&!o&&(o=[]),typeof e!="function")return insertExpression(t,e,o,n);createRenderEffect(s=>insertExpression(t,e(),s,n),o)}function eventHandler(t){const e=`$$${t.type}`;let n=t.composedPath&&t.composedPath()[0]||t.target;for(t.target!==n&&Object.defineProperty(t,"target",{configurable:!0,value:n}),Object.defineProperty(t,"currentTarget",{configurable:!0,get(){return n||document}});n;){const o=n[e];if(o&&!n.disabled){const s=n[`${e}Data`];if(s!==void 0?o.call(n,s,t):o.call(n,t),t.cancelBubble)return}n=n._$host||n.parentNode||n.host}}function insertExpression(t,e,n,o,s){for(;typeof n=="function";)n=n();if(e===n)return n;const i=typeof e,a=o!==void 0;if(t=a&&n[0]&&n[0].parentNode||t,i==="string"||i==="number"){if(i==="number"&&(e=e.toString(),e===n))return n;if(a){let c=n[0];c&&c.nodeType===3?c.data!==e&&(c.data=e):c=document.createTextNode(e),n=cleanChildren(t,n,o,c)}else n!==""&&typeof n=="string"?n=t.firstChild.data=e:n=t.textContent=e}else if(e==null||i==="boolean")n=cleanChildren(t,n,o);else{if(i==="function")return createRenderEffect(()=>{let c=e();for(;typeof c=="function";)c=c();n=insertExpression(t,c,n,o)}),()=>n;if(Array.isArray(e)){const c=[],m=n&&Array.isArray(n);if(normalizeIncomingArray(c,e,n,s))return createRenderEffect(()=>n=insertExpression(t,c,n,o,!0)),()=>n;if(c.length===0){if(n=cleanChildren(t,n,o),a)return n}else m?n.length===0?appendNodes(t,c,o):reconcileArrays(t,n,c):(n&&cleanChildren(t),appendNodes(t,c));n=c}else if(e.nodeType){if(Array.isArray(n)){if(a)return n=cleanChildren(t,n,o,e);cleanChildren(t,n,null,e)}else n==null||n===""||!t.firstChild?t.appendChild(e):t.replaceChild(e,t.firstChild);n=e}}return n}function normalizeIncomingArray(t,e,n,o){let s=!1;for(let i=0,a=e.length;i<a;i++){let c=e[i],m=n&&n[t.length],l;if(!(c==null||c===!0||c===!1))if((l=typeof c)=="object"&&c.nodeType)t.push(c);else if(Array.isArray(c))s=normalizeIncomingArray(t,c,m)||s;else if(l==="function")if(o){for(;typeof c=="function";)c=c();s=normalizeIncomingArray(t,Array.isArray(c)?c:[c],Array.isArray(m)?m:[m])||s}else t.push(c),s=!0;else{const f=String(c);m&&m.nodeType===3&&m.data===f?t.push(m):t.push(document.createTextNode(f))}}return s}function appendNodes(t,e,n=null){for(let o=0,s=e.length;o<s;o++)t.insertBefore(e[o],n)}function cleanChildren(t,e,n,o){if(n===void 0)return t.textContent="";const s=o||document.createTextNode("");if(e.length){let i=!1;for(let a=e.length-1;a>=0;a--){const c=e[a];if(s!==c){const m=c.parentNode===t;!i&&!a?m?t.replaceChild(s,c):t.insertBefore(s,n):m&&c.remove()}else i=!0}}else t.insertBefore(s,n);return[s]}const $RAW=Symbol("store-raw"),$NODE=Symbol("store-node"),$HAS=Symbol("store-has"),$SELF=Symbol("store-self");function wrap$1(t){let e=t[$PROXY];if(!e&&(Object.defineProperty(t,$PROXY,{value:e=new Proxy(t,proxyTraps$1)}),!Array.isArray(t))){const n=Object.keys(t),o=Object.getOwnPropertyDescriptors(t);for(let s=0,i=n.length;s<i;s++){const a=n[s];o[a].get&&Object.defineProperty(t,a,{enumerable:o[a].enumerable,get:o[a].get.bind(e)})}}return e}function isWrappable(t){let e;return t!=null&&typeof t=="object"&&(t[$PROXY]||!(e=Object.getPrototypeOf(t))||e===Object.prototype||Array.isArray(t))}function unwrap(t,e=new Set){let n,o,s,i;if(n=t!=null&&t[$RAW])return n;if(!isWrappable(t)||e.has(t))return t;if(Array.isArray(t)){Object.isFrozen(t)?t=t.slice(0):e.add(t);for(let a=0,c=t.length;a<c;a++)s=t[a],(o=unwrap(s,e))!==s&&(t[a]=o)}else{Object.isFrozen(t)?t=Object.assign({},t):e.add(t);const a=Object.keys(t),c=Object.getOwnPropertyDescriptors(t);for(let m=0,l=a.length;m<l;m++)i=a[m],!c[i].get&&(s=t[i],(o=unwrap(s,e))!==s&&(t[i]=o))}return t}function getNodes(t,e){let n=t[e];return n||Object.defineProperty(t,e,{value:n=Object.create(null)}),n}function getNode(t,e,n){if(t[e])return t[e];const[o,s]=createSignal(n,{equals:!1,internal:!0});return o.$=s,t[e]=o}function proxyDescriptor$1(t,e){const n=Reflect.getOwnPropertyDescriptor(t,e);return!n||n.get||!n.configurable||e===$PROXY||e===$NODE||(delete n.value,delete n.writable,n.get=()=>t[$PROXY][e]),n}function trackSelf(t){getListener()&&getNode(getNodes(t,$NODE),$SELF)()}function ownKeys(t){return trackSelf(t),Reflect.ownKeys(t)}const proxyTraps$1={get(t,e,n){if(e===$RAW)return t;if(e===$PROXY)return n;if(e===$TRACK)return trackSelf(t),n;const o=getNodes(t,$NODE),s=o[e];let i=s?s():t[e];if(e===$NODE||e===$HAS||e==="__proto__")return i;if(!s){const a=Object.getOwnPropertyDescriptor(t,e);getListener()&&(typeof i!="function"||t.hasOwnProperty(e))&&!(a&&a.get)&&(i=getNode(o,e,i)())}return isWrappable(i)?wrap$1(i):i},has(t,e){return e===$RAW||e===$PROXY||e===$TRACK||e===$NODE||e===$HAS||e==="__proto__"?!0:(getListener()&&getNode(getNodes(t,$HAS),e)(),e in t)},set(){return!0},deleteProperty(){return!0},ownKeys,getOwnPropertyDescriptor:proxyDescriptor$1};function setProperty(t,e,n,o=!1){if(!o&&t[e]===n)return;const s=t[e],i=t.length;n===void 0?(delete t[e],t[$HAS]&&t[$HAS][e]&&s!==void 0&&t[$HAS][e].$()):(t[e]=n,t[$HAS]&&t[$HAS][e]&&s===void 0&&t[$HAS][e].$());let a=getNodes(t,$NODE),c;if((c=getNode(a,e,s))&&c.$(()=>n),Array.isArray(t)&&t.length!==i){for(let m=t.length;m<i;m++)(c=a[m])&&c.$();(c=getNode(a,"length",i))&&c.$(t.length)}(c=a[$SELF])&&c.$()}function mergeStoreNode(t,e){const n=Object.keys(e);for(let o=0;o<n.length;o+=1){const s=n[o];setProperty(t,s,e[s])}}function updateArray(t,e){if(typeof e=="function"&&(e=e(t)),e=unwrap(e),Array.isArray(e)){if(t===e)return;let n=0,o=e.length;for(;n<o;n++){const s=e[n];t[n]!==s&&setProperty(t,n,s)}setProperty(t,"length",o)}else mergeStoreNode(t,e)}function updatePath(t,e,n=[]){let o,s=t;if(e.length>1){o=e.shift();const a=typeof o,c=Array.isArray(t);if(Array.isArray(o)){for(let m=0;m<o.length;m++)updatePath(t,[o[m]].concat(e),n);return}else if(c&&a==="function"){for(let m=0;m<t.length;m++)o(t[m],m)&&updatePath(t,[m].concat(e),n);return}else if(c&&a==="object"){const{from:m=0,to:l=t.length-1,by:f=1}=o;for(let d=m;d<=l;d+=f)updatePath(t,[d].concat(e),n);return}else if(e.length>1){updatePath(t[o],e,[o].concat(n));return}s=t[o],n=[o].concat(n)}let i=e[0];typeof i=="function"&&(i=i(s,n),i===s)||o===void 0&&i==null||(i=unwrap(i),o===void 0||isWrappable(s)&&isWrappable(i)&&!Array.isArray(i)?mergeStoreNode(s,i):setProperty(t,o,i))}function createStore(...[t,e]){const n=unwrap(t||{}),o=Array.isArray(n),s=wrap$1(n);function i(...a){batch(()=>{o&&a.length===1?updateArray(n,a[0]):updatePath(n,a)})}return[s,i]}class PluginSolidSettingTab extends obsidian.PluginSettingTab{constructor(n,o,s){super(n.app,n);L(this,"cleanup");this.component=o,this.props=s}display(){this.cleanup=render(()=>this.component(this.props),this.containerEl)}hide(){var n;(n=this.cleanup)==null||n.call(this)}}const name="Code Emitter",version="0.4.0",ClientAgent=`Obsidian ${name}/${version} (Just for programming learning, please let me know if it is not allowed.)`,url$4="https://api.kotlinlang.org//api/1.7.10/compiler/run";async function kotlin(t,e){const s=await(await fetch(url$4,{method:"POST",headers:{"User-Agent":ClientAgent,"Client-Agent":ClientAgent,"content-type":"application/json; charset=utf-8"},body:JSON.stringify({args:"",confType:"java",files:[{name:"File.kt",publicId:"",text:t}]})})).json();if(s.errors&&Object.keys(s.errors).length>0)for(const i of Object.values(s.errors))for(const a of i)e.stderr(a.message);e.stdout(s.text)}const url$3="https://play.rust-lang.org/execute";async function rust(t,e){const s=await(await fetch(url$3,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({channel:"stable",mode:"debug",edition:"2021",crateType:"bin",tests:!1,code:t,backtrace:!1})})).json();s.success?e.stdout(s.stdout):e.stderr(s.stderr)}const url$2="https://api2.sololearn.com/v2/codeplayground/v2/compile",run$1=async(t,e)=>await(await fetch(url$2,{headers:{"User-Agent":ClientAgent,"Client-Agent":ClientAgent,Accept:"application/json, text/plain, */*","Accept-Language":"zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2","Content-Type":"application/json"},body:JSON.stringify({code:t,codeId:null,input:"",language:e}),method:"POST"})).json();async function cpp(t,e){const n=await run$1(t,"cpp");n.success?e.stdout(n.data.output):e.stderr((n.errors??[]).join(`
`))}async function go(t,e){const n=await run$1(t,"go");n.success?e.stdout(n.data.output):e.stderr((n.errors??[]).join(`
`))}const headers={"User-Agent":ClientAgent,"Client-Agent":ClientAgent,Accept:"application/json","Content-Type":"application/json"};let ghcVersion;const getGhcVersion=async()=>(ghcVersion=(await obsidian.requestUrl({url:"https://play.haskell.org/versions",headers})).json.last(),ghcVersion),run=async t=>(await obsidian.requestUrl({url:"https://play.haskell.org/submit",headers,body:JSON.stringify({code:t,opt:"O1",output:"run",version:ghcVersion??getGhcVersion()}),method:"POST"})).json;async function hs(t,e){const n=await run(t);n.ec==0?(e.stdout(n.sout),e.stdout(n.serr)):e.stderr(n.ghcout)}const nativeGlobal=new Function("return this")(),fnRegexCheckCacheMap=new WeakMap;function isConstructable(t){const e=t.prototype&&t.prototype.constructor===t&&Object.getOwnPropertyNames(t.prototype).length>1;if(e)return!0;if(fnRegexCheckCacheMap.has(t))return fnRegexCheckCacheMap.get(t);let n=e;if(!n){const o=t.toString(),s=/^function\b\s[A-Z].*/,i=/^class\b/;n=s.test(o)||i.test(o)}return fnRegexCheckCacheMap.set(t,n),n}const naughtySafari=typeof document.all=="function"&&typeof document.all>"u",callableFnCacheMap=new WeakMap,isCallable=t=>{if(callableFnCacheMap.has(t))return!0;const e=naughtySafari?typeof t=="function"&&typeof t<"u":typeof t=="function";return e&&callableFnCacheMap.set(t,e),e},boundedMap=new WeakMap;function isBoundedFunction(t){if(boundedMap.has(t))return boundedMap.get(t);const e=t.name.indexOf("bound ")===0&&!t.hasOwnProperty("prototype");return boundedMap.set(t,e),e}const functionBoundedValueMap=new WeakMap;function getTargetValue(t,e){if(isCallable(e)&&!isBoundedFunction(e)&&!isConstructable(e)){const n=functionBoundedValueMap.get(e);if(n)return n;const o=Function.prototype.bind.call(e,t);for(const s in e)o[s]=e[s];if(e.hasOwnProperty("prototype")&&!o.hasOwnProperty("prototype")&&Object.defineProperty(o,"prototype",{value:e.prototype,enumerable:!1,writable:!0}),typeof e.toString=="function"){const s=e.hasOwnProperty("toString")&&!o.hasOwnProperty("toString"),i=o.toString===Function.prototype.toString;if(s||i){const a=Object.getOwnPropertyDescriptor(s?e:Function.prototype,"toString");Object.defineProperty(o,"toString",{...a,...a!=null&&a.get?null:{value:()=>e.toString()}})}}return functionBoundedValueMap.set(e,o),o}return e}function uniq(t){return t.filter(function(n){return n in this?!1:this[n]=!0},Object.create(null))}const rawObjectDefineProperty=Object.defineProperty,NODE_ENV=(typeof process<"u"?process.env.NODE_ENV:void 0)??"production",variableWhiteListInDev=NODE_ENV==="development"||window.__QIANKUN_DEVELOPMENT__?["__REACT_ERROR_OVERLAY_GLOBAL_HOOK__"]:[],variableWhiteList=["System","__cjsWrapper",...variableWhiteListInDev],unscopables={undefined:!0,Array:!0,Object:!0,String:!0,Boolean:!0,Math:!0,Number:!0,Symbol:!0,parseFloat:!0,Float32Array:!0,isNaN:!0,Infinity:!0,Reflect:!0,Float64Array:!0,Function:!0,Map:!0,NaN:!0,Promise:!0,Proxy:!0,Set:!0,parseInt:!0,requestAnimationFrame:!0},scopables={console};function makeScope(t,e){const n=scopables[e];t[e]=new Proxy({},{get(o,s,i){return o[s]??n[s]},set(o,s,i,a){return i===n[s]?delete o[e]:o[s]=i,!0}})}const useNativeWindowForBindingsProps=new Map([["fetch",!0],["mockDomAPIInBlackList",NODE_ENV==="test"]]);function createFakeWindow(t){const e=new Map,n={};return Object.getOwnPropertyNames(t).filter(o=>{if(o in scopables)return!0;const s=Object.getOwnPropertyDescriptor(t,o);return!(s!=null&&s.configurable)}).forEach(o=>{if(o in scopables){makeScope(n,o);return}const s=Object.getOwnPropertyDescriptor(t,o);if(s){const i=Object.prototype.hasOwnProperty.call(s,"get");(o==="top"||o==="parent"||o==="self"||o==="window"||NODE_ENV==="test"&&(o==="mockTop"||o==="mockSafariTop"))&&(s.configurable=!0,i||(s.writable=!0)),i&&e.set(o,!0),rawObjectDefineProperty(n,o,Object.freeze(s))}}),{fakeWindow:n,propertiesWithGetter:e}}let activeSandboxCount=0;class ProxySandbox{constructor(e,n=window){L(this,"updatedValueSet",new Set);L(this,"name");L(this,"proxy");L(this,"globalContext");L(this,"sandboxRunning",!0);L(this,"latestSetProp",null);this.name=e,this.globalContext=n;const{updatedValueSet:o}=this,{fakeWindow:s,propertiesWithGetter:i}=createFakeWindow(n),a=new Map,c=l=>s.hasOwnProperty(l)||n.hasOwnProperty(l),m=new Proxy(s,{set:(l,f,d)=>{if(this.sandboxRunning){if(!l.hasOwnProperty(f)&&n.hasOwnProperty(f)){const $=Object.getOwnPropertyDescriptor(n,f),{writable:C,configurable:_,enumerable:S}=$;C&&Object.defineProperty(l,f,{configurable:_,enumerable:S,writable:C,value:d})}else l[f]=d;return variableWhiteList.indexOf(f)!==-1&&(n[f]=d),o.add(f),this.latestSetProp=f,!0}return NODE_ENV==="development"&&console.warn(`[qiankun] Set window.${f.toString()} while sandbox destroyed or inactive in ${e}!`),!0},get:(l,f)=>{if(f===Symbol.unscopables)return unscopables;if(f==="window"||f==="self"||f==="globalThis")return m;if(f==="top"||f==="parent"||NODE_ENV==="test"&&(f==="mockTop"||f==="mockSafariTop"))return n===n.parent?m:n[f];if(f==="hasOwnProperty")return c;if(f==="document")return document;if(f==="eval")return eval;const d=i.has(f)?n[f]:f in l?l[f]:n[f],$=useNativeWindowForBindingsProps.get(f)?nativeGlobal:n;return getTargetValue($,d)},has(l,f){return f in unscopables||f in l||f in n},getOwnPropertyDescriptor(l,f){if(l.hasOwnProperty(f)){const d=Object.getOwnPropertyDescriptor(l,f);return a.set(f,"target"),d}if(n.hasOwnProperty(f)){const d=Object.getOwnPropertyDescriptor(n,f);return a.set(f,"globalContext"),d&&!d.configurable&&(d.configurable=!0),d}},ownKeys(l){return uniq(Reflect.ownKeys(n).concat(Reflect.ownKeys(l)))},defineProperty(l,f,d){switch(a.get(f)){case"globalContext":return Reflect.defineProperty(n,f,d);default:return Reflect.defineProperty(l,f,d)}},deleteProperty:(l,f)=>(l.hasOwnProperty(f)&&(delete l[f],o.delete(f)),!0),getPrototypeOf(){return Reflect.getPrototypeOf(n)}});this.proxy=m,activeSandboxCount++}active(){this.sandboxRunning||activeSandboxCount++,this.sandboxRunning=!0}inactive(){NODE_ENV==="development"&&console.info(`[qiankun:sandbox] ${this.name} modified global properties restore...`,[...this.updatedValueSet.keys()]),--activeSandboxCount===0&&variableWhiteList.forEach(e=>{this.proxy.hasOwnProperty(e)&&delete this.globalContext[e]}),this.sandboxRunning=!1}}async function js(code,output){return new Promise((resolve,reject)=>{const sandbox=new ProxySandbox("t");let run=async function(window){const{console}=window;Object.assign(console,wrapConsole(output)),sandbox.active();try{await eval(code)}catch(t){console.error(t)}finally{sandbox.inactive()}};run=run.bind(sandbox.proxy),run(sandbox.proxy).then(resolve).catch(reject)})}const wrapConsole=({update:t})=>{const e=(c,m)=>{const l=`<div class="log-${c}">${m.join(",")}</div>`;t(f=>[...f,l])};return{log:(...c)=>e("info",c),info:(...c)=>e("info",c),debug:(...c)=>e("debug",c),warn:(...c)=>e("warn",c),error:(...c)=>e("error",c)}};function urlImport(t,e){return new Promise((n,o)=>{document.head.querySelector(`script[src="${t}"]`)&&n(e?e():void 0);const s=document.createElement("script");s.src=t,s.onload=function(){n(e?e():void 0)},document.head.append(s)})}const cdn$1="https://cdn.jsdelivr.net/npm/typescript@4.7.4/lib/typescript.min.js",ts=function(){let t=null,e=null;const n=async function(o,s){t||await e();const i=t.transpile(`(async () => { ${o} })();`,{module:t.ModuleKind.ESNext,target:t.ScriptTarget.ES2018});await js(i,s)};return n.loading=!0,e=async()=>{t=await urlImport(cdn$1,()=>window.ts),n.loading=!1,console.log("typescript loaded.")},n}();async function java(t,e){const n=await run$1(t,"java");n.success?e.stdout(n.data.output):e.stderr((n.errors??[]).join(`
`))}typeof process<"u"&&typeof process.browser>"u"&&(process.browser=!0);const default_cdn="https://cdn.jsdelivr.net/pyodide/v0.26.2/full/",setMplTarget=t=>{t?document.pyodideMplTarget=t:delete document.pyodideMplTarget};let cache=null;const python=function(t){const e=default_cdn;if((cache==null?void 0:cache.cdn)===e)return cache.backend;let n=null,o=null,s=null;const i=async(a,c)=>{n||await s(),o=c;try{setMplTarget(c.viewEl),await n.runPythonAsync(a)}catch(m){c.stderr(m)}finally{setMplTarget(void 0)}};return i.loading=!0,s=async()=>{n=await(await urlImport(`${e}pyodide.js`,()=>window.loadPyodide))({indexURL:e,stdout:c=>o==null?void 0:o.stdout(c),stderr:c=>o==null?void 0:o.stderr(c)}),await n.loadPackage("micropip"),console.log("python loaded."),i.loading=!1},cache={cdn:e,backend:i},i}();async function csharp(t,e){const n=await run$1(t,"cs");n.success?e.stdout(n.data.output):e.stderr((n.errors??[]).join(`
`))}async function swift(t,e){const n=await run$1(t,"swift");n.success?e.stdout(n.data.output):e.stderr((n.errors??[]).join(`
`))}const url$1="https://play.vosca.dev/run";async function v(t,e){var i;const n=new FormData;n.append("code",t);const s=await(await fetch(url$1,{method:"POST",body:n})).json();((i=s.error)==null?void 0:i.length)>0?e.stderr(s.error):e.stdout(s.output)}const cdn="https://unpkg.com/@wenyan/core/index.min.js",wy=function(){let t=null,e=null;const n=async function(o,s){t||await e();const i=t.compile(o);console.log("wenyan:"),console.log(i),await js(`(async () => { ${i} })();`,s)};return n.loading=!0,e=async()=>{t=await urlImport(cdn,()=>window.Wenyan),n.loading=!1,console.log("wenyan loaded.")},n}(),url="https://play.crystal-lang.org/run_requests";async function crystal(t,e){const i=(await(await fetch(url,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({run_request:{language:"crystal",version:"1.8.2",code:t}})})).json()).run_request.run;i.stderr?e.stderr(i.stderr):e.stdout(i.stdout)}async function r(t,e){const n=await run$1(t,"r");n.success?e.stdout(n.data.output):e.stderr((n.errors??[]).join(`
`))}var _tmpl$$6=template("<div class=html-viewer><div>");async function html(t,e){render(()=>createComponent(HtmlViewer,{code:t}),e.viewEl)}const HtmlViewer=t=>{let e,n,o;return onMount(()=>{e&&(o=e.attachShadow({mode:"closed"}),o.appendChild(n))}),onCleanup(()=>{e==null||e.detach()}),(()=>{var s=_tmpl$$6(),i=s.firstChild,a=e;typeof a=="function"?use(a,s):e=s;var c=n;return typeof c=="function"?use(c,i):n=i,createRenderEffect(()=>i.innerHTML=t.code),s})()},languages={kotlin,rust,java,c:cpp,cpp,csharp,js,javascript:js,html,hs,haskell:hs,ts,typescript:ts,python,go,swift,v,vlang:v,wy,wenyan:wy,crystal,cr:crystal,r,R:r};function createStdio(){let t=[],e=[];const n=l=>{t=l(t);for(const f of e)f(t)},o=l=>{n(()=>l)},s=(...l)=>{const f=l.join(",");n(d=>[...d,f])},i=(...l)=>{const f=l.join(",");n(d=>[...d,f])},a=document.createElement("div");return{subscribe:l=>(e.push(l),()=>{e=e.filter(f=>f!==l)}),write:s,viewEl:a,stdout:s,stderr:i,clear:()=>{o([]),a.empty()},update:n,set:o}}const backend={...languages};var _tmpl$$5=template('<div class="setting-item mod-toggle"><div class=setting-item-info><div class=setting-item-name>Auto run</div><div class=setting-item-description>Automatically run markdown CodeBlock</div></div><div class=setting-item-control><div class=checkbox-container><input type=checkbox>');const SettingTab=t=>(()=>{var e=_tmpl$$5(),n=e.firstChild,o=n.nextSibling,s=o.firstChild,i=s.firstChild;return s.$$click=()=>t.settingsUpdate("autoRun",a=>!a),createRenderEffect(()=>s.classList.toggle("is-enabled",!!t.settings.autoRun)),createRenderEffect(()=>i.checked=t.settings.autoRun),e})();delegateEvents(["click"]);var _tmpl$$4=template("<div class=lds-ellipsis><div class=dot></div><div class=dot></div><div class=dot></div><div class=dot>");const Spin=()=>_tmpl$$4();var commonjsGlobal=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function getDefaultExportFromCjs(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var md5$1={exports:{}};function commonjsRequire(t){throw new Error('Could not dynamically require "'+t+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var core={exports:{}},hasRequiredCore;function requireCore(){return hasRequiredCore||(hasRequiredCore=1,function(t,e){(function(n,o){t.exports=o()})(commonjsGlobal,function(){var n=n||function(o,s){var i;if(typeof window<"u"&&window.crypto&&(i=window.crypto),typeof self<"u"&&self.crypto&&(i=self.crypto),typeof globalThis<"u"&&globalThis.crypto&&(i=globalThis.crypto),!i&&typeof window<"u"&&window.msCrypto&&(i=window.msCrypto),!i&&typeof commonjsGlobal<"u"&&commonjsGlobal.crypto&&(i=commonjsGlobal.crypto),!i&&typeof commonjsRequire=="function")try{i=require("crypto")}catch{}var a=function(){if(i){if(typeof i.getRandomValues=="function")try{return i.getRandomValues(new Uint32Array(1))[0]}catch{}if(typeof i.randomBytes=="function")try{return i.randomBytes(4).readInt32LE()}catch{}}throw new Error("Native crypto module could not be used to get secure random number.")},c=Object.create||function(){function u(){}return function(w){var b;return u.prototype=w,b=new u,u.prototype=null,b}}(),m={},l=m.lib={},f=l.Base=function(){return{extend:function(u){var w=c(this);return u&&w.mixIn(u),(!w.hasOwnProperty("init")||this.init===w.init)&&(w.init=function(){w.$super.init.apply(this,arguments)}),w.init.prototype=w,w.$super=this,w},create:function(){var u=this.extend();return u.init.apply(u,arguments),u},init:function(){},mixIn:function(u){for(var w in u)u.hasOwnProperty(w)&&(this[w]=u[w]);u.hasOwnProperty("toString")&&(this.toString=u.toString)},clone:function(){return this.init.prototype.extend(this)}}}(),d=l.WordArray=f.extend({init:function(u,w){u=this.words=u||[],w!=s?this.sigBytes=w:this.sigBytes=u.length*4},toString:function(u){return(u||C).stringify(this)},concat:function(u){var w=this.words,b=u.words,E=this.sigBytes,x=u.sigBytes;if(this.clamp(),E%4)for(var P=0;P<x;P++){var B=b[P>>>2]>>>24-P%4*8&255;w[E+P>>>2]|=B<<24-(E+P)%4*8}else for(var k=0;k<x;k+=4)w[E+k>>>2]=b[k>>>2];return this.sigBytes+=x,this},clamp:function(){var u=this.words,w=this.sigBytes;u[w>>>2]&=4294967295<<32-w%4*8,u.length=o.ceil(w/4)},clone:function(){var u=f.clone.call(this);return u.words=this.words.slice(0),u},random:function(u){for(var w=[],b=0;b<u;b+=4)w.push(a());return new d.init(w,u)}}),$=m.enc={},C=$.Hex={stringify:function(u){for(var w=u.words,b=u.sigBytes,E=[],x=0;x<b;x++){var P=w[x>>>2]>>>24-x%4*8&255;E.push((P>>>4).toString(16)),E.push((P&15).toString(16))}return E.join("")},parse:function(u){for(var w=u.length,b=[],E=0;E<w;E+=2)b[E>>>3]|=parseInt(u.substr(E,2),16)<<24-E%8*4;return new d.init(b,w/2)}},_=$.Latin1={stringify:function(u){for(var w=u.words,b=u.sigBytes,E=[],x=0;x<b;x++){var P=w[x>>>2]>>>24-x%4*8&255;E.push(String.fromCharCode(P))}return E.join("")},parse:function(u){for(var w=u.length,b=[],E=0;E<w;E++)b[E>>>2]|=(u.charCodeAt(E)&255)<<24-E%4*8;return new d.init(b,w)}},S=$.Utf8={stringify:function(u){try{return decodeURIComponent(escape(_.stringify(u)))}catch{throw new Error("Malformed UTF-8 data")}},parse:function(u){return _.parse(unescape(encodeURIComponent(u)))}},A=l.BufferedBlockAlgorithm=f.extend({reset:function(){this._data=new d.init,this._nDataBytes=0},_append:function(u){typeof u=="string"&&(u=S.parse(u)),this._data.concat(u),this._nDataBytes+=u.sigBytes},_process:function(u){var w,b=this._data,E=b.words,x=b.sigBytes,P=this.blockSize,B=P*4,k=x/B;u?k=o.ceil(k):k=o.max((k|0)-this._minBufferSize,0);var N=k*P,j=o.min(N*4,x);if(N){for(var R=0;R<N;R+=P)this._doProcessBlock(E,R);w=E.splice(0,N),b.sigBytes-=j}return new d.init(w,j)},clone:function(){var u=f.clone.call(this);return u._data=this._data.clone(),u},_minBufferSize:0});l.Hasher=A.extend({cfg:f.extend(),init:function(u){this.cfg=this.cfg.extend(u),this.reset()},reset:function(){A.reset.call(this),this._doReset()},update:function(u){return this._append(u),this._process(),this},finalize:function(u){u&&this._append(u);var w=this._doFinalize();return w},blockSize:16,_createHelper:function(u){return function(w,b){return new u.init(b).finalize(w)}},_createHmacHelper:function(u){return function(w,b){return new T.HMAC.init(u,b).finalize(w)}}});var T=m.algo={};return m}(Math);return n})}(core)),core.exports}(function(t,e){(function(n,o){t.exports=o(requireCore())})(commonjsGlobal,function(n){return function(o){var s=n,i=s.lib,a=i.WordArray,c=i.Hasher,m=s.algo,l=[];(function(){for(var S=0;S<64;S++)l[S]=o.abs(o.sin(S+1))*4294967296|0})();var f=m.MD5=c.extend({_doReset:function(){this._hash=new a.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(S,A){for(var T=0;T<16;T++){var u=A+T,w=S[u];S[u]=(w<<8|w>>>24)&16711935|(w<<24|w>>>8)&4278255360}var b=this._hash.words,E=S[A+0],x=S[A+1],P=S[A+2],B=S[A+3],k=S[A+4],N=S[A+5],j=S[A+6],R=S[A+7],D=S[A+8],M=S[A+9],U=S[A+10],I=S[A+11],W=S[A+12],F=S[A+13],G=S[A+14],H=S[A+15],p=b[0],h=b[1],g=b[2],y=b[3];p=d(p,h,g,y,E,7,l[0]),y=d(y,p,h,g,x,12,l[1]),g=d(g,y,p,h,P,17,l[2]),h=d(h,g,y,p,B,22,l[3]),p=d(p,h,g,y,k,7,l[4]),y=d(y,p,h,g,N,12,l[5]),g=d(g,y,p,h,j,17,l[6]),h=d(h,g,y,p,R,22,l[7]),p=d(p,h,g,y,D,7,l[8]),y=d(y,p,h,g,M,12,l[9]),g=d(g,y,p,h,U,17,l[10]),h=d(h,g,y,p,I,22,l[11]),p=d(p,h,g,y,W,7,l[12]),y=d(y,p,h,g,F,12,l[13]),g=d(g,y,p,h,G,17,l[14]),h=d(h,g,y,p,H,22,l[15]),p=$(p,h,g,y,x,5,l[16]),y=$(y,p,h,g,j,9,l[17]),g=$(g,y,p,h,I,14,l[18]),h=$(h,g,y,p,E,20,l[19]),p=$(p,h,g,y,N,5,l[20]),y=$(y,p,h,g,U,9,l[21]),g=$(g,y,p,h,H,14,l[22]),h=$(h,g,y,p,k,20,l[23]),p=$(p,h,g,y,M,5,l[24]),y=$(y,p,h,g,G,9,l[25]),g=$(g,y,p,h,B,14,l[26]),h=$(h,g,y,p,D,20,l[27]),p=$(p,h,g,y,F,5,l[28]),y=$(y,p,h,g,P,9,l[29]),g=$(g,y,p,h,R,14,l[30]),h=$(h,g,y,p,W,20,l[31]),p=C(p,h,g,y,N,4,l[32]),y=C(y,p,h,g,D,11,l[33]),g=C(g,y,p,h,I,16,l[34]),h=C(h,g,y,p,G,23,l[35]),p=C(p,h,g,y,x,4,l[36]),y=C(y,p,h,g,k,11,l[37]),g=C(g,y,p,h,R,16,l[38]),h=C(h,g,y,p,U,23,l[39]),p=C(p,h,g,y,F,4,l[40]),y=C(y,p,h,g,E,11,l[41]),g=C(g,y,p,h,B,16,l[42]),h=C(h,g,y,p,j,23,l[43]),p=C(p,h,g,y,M,4,l[44]),y=C(y,p,h,g,W,11,l[45]),g=C(g,y,p,h,H,16,l[46]),h=C(h,g,y,p,P,23,l[47]),p=_(p,h,g,y,E,6,l[48]),y=_(y,p,h,g,R,10,l[49]),g=_(g,y,p,h,G,15,l[50]),h=_(h,g,y,p,N,21,l[51]),p=_(p,h,g,y,W,6,l[52]),y=_(y,p,h,g,B,10,l[53]),g=_(g,y,p,h,U,15,l[54]),h=_(h,g,y,p,x,21,l[55]),p=_(p,h,g,y,D,6,l[56]),y=_(y,p,h,g,H,10,l[57]),g=_(g,y,p,h,j,15,l[58]),h=_(h,g,y,p,F,21,l[59]),p=_(p,h,g,y,k,6,l[60]),y=_(y,p,h,g,I,10,l[61]),g=_(g,y,p,h,P,15,l[62]),h=_(h,g,y,p,M,21,l[63]),b[0]=b[0]+p|0,b[1]=b[1]+h|0,b[2]=b[2]+g|0,b[3]=b[3]+y|0},_doFinalize:function(){var S=this._data,A=S.words,T=this._nDataBytes*8,u=S.sigBytes*8;A[u>>>5]|=128<<24-u%32;var w=o.floor(T/4294967296),b=T;A[(u+64>>>9<<4)+15]=(w<<8|w>>>24)&16711935|(w<<24|w>>>8)&4278255360,A[(u+64>>>9<<4)+14]=(b<<8|b>>>24)&16711935|(b<<24|b>>>8)&4278255360,S.sigBytes=(A.length+1)*4,this._process();for(var E=this._hash,x=E.words,P=0;P<4;P++){var B=x[P];x[P]=(B<<8|B>>>24)&16711935|(B<<24|B>>>8)&4278255360}return E},clone:function(){var S=c.clone.call(this);return S._hash=this._hash.clone(),S}});function d(S,A,T,u,w,b,E){var x=S+(A&T|~A&u)+w+E;return(x<<b|x>>>32-b)+A}function $(S,A,T,u,w,b,E){var x=S+(A&u|T&~u)+w+E;return(x<<b|x>>>32-b)+A}function C(S,A,T,u,w,b,E){var x=S+(A^T^u)+w+E;return(x<<b|x>>>32-b)+A}function _(S,A,T,u,w,b,E){var x=S+(T^(A|~u))+w+E;return(x<<b|x>>>32-b)+A}s.MD5=c._createHelper(f),s.HmacMD5=c._createHmacHelper(f)}(Math),n.MD5})})(md5$1);var md5Exports=md5$1.exports;const md5=getDefaultExportFromCjs(md5Exports),O=Object,colorCodes=["black","red","green","yellow","blue","magenta","cyan","lightGray","","default"],colorCodesLight=["darkGray","lightRed","lightGreen","lightYellow","lightBlue","lightMagenta","lightCyan","white",""],styleCodes=["","bright","dim","italic","underline","","","inverse"],asBright={red:"lightRed",green:"lightGreen",yellow:"lightYellow",blue:"lightBlue",magenta:"lightMagenta",cyan:"lightCyan",black:"darkGray",lightGray:"white"},types={0:"style",2:"unstyle",3:"color",9:"colorLight",4:"bgColor",10:"bgColorLight"},subtypes={color:colorCodes,colorLight:colorCodesLight,bgColor:colorCodes,bgColorLight:colorCodesLight,style:styleCodes,unstyle:styleCodes};class Color{constructor(e,n,o){this.background=e,this.name=n,this.brightness=o}get inverse(){return new Color(!this.background,this.name||(this.background?"black":"white"),this.brightness)}get clean(){const e=this.name==="default"?"":this.name,n=this.brightness===Code.bright,o=this.brightness===Code.dim;if(!(!e&&!n&&!o))return{name:e,bright:n,dim:o}}defaultBrightness(e){return new Color(this.background,this.name,this.brightness||e)}css(e){const n=e?this.inverse:this,o=n.brightness===Code.bright&&asBright[n.name]||n.name,s=n.background?"background:":"color:",i=Colors.rgb[o],a=this.brightness===Code.dim?.5:1;return i?s+"rgba("+[...i,a].join(",")+");":!n.background&&a<1?"color:rgba(0,0,0,0.5);":""}}class Code{constructor(e){let n,o,s,i="",a=!1;if(e!==void 0){if(n=Number(e),o=types[Math.floor(n/10)],o===void 0||subtypes[o]===void 0)return;s=subtypes[o][n%10],i="\x1B["+n+"m",a=n===Code.noBrightness||n===Code.bright||n===Code.dim}this.value=n,this.type=o,this.subtype=s,this.str=i,this.isBrightness=a}static str(e){return e===void 0?"":"\x1B["+Number(e)+"m"}clone(){const e=new Code(void 0);return e.value=this.value,e.type=this.type,e.subtype=this.subtype,e.str=this.str,e.isBrightness=this.isBrightness,e}}O.assign(Code,{reset:0,bright:1,dim:2,inverse:7,noBrightness:22,noItalic:23,noUnderline:24,noInverse:27,noColor:39,noBgColor:49});const replaceAll=(t,e,n)=>t.split(e).join(n),denormalizeBrightness=t=>t.replace(/(\u001b\[(1|2)m)/g,"\x1B[22m$1"),normalizeBrightness=t=>t.replace(/\u001b\[22m(\u001b\[(1|2)m)/g,"$1"),wrap=(t,e,n)=>{const o=Code.str(e),s=Code.str(n);return String(t).split(`
`).map(i=>denormalizeBrightness(o+replaceAll(normalizeBrightness(i),s,o)+s)).join(`
`)},camel=(t,e)=>t+e.charAt(0).toUpperCase()+e.slice(1),stringWrappingMethods=[...colorCodes.map((t,e)=>t?[[t,30+e,Code.noColor],[camel("bg",t),40+e,Code.noBgColor]]:[]),...colorCodesLight.map((t,e)=>t?[[t,90+e,Code.noColor],[camel("bg",t),100+e,Code.noBgColor]]:[]),...["","BrightRed","BrightGreen","BrightYellow","BrightBlue","BrightMagenta","BrightCyan"].map((t,e)=>t?[["bg"+t,100+e,Code.noBgColor]]:[]),...styleCodes.map((t,e)=>t?[[t,e,t==="bright"||t==="dim"?Code.noBrightness:20+e]]:[])].reduce((t,e)=>t.concat(e)),assignStringWrappingAPI=(t,e=t)=>stringWrappingMethods.reduce((n,[o,s,i])=>O.defineProperty(n,o,{get:()=>assignStringWrappingAPI(a=>e(wrap(a,s,i)))}),t),TEXT=0,BRACKET=1,CODE=2;class Span{constructor(e,n){this.code=e,this.text=n,this.css="",this.color=void 0,this.bgColor=void 0,this.bold=void 0,this.inverse=void 0,this.italic=void 0,this.underline=void 0,this.bright=void 0,this.dim=void 0}}function*rawParse(t){const e={state:TEXT,buffer:"",text:"",code:"",codes:[]},o=splitStringToChunksOfSize(t(),1048576);for(let s=0;s<o.length;s++){const i=o[s];o[s]=void 0,yield*processChunk(i,e)}e.state!==TEXT&&(e.text+=e.buffer),e.text&&(yield new Span(new Code,e.text))}function splitStringToChunksOfSize(t,e){const n=[],o=Math.ceil(t.length/e);for(let s=0,i=0;s<o;++s,i+=e)n.push(t.substring(i,i+e));return n}function*processChunk(t,e){const n=t,o=t.length;for(let s=0;s<o;s++){const i=n[s];switch(e.buffer+=i,e.state){case TEXT:i==="\x1B"?(e.state=BRACKET,e.buffer=i):e.text+=i;break;case BRACKET:i==="["?(e.state=CODE,e.code="",e.codes=[]):(e.state=TEXT,e.text+=e.buffer);break;case CODE:if(i>="0"&&i<="9")e.code+=i;else if(i===";")e.codes.push(new Code(e.code)),e.code="";else if(i==="m"){e.code=e.code||"0";for(const a of e.codes)yield new Span(a,e.text),e.text="";yield new Span(new Code(e.code),e.text),e.text="",e.state=TEXT}else e.state=TEXT,e.text+=e.buffer}}}function*parseAnsi(t){let e=new Color,n=new Color(!0),o,s=new Set;function i(){e=new Color,n=new Color(!0),o=void 0,s.clear()}i();for(const a of t){const c=a.code;if(a.text!==""){const m=s.has("inverse"),l=s.has("underline")?"text-decoration: underline;":"",f=s.has("italic")?"font-style: italic;":"",d=o===Code.bright?"font-weight: bold;":"",$=e.defaultBrightness(o),C=new Span(a.code?a.code.clone():void 0,a.text);C.css=a.css?a.css:d+f+l+$.css(m)+n.css(m),C.bold=a.bold?a.bold:!!d,C.color=a.color?a.color:$.clean,C.bgColor=a.bgColor?a.bgColor:n.clean,C.inverse=m,C.italic=!!f,C.underline=!!l,C.bright=s.has("bright"),C.dim=s.has("dim"),yield C}if(c.isBrightness){o=c.value;continue}if(a.code.value!==void 0){if(a.code.value===Code.reset){i();continue}switch(a.code.type){case"color":case"colorLight":e=new Color(!1,c.subtype);break;case"bgColor":case"bgColorLight":n=new Color(!0,c.subtype);break;case"style":s.add(c.subtype);break;case"unstyle":s.delete(c.subtype);break}}}}class Colors{constructor(e){this.spans=e?Array.from(rawParse(typeof e=="string"?()=>e:e)):[]}get str(){return this.spans.reduce((e,n)=>e+n.text+n.code.str,"")}get parsed(){const e=new Colors;return e.spans=Array.from(parseAnsi(this.spans)),e}get asChromeConsoleLogArguments(){const e=this.parsed.spans;return[e.map(n=>"%c"+n.text).join(""),...e.map(n=>n.css)]}get browserConsoleArguments(){return this.asChromeConsoleLogArguments}static get nice(){return Colors.names.forEach(e=>{e in String.prototype||O.defineProperty(String.prototype,e,{get:function(){return Colors[e](this)}})}),Colors}static parse(e){return new Colors(e).parsed}static parseIterator(e){return parseAnsi(rawParse(typeof e=="string"?()=>e:e))}static strip(e){return e.replace(/[\u001b\u009b][[()#;?]*(?:[0-9]{1,4}(?:;[0-9]{0,4})*)?[0-9A-PRZcf-nqry=><]/g,"")}static isEscaped(e){return e=String(e),Colors.strip(e)!==e}[Symbol.iterator](){return this.spans[Symbol.iterator]()}static get ansicolor(){return Colors}}assignStringWrappingAPI(Colors,t=>t);Colors.names=stringWrappingMethods.map(([t])=>t);Colors.rgb={black:[0,0,0],darkGray:[100,100,100],lightGray:[200,200,200],white:[255,255,255],red:[204,0,0],lightRed:[255,51,0],green:[0,204,0],lightGreen:[51,204,51],yellow:[204,102,0],lightYellow:[255,153,51],blue:[0,0,255],lightBlue:[26,140,255],magenta:[204,0,204],lightMagenta:[255,0,255],cyan:[0,153,255],lightCyan:[0,204,255]};var ansicolor=Colors;const Colors$1=getDefaultExportFromCjs(ansicolor),parse=Colors$1.parse;Colors$1.parseIterator;Colors$1.strip;Colors$1.ansicolor;Colors$1.black;Colors$1.bgBlack;Colors$1.red;Colors$1.bgRed;Colors$1.green;Colors$1.bgGreen;Colors$1.yellow;Colors$1.bgYellow;Colors$1.blue;Colors$1.bgBlue;Colors$1.magenta;Colors$1.bgMagenta;Colors$1.cyan;Colors$1.bgCyan;Colors$1.lightGray;Colors$1.bgLightGray;Colors$1.bgDefault;Colors$1.darkGray;Colors$1.bgDarkGray;Colors$1.lightRed;Colors$1.bgLightRed;Colors$1.lightGreen;Colors$1.bgLightGreen;Colors$1.lightYellow;Colors$1.bgLightYellow;Colors$1.lightBlue;Colors$1.bgLightBlue;Colors$1.lightMagenta;Colors$1.bgLightMagenta;Colors$1.lightCyan;Colors$1.bgLightCyan;Colors$1.white;Colors$1.bgWhite;Colors$1.bgBrightRed;Colors$1.bgBrightGreen;Colors$1.bgBrightYellow;Colors$1.bgBrightBlue;Colors$1.bgBrightMagenta;Colors$1.bgBrightCyan;Colors$1.bright;Colors$1.dim;Colors$1.italic;Colors$1.underline;Colors$1.inverse;Colors$1.names;Colors$1.rgb;var _tmpl$$3=template("<ul>"),_tmpl$2$2=template("<li>"),_tmpl$3$1=template("<span>");const htmlRegex=/^\s*<([a-zA-Z_][a-zA-Z0-9-_]*)(\s+[^>]+)*>.*<\/\1>\s*$/,Term=t=>(()=>{var e=_tmpl$$3();return insert(e,createComponent(For,{get each(){return t.lines},children:n=>createComponent(Show,{get when(){return!htmlRegex.test(n)},get fallback(){return(()=>{var o=_tmpl$2$2();return o.innerHTML=n,o})()},get children(){var o=_tmpl$2$2();return insert(o,createComponent(For,{get each(){return parse(n??"").spans},children:s=>(()=>{var i=_tmpl$3$1();return insert(i,()=>s.text),createRenderEffect(a=>style(i,s.css+(s.color?`color;${s.color}`:""),a)),i})()})),o}})})),e})();var _tmpl$$2=template('<svg class=svg-icon xmlns=http://www.w3.org/2000/svg width=0.75em height=1em viewBox="0 0 384 512"><path fill=currentColor d="M342.6 150.6c12.5-12.5 12.5-32.8 0-45.3s-32.8-12.5-45.3 0L192 210.7L86.6 105.4c-12.5-12.5-32.8-12.5-45.3 0s-12.5 32.8 0 45.3L146.7 256L41.4 361.4c-12.5 12.5-12.5 32.8 0 45.3s32.8 12.5 45.3 0L192 301.3l105.4 105.3c12.5 12.5 32.8 12.5 45.3 0s12.5-32.8 0-45.3L237.3 256z">'),_tmpl$2$1=template('<svg class=svg-icon xmlns=http://www.w3.org/2000/svg width=0.75em height=1em viewBox="0 0 384 512"><path fill=currentColor d="M73 39c-14.8-9.1-33.4-9.4-48.5-.9S0 62.6 0 80v352c0 17.4 9.4 33.4 24.5 41.9S58.2 482 73 473l288-176c14.3-8.7 23-24.2 23-41s-8.7-32.2-23-41z">');const icons={clear:()=>_tmpl$$2(),play:()=>_tmpl$2$1()},Icon=t=>icons[t.name]();var _tmpl$$1=template("<i aria-label=play class=button-play>"),_tmpl$2=template("<hr class=code-seprator>"),_tmpl$3=template("<div class=loadding>"),_tmpl$4=template("<div class=code-output>"),_tmpl$5=template('<div class="code-emitter-block solid">'),_tmpl$6=template("<div>"),_tmpl$7=template("<i aria-label=clear class=button-clear>");const Play=t=>{const e=()=>`code-emitter-cache-${t.sourcePath}`,n=()=>md5(t.code).toString(),o=createStdio(),[s,i]=createSignal();o.subscribe(i);const a=()=>{var $;return(($=s())==null?void 0:$.length)>0||o.viewEl.hasChildNodes()},[c,m]=createSignal(!1),l=async()=>{m(!0);try{const $=backend[t.lang];await $(t.code,o)}finally{m(!1)}},f=()=>{const $=localStorage.getItem(e());if(!$)return;const _=JSON.parse($)[n()];if(_)return _.outputs},d=()=>{const $=localStorage.getItem(e()),C=$?JSON.parse($):{};C[n()]={outputs:s(),lastAccessTime:Date.now()},localStorage.setItem(e(),JSON.stringify(C))};return onMount(async()=>{const $=f();$?o.set($):t.autoRun&&await l()}),onCleanup(d),(()=>{var $=_tmpl$5();return insert($,createComponent(Show,{get when(){return createMemo(()=>!c())()&&!a()},get children(){var C=_tmpl$$1();return C.$$click=l,insert(C,createComponent(Icon,{name:"play"})),C}}),null),insert($,createComponent(Show,{get when(){return c()||a()},get children(){return[_tmpl$2(),(()=>{var C=_tmpl$4();return insert(C,createComponent(Show,{get when(){return c()},get fallback(){return[(()=>{var _=_tmpl$6();return insert(_,()=>o.viewEl),_})(),createComponent(Term,{get lines(){return s()}}),(()=>{var _=_tmpl$7();return addEventListener(_,"click",o.clear),insert(_,createComponent(Icon,{name:"clear"})),_})()]},get children(){var _=_tmpl$3();return insert(_,createComponent(Spin,{})),_}})),C})()]}}),null),$})()};delegateEvents(["click"]);var _tmpl$=template("<pre><code>");const CodeBlock=t=>{const e=async o=>{(await obsidian.loadPrism()).highlightElement(o)},n=()=>`language-${t.lang}`;return(()=>{var o=_tmpl$(),s=o.firstChild;return use(e,s),insert(s,()=>t.code),insert(o,createComponent(Play,t),null),createRenderEffect(()=>className(o,n())),o})()},SETTING_DEFAULT={autoRun:!1,python:{cdn:"https://cdn.jsdelivr.net/pyodide/v0.26.2/full/"}};class CodeEmitterPlugin extends obsidian.Plugin{constructor(n,o){super(n,o);L(this,"settings");L(this,"settingsUpdate");const[s,i]=createStore(SETTING_DEFAULT);this.settings=s,this.settingsUpdate=i}async onload(){await this.loadSettings(),this.addSettingTab(new PluginSolidSettingTab(this,SettingTab,{settings:this.settings,settingsUpdate:this.settingsUpdate})),Object.keys(backend).forEach(n=>{n.includes("+")||n.includes("#")||this.registerMarkdownCodeBlockProcessor(n,async(o,s,i)=>{render(()=>{const a=this;return createComponent(CodeBlock,{lang:n,code:o,get sourcePath(){return i.sourcePath},get autoRun(){return a.settings.autoRun}})},s)},-1)})}async unload(){await this.saveSettings(),super.unload()}async loadSettings(){this.settingsUpdate(Object.assign({},SETTING_DEFAULT,await this.loadData()))}async saveSettings(){await this.saveData(unwrap(this.settings))}}exports.default=CodeEmitterPlugin;

/* nosourcemap */