{"I18N_AGREEMENT": true, "I18N_WIZARD": false, "I18N_UUID": "5ba64829-7bdc-4eb4-b7a5-d383e603cda4", "I18N_LANGUAGE": "zh-cn", "I18N_COLOR": "#409EFF", "I18N_AUTHOR": "", "I18N_EDIT_MODE": true, "I18N_OPEN_SETTINGS": true, "I18N_CHECK_UPDATES": true, "I18N_SEARCH_TEXT": "", "I18N_SORT": "0", "I18N_TYPE": "0", "I18N_MODE": 0, "I18N_NOTICE": true, "I18N_START_TIME": true, "I18N_MODE_LDT": true, "I18N_AUTOMATIC_UPDATE": true, "I18N_INCREMENTAL_EXTRACTION": true, "I18N_NAME_TRANSLATION": false, "I18N_NAME_TRANSLATION_PREFIX": "[", "I18N_NAME_TRANSLATION_SUFFIX": "]", "I18N_STYLE_SETTINGS": "obsidian-style-settings", "I18N_MODE_NDT": true, "I18N_IGNORE": true, "I18N_NDT_URL": "gitee", "I18N_MODE_NIT": true, "I18N_NIT_API": "BAIDU", "I18N_NIT_API_INTERVAL": 500, "I18N_NIT_APIS": {"BAIDU": {"FROM": "auto", "TO": "zh", "APP_ID": "20200229000390287", "KEY": "jFEA3B7SCKZ6AFALCe_g"}}, "I18N_NIT_OPENAI_URL": "https://api.openai.com", "I18N_NIT_OPENAI_KEY": "", "I18N_NIT_OPENAI_MODEL": "gpt-3.5-turbo", "I18N_NIT_OPENAI_TIPS": "你是一个翻译工作者，你将进行obsidian笔记软件的插件翻译，本次翻译的插件名称为: ${plugin}，请结合插件名称以及软件翻译的标准进行后续工作，因为大多数文本长度较短，请以符合中文习惯的方式翻译。接下来我会提交给你很多英文文本，请将其翻译为简体中文，并且只返回给我翻译后的内容", "I18N_MODE_IMT": true, "I18N_IMT_CONFIG": {"selectors": ["*"], "excludeSelectors": [".modal .i18n__container"], "excludeTags": [], "additionalSelectors": [], "additionalExcludeSelectors": [], "additionalExcludeTags": [], "stayOriginalSelectors": [], "stayOriginalTags": [], "atomicBlockSelectors": [], "atomicBlockTags": [], "id": "obsidian.md", "matches": "obsidian.md", "mainFrameMinTextCount": 0, "mainFrameMinWordCount": 0}, "I18N_SHARE_MODE": false, "I18N_SHARE_TOKEN": "", "I18N_ADMIN_MODE": false, "I18N_ADMIN_VERIFY": false, "I18N_ADMIN_TOKEN": "", "I18N_RE_TEMP_MODE": true, "I18N_RE_TEMP": "", "I18N_RE_MODE": "默认", "I18N_RE_FLAGS": "gs", "I18N_RE_LENGTH": 300, "I18N_RE_MODE_EDIT": false, "I18N_RE_MODE_DISPLAY": false, "I18N_RE_DATAS_DISPLAY": false, "I18N_RE_MODES": ["默认"], "I18N_RE_DATAS": {"默认": ["Notice\\(\\s*(.+?)\\s*\\)", ".log\\(\\s*(.+?)\\s*\\)", ".error\\(\\s*(.+?)\\s*\\)", "t\\s*=\\s*:\\s*(['\"`])(.+?)\\1", ".textContent\\s*=\\s*:\\s*(['\"`])(.+?)\\1", "name\\s*:\\s*(['\"`])(.+?)\\1", "description\\s*:\\s*(['\"`])(.+?)\\1", "selection\\s*:\\s*(['\"`])(.+?)\\1", "annotation\\s*:\\s*(['\"`])(.+?)\\1", "link\\s*:\\s*(['\"`])(.+?)\\1", "text\\s*:\\s*(['\"`])(.+?)\\1", "search\\s*:\\s*(['\"`])(.+?)\\1", "speech\\s*:\\s*(['\"`])(.+?)\\1", "page\\s*:\\s*(['\"`])(.+?)\\1", "settings\\s*:\\s*(['\"`])(.+?)\\1", ".setText\\(\\s*(['\"`])(.+?)\\1\\s*\\)", ".setButtonText\\(\\s*(['\"`])(.+?)\\1\\s*\\)", ".setName\\(\\s*(['\"`])(.+?)\\1\\s*\\)", ".setDesc\\(\\s*(['\"`])(.+?)\\1\\s*\\)", ".setPlaceholder\\(\\s*(['\"`])(.+?)\\1\\s*\\)", ".setTooltip\\(\\s*(['\"`])(.+?)\\1\\s*\\)", ".appendText\\(\\s*(['\"`])(.+?)\\1\\s*\\)", ".setTitle\\(\\s*(['\"`])(.+?)\\1\\s*\\)", ".addHeading\\(\\s*(['\"`])(.+?)\\1\\s*\\)", ".renderMarkdown\\(\\s*(['\"`])(.+?)\\1\\s*\\)", ".innerText\\s*=\\s*(['\"`]).*?\\1"]}, "I18N_TAG_TYPE": "light", "I18N_TAG_SHAPE": "square", "I18N_BUTTON_TYPE": "default", "I18N_BUTTON_SHAPE": "square"}