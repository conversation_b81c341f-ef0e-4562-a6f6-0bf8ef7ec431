:root {
  --advanced-tables-helper-size: 28px;
}

.HyperMD-table-row span.cm-inline-code {
  font-size: 100%;
  padding: 0px;
}

.advanced-tables-buttons>div>.title {
  font-weight: var(--font-medium);
  font-size: var(--nav-item-size);
  color: var(--nav-item-color);
  text-decoration: underline;
}

[data-type="advanced-tables-toolbar"] .nav-buttons-container {
  column-gap: 0.2rem;
  margin: 0.2rem 0 0.2rem 0;
  justify-content: start;
}

[data-type="advanced-tables-toolbar"] .nav-buttons-container::before {
  min-width: 2.6rem;
  line-height: var(--advanced-tables-helper-size);
  font-size: var(--nav-item-size);
  font-weight: var(--nav-item-weight);
  color: var(--nav-item-color);
}

[data-type="advanced-tables-toolbar"] .nav-buttons-container>* {
  height: var(--advanced-tables-helper-size);
  line-height: var(--advanced-tables-helper-size);
}

[data-type="advanced-tables-toolbar"] .nav-buttons-container .nav-action-button {
  width: var(--advanced-tables-helper-size);
  height: var(--advanced-tables-helper-size);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: var(--radius-s);
}

[data-type="advanced-tables-toolbar"] .nav-buttons-container .nav-action-button:hover {
  background-color: var(--nav-item-background-hover);
  color: var(--nav-item-color-hover);
  font-weight: var(--nav-item-weight-hover);
}

.advanced-tables-row-label {
  width: 50px;
}

.widget-icon {
  width: 20px;
  height: 20px;
  fill: var(--text-muted);
}

.widget-icon:hover {
  fill: var(--text-normal);
}

.advanced-tables-csv-export textarea {
  height: 200px;
  width: 100%;
}

.advanced-tables-donation {
  width: 70%;
  margin: 0 auto;
  text-align: center;
}

.advanced-tables-donate-button {
  margin: 10px;
}