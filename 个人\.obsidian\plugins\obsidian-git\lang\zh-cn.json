{"manifest": {"translationVersion": 1744633323188, "pluginVersion": "2.32.1"}, "description": {"original": "Integrate Git version control with automatic backup and other advanced features.", "translation": "集成Git版本控制，提供自动备份等高级功能。"}, "dict": {"Notice(\"Debug information copied to clipboard. May contain sensitive information!\")": "Notice(\"调试信息已复制到剪贴板。可能包含敏感信息！\")", "Notice(\"No repository found\")": "Notice(\"未找到仓库\")", "Notice(\"All local changes have been discarded. New files remain untouched.\")": "Notice(\"所有本地更改已被丢弃。新文件保持不变。\")", ".log(\"Reloading settings\")": ".log(\"重新加载设置\")", "name:\"History\"": "name:\"历史记录\"", "name:\"Open history view\"": "name:\"打开历史记录视图\"", "name:\"Open diff view\"": "name:\"打开差异视图\"", "name:\"Pull\"": "name:\"拉取\"", "name:\"Fetch\"": "name:\"获取\"", "name:\"Switch to remote branch\"": "name:\"切换到远程分支\"", "name:\"Commit-and-sync\"": "name:\"提交并同步\"", "name:\"Commit all changes\"": "name:\"提交所有更改\"", "name:\"Commit all changes with specific message\"": "name:\"使用特定消息提交所有更改\"", "name:\"Push\"": "name:\"推送\"", "name:\"Stage current file\"": "name:\"暂存当前文件\"", "name:\"Unstage current file\"": "name:\"取消暂存当前文件\"", "name:\"Edit remotes\"": "name:\"编辑远程仓库\"", "name:\"Set upstream branch\"": "name:\"设置上游分支\"", "name:\"CAUTION: Delete repository\"": "name:\"警告：删除仓库\"", "name:\"Initialize a new repo\"": "name:\"初始化新仓库\"", "name:\"Switch branch\"": "name:\"切换分支\"", "name:\"Create new branch\"": "name:\"创建新分支\"", "name:\"Delete branch\"": "name:\"删除分支\"", "name:\"Toggle line author information\"": "name:\"切换行作者信息\"", "name:\"fileIcon\"": "name:\"文件图标\"", "name:\"fileTag\"": "name:\"文件标签\"", "text:\"Save\"": "text:\"保存\"", "text:\"Cancel\"": "text:\"取消\"", ".setText(\"Copy Debug Information\")": ".setText(\"复制调试信息\")", ".setButtonText(\"Preview\")": ".setButtonText(\"预览\")", ".setButtonText(\"Reload\")": ".setButtonText(\"重新加载\")", ".setName(\"Automatic\")": ".setName(\"自动\")", ".setName(\"Auto push interval (minutes)\")": ".setName(\"自动推送间隔(分钟)\")", ".setName(\"Auto pull interval (minutes)\")": ".setName(\"自动拉取间隔(分钟)\")", ".setName(\"{{date}} placeholder format\")": ".setName(\"{{date}} 占位符格式\")", ".setName(\"{{hostname}} placeholder replacement\")": ".setName(\"{{hostname}} 占位符替换\")", ".setName(\"Pull\")": ".setName(\"拉取\")", ".setName(\"Merge strategy\")": ".setName(\"合并策略\")", ".setName(\"Commit-and-sync\")": ".setName(\"提交并同步\")", ".setName(\"Show Author\")": ".setName(\"显示作者\")", ".setName(\"Show Date\")": ".setName(\"显示日期\")", ".setName(\"Miscellaneous\")": ".setName(\"杂项\")", ".setName(\"Disable notifications\")": ".setName(\"禁用通知\")", ".setName(\"Show status bar\")": ".setName(\"显示状态栏\")", ".setName(\"Show branch status bar\")": ".setName(\"显示分支状态栏\")", ".setName(\"Commit author\")": ".setName(\"提交作者\")", ".setName(\"Password/Personal access token\")": ".setName(\"密码/个人访问令牌\")", ".setName(\"Author name for commit\")": ".setName(\"提交的作者名称\")", ".setName(\"Advanced\")": ".<PERSON><PERSON><PERSON>(\"高级\")", ".setName(\"Update submodules\")": ".setName(\"更新子模块\")", ".setName(\"Submodule recurse checkout/switch\")": ".setName(\"子模块递归检出/切换\")", ".setName(\"Additional environment variables\")": ".setName(\"额外的环境变量\")", ".setName(\"Disable on this device\")": ".setName(\"在此设备上禁用\")", ".setName(\"Support\")": ".setName(\"支持\")", ".setName(\"Donate\")": ".setName(\"捐赠\")", ".setName(\"Custom authoring date format\")": ".setName(\"自定义作者日期格式\")", ".setName(\"Text color\")": ".setName(\"文本颜色\")", ".setDesc(\"Push commits every X minutes. Set to 0 (default) to disable.\")": ".setDesc(\"每X分钟推送提交。设置为0(默认)以禁用。\")", ".setDesc(\"Specify custom hostname for every device.\")": ".setDesc(\"为每个设备指定自定义主机名。\")", ".setDesc(\"Type in your password. You won't be able to see it again.\")": ".setDesc(\"输入您的密码。您将无法再次看到它。\")", ".setPlaceholder(\"Select branch to checkout\")": ".setPlaceholder(\"选择要检出的分支\")", ".setTitle(\"Copy commit hash\")": ".setTitle(\"复制提交哈希\")", ".setTitle(\"Git: Stage\")": ".setTitle(\"Git: 暂存\")", "Notice(u,999999)": "Notice(u,999999)", "Notice(r,n=!0)": "Notice(r,n=!0)", "Notice(r,n?this.noticeLength:void 0)": "Notice(r,n?this.noticeLength:void 0)", "Notice(`${f}`)": "Notice(`${f}`)", "Notice(n.reason)": "Notice(n.reason)", "Notice(r.reason)": "Notice(r.reason)", "Notice(r,5*1e3)": "Notice(r,5*1e3)", "Notice(i.message,n)": "Notice(i.message,n)", ".log(t)": ".log(t)", ".log(O)": ".log(O)", "{log(...r)": "{log(...r)", ".log(string, string)": ".log(string, string)", ".log({ from: string, to: string })": ".log({ from: string, to: string })", ".log(s)": ".log(s)", " log(r,n=!0,i,a)": " log(r,n=!0,i,a)", ".log(o)": ".log(o)", ".log({n:1})": ".log({n:1})", ".log({...this.getRepo()": ".log({...this.getRepo()", ".log(void 0,!1,_)": ".log(void 0,!1,_)", ".log(void 0,!1,_,(x=R(a)": ".log(void 0,!1,_,(x=R(a)", " log(){return R(k)": " log(){return R(k)", ".log(r)": ".log(r)", "}log(...r)": "}log(...r)", ".log(`${this.manifest.id}:`,...r)": ".log(`${this.manifest.id}:`,...r)", ".error(i.error)": ".error(i.error)", ".error(n.error)": ".error(n.error)", ".error(i)": ".error(i)", ".error(`${this.manifest.id}:`,i.stack)": ".error(`${this.manifest.id}:`,i.stack)", "name:\"'+b(w.partials[S].name)+'\"": "name:\"'+b(w.partials[S].name)+'\"", "name:\"isomorphic-git\"": "name:\"isomorphic-git\"", "name:\"\",epochSeconds:cT(r),tz:\"": "name:\"\",epochSeconds:cT(r),tz:\"", "link:'<path d=\"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71\"></path><path d=\"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71\"></path>'": "link:'<path d=\"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71\"></path><path d=\"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71\"></path>'", "text:\"CMD (\\u2318) + OPTION (\\u2325) + I\"": "text:\"CMD (\\u2318) + OPTION (\\u2325) + I\"", "text:\"CTRL + SHIFT + I\"": "text:\"CTRL + SHIFT + I\"", "text:\"\\u26A0\\uFE0F\"": "text:\"\\u26A0\\uFE0F\"", "search:'<circle cx=\"11\" cy=\"11\" r=\"8\"></circle><line x1=\"21\" y1=\"21\" x2=\"16.65\" y2=\"16.65\"></line>'": "search:'<circle cx=\"11\" cy=\"11\" r=\"8\"></circle><line x1=\"21\" y1=\"21\" x2=\"16.65\" y2=\"16.65\"></line>'", ".setPlaceholder(\"git\")": ".setPlaceholder(\"git\")", ".setPlaceholder(\".git\")": ".setPlaceholder(\".git\")", ".setPlaceholder(\"YYYY-MM-DD HH:mm\")": ".setPlaceholder(\"YYYY-MM-DD HH:mm\")", "Notice(\"ObsidianGit: Base path does not exist\")": "Notice(\"ObsidianGit: 基本路径不存在\")", "Notice(\"Authentication failed. Please try with different credentials\")": "Notice(\"认证失败。请尝试使用不同的凭据\")", "Notice(\"This takes longer: Getting status\",this.noticeLength)": "Notice(\"这需要更长时间：正在获取状态\",this.noticeLength)", "Notice(\"Initializing pull\")": "Notice(\"正在初始化拉取\")", "Notice(\"Finished pull\",!1)": "Notice(\"拉取完成\",!1)", "Notice(\"Initializing push\")": "Notice(\"正在初始化推送\")", "Notice(\"Initializing clone\")": "Notice(\"正在初始化克隆\")", "Notice(\"Initializing fetch\")": "Notice(\"正在初始化获取\")", "Notice(\"Obsidian must be restarted for the changes to take affect.\")": "Notice(\"Obsidian必须重新启动才能使更改生效。\")", "Notice(\"It seems like you are not using GitHub\")": "Notice(\"看起来您没有使用GitHub\")", "Notice(\"Successfully deleted repository. Reloading plugin...\")": "Notice(\"已成功删除仓库。正在重新加载插件...\")", "Notice(\"Can't find a valid git repository. Please create one via the given command or clone an existing repo.\",1e4)": "Notice(\"找不到有效的git仓库。请通过给定命令创建一个或克隆现有仓库。\",1e4)", "Notice(\"Initialized new repo\")": "Notice(\"已初始化新仓库\")", "Notice(\"Aborted clone\")": "Notice(\"已中止克隆\")", "Notice(\"Invalid depth. Aborting clone.\")": "Notice(\"深度无效。正在中止克隆。\")", "Notice(`Cloning new repo into \"${a}\"`)": "Notice(`正在克隆新仓库到 \"${a}\"`)", "Notice(\"Cloned new repo.\")": "Notice(\"已克隆新仓库。\")", "Notice(\"Please restart Obsidian\")": "Notice(\"请重启Obsidian\")", "Notice(\"Auto backup: Please enter a custom commit message. Leave empty to abort\")": "Notice(\"自动备份：请输入自定义提交消息。留空以中止\")", "Notice(\"No upstream branch is set. Please select one.\")": "Notice(\"未设置上游分支。请选择一个。\")", "Notice(\"Aborted\")": "Notice(\"已中止\")", ".log(\"No tracking branch found. Ignoring pull of main repo and updating submodules only.\")": ".log(\"未找到跟踪分支。忽略主仓库拉取，仅更新子模块。\")", ".log(\"No tracking branch found. Ignoring push of main repo and updating submodules only.\")": ".log(\"未找到跟踪分支。忽略主仓库推送，仅更新子模块。\")", ".log(\"Something weird happened:\")": ".log(\"发生了奇怪的情况：\")", ".log(this.plg.manifest.name+\": Enabled line authoring.\")": ".log(this.plg.manifest.name+\": 已启用行作者信息。\")", ".log(this.plg.manifest.name+\": Disabled line authoring.\")": ".log(this.plg.manifest.name+\": 已禁用行作者信息。\")", ".log(\"loading \"+this.manifest.name+\" plugin: v\"+this.manifest.version)": ".log(\"正在加载 \"+this.manifest.name+\" 插件: v\"+this.manifest.version)", ".log(\"unloading \"+this.manifest.name+\" plugin\")": ".log(\"正在卸载 \"+this.manifest.name+\" 插件\")", ".log(\"Something weird happened. The 'checkRequirements' result is \"+i)": ".log(\"发生了奇怪的情况。'checkRequirements'的结果是 \"+i)", ".log(\"Pushing....\")": ".log(\"正在推送....\")", ".log(\"Pulling....\")": ".log(\"正在拉取....\")", ".log(\"Encountered network error, but already in offline mode\")": ".log(\"遇到网络错误，但已处于离线模式\")", ".error(\"This browser lacks typed array (Uint8Array)": ".error(\"此浏览器缺少类型化数组(Uint8Array)\")", ".error(\"Failed to parse lines, starting in 0!\")": ".error(\"解析行失败，从0开始！\")", ".error(\"Unknown state reached while processing groups of lines\",a,s,o)": ".error(\"处理行组时达到未知状态\",a,s,o)", "name:\"\",email:\"": "name:\"\",email:\"", "name:\"Source Control\"": "name:\"源代码控制\"", "name:\"Diff View\"": "name:\"差异查看\"", "name:\"Edit .gitignore\"": "name:\"编辑.gitignore\"", "name:\"Open source control view\"": "name:\"打开源代码控制视图\"", "name:\"Open file on GitHub\"": "name:\"在GitHub上打开文件\"", "name:\"Open file history on GitHub\"": "name:\"在GitHub上打开文件历史\"", "name:\"Commit-and-sync and then close Obsidian\"": "name:\"提交并同步然后关闭Obsidian\"", "name:\"Commit-and-sync with specific message\"": "name:\"使用特定消息提交并同步\"", "name:\"Commit staged\"": "name:\"提交已暂存\"", "name:\"Amend staged\"": "name:\"修改已暂存\"", "name:\"Commit staged with specific message\"": "name:\"使用特定消息提交已暂存\"", "name:\"Remove remote\"": "name:\"删除远程仓库\"", "name:\"Clone an existing remote repo\"": "name:\"克隆现有远程仓库\"", "name:\"List changed files\"": "name:\"列出更改的文件\"", "name:\"CAUTION: Discard all changes\"": "name:\"警告：丢弃所有更改\"", "text:\"Git is not ready. When all settings are correct you can configure commit-sync, etc.\"": "text:\"Git未就绪。当所有设置正确后，您可以配置提交同步等。\"", "text:\"File not found: \"": "text:\"未找到文件: \"", ".setText(`Debugging and logging:\nYou can always see the logs of this and every other plugin by opening the console with`)": ".setText(`调试和日志记录：\n您可以通过打开控制台随时查看此插件及所有其他插件的日志，使用`)", ".setText(\"Edit .gitignore\")": ".setText(\"编辑.gitignore\")", ".setText(`${this.deletion?\"Delete\":\"Discard\"} this file?`)": ".setText(`${this.deletion?\"删除\":\"丢弃\"}此文件？`)", ".setText(`Do you really want to ${this.deletion?\"delete\":\"discard the changes of\"} \"${this.filename}\"`)": ".setText(`您确定要${this.deletion?\"删除\":\"丢弃\"} \"${this.filename}\" 的更改吗`)", ".setName(\"Split timers for automatic commit and sync\")": ".setName(\"为自动提交和同步分开计时器\")", ".setName(`Auto ${i} interval (minutes)`)": ".setName(`自动 ${i} 间隔(分钟)`)", ".setName(`Auto ${i} after stopping file edits`)": ".setName(`停止文件编辑后自动 ${i}`)", ".setName(`Auto ${i} after latest commit`)": ".setName(`最新提交后自动 ${i}`)", ".setName(`Specify custom commit message on auto ${i}`)": ".setName(`在自动 ${i} 时指定自定义提交消息`)", ".setName(`Commit message on auto ${i}`)": ".setName(`自动 ${i} 的提交消息`)", ".setName(\"Commit message\")": ".setName(\"提交消息\")", ".setName(\"Commit message on manual commit\")": ".setName(\"手动提交的提交消息\")", ".setName(\"Preview commit message\")": ".setName(\"预览提交消息\")", ".setName(\"List filenames affected by commit in the commit body\")": ".setName(\"在提交正文中列出受影响的文件名\")", ".setName(\"Pull on startup\")": ".setName(\"启动时拉取\")", ".setName(\"Push on commit-and-sync\")": ".setName(\"提交并同步时推送\")", ".setName(\"Pull on commit-and-sync\")": ".setName(\"提交并同步时拉取\")", ".setName(\"Line author information\")": ".setName(\"行作者信息\")", ".setName(\"History view\")": ".set<PERSON>ame(\"历史记录视图\")", ".setName(\"Source control view\")": ".setName(\"源代码控制视图\")", ".setName(\"Automatically refresh source control view on file changes\")": ".setName(\"文件更改时自动刷新源代码控制视图\")", ".setName(\"Source control view refresh interval\")": ".setName(\"源代码控制视图刷新间隔\")", ".setName(\"Hide notifications for no changes\")": ".setName(\"无更改时隐藏通知\")", ".setName(\"Show stage/unstage button in file menu\")": ".setName(\"在文件菜单中显示暂存/取消暂存按钮\")", ".setName(\"Show the count of modified files in the status bar\")": ".setName(\"在状态栏中显示修改文件的数量\")", ".setName(\"Authentication/commit author\")": ".setName(\"认证/提交作者\")", ".setName(\"Username on your git server. E.g. your username on GitHub\")": ".setName(\"Git服务器上的用户名。例如GitHub用户名\")", ".setName(\"Author email for commit\")": ".setName(\"提交的作者邮箱\")", ".setName(\"Custom Git binary path\")": ".setName(\"自定义Git二进制路径\")", ".setName(\"Additional PATH environment variable paths\")": ".setName(\"额外的PATH环境变量路径\")", ".setName(\"Reload with new environment variables\")": ".setName(\"使用新环境变量重新加载\")", ".setName(\"Custom base path (Git repository path)\")": ".setName(\"自定义基本路径(Git仓库路径)\")", ".setName(\"Custom Git directory path (Instead of '.git')\")": ".set<PERSON>ame(\"自定义Git目录路径(替代'.git')\")", ".setName(\"Show commit authoring information next to each line\")": ".setName(\"在每行旁边显示提交作者信息\")", ".setName(\"Follow movement and copies across files and commits\")": ".setName(\"跟踪文件和提交间的移动和复制\")", ".setName(\"Show commit hash\")": ".setName(\"显示提交哈希\")", ".setName(\"Author name display\")": ".setName(\"作者名称显示\")", ".setName(\"Authoring date display\")": ".setName(\"作者日期显示\")", ".setName(\"Authoring date display timezone\")": ".setName(\"作者日期显示时区\")", ".setName(\"Oldest age in coloring\")": ".setName(\"颜色显示的最早时间\")", ".setName(\"Ignore whitespace and newlines in changes\")": ".setName(\"忽略更改中的空格和换行\")", ".setDesc(\"Enable to use one interval for commit and another for sync.\")": ".setDesc(\"启用可为提交和同步使用不同的间隔。\")", ".setDesc(`If turned on, sets last auto ${i} timestamp to the latest commit timestamp. This reduces the frequency of auto ${i} when doing manual commits.`)": ".setDesc(`如果开启，将最后自动${i}时间戳设置为最新提交时间戳。这会减少手动提交时自动${i}的频率。`)", ".setDesc(\"Pull changes every X minutes. Set to 0 (default) to disable.\")": ".setDesc(\"每X分钟拉取更改。设置为0(默认)以禁用。\")", ".setDesc(\"You will get a pop up to specify your message.\")": ".setDesc(\"您将收到弹出窗口以指定消息。\")", ".setDesc(\"Available placeholders: {{date}} (see below), {{hostname}} (see below), {{numFiles}} (number of changed files in the commit) and {{files}} (changed files in commit message).\")": ".setDesc(\"可用占位符：{{date}}(见下方)，{{hostname}}(见下方)，{{numFiles}}(提交中更改文件数)和{{files}}(提交消息中的更改文件)。\")", ".setDesc(\"Decide how to integrate commits from your remote branch into your local branch.\")": ".setDesc(\"决定如何将远程分支的提交集成到本地分支。\")", ".setDesc(\"Automatically pull commits when Obsidian starts.\")": ".setDesc(\"Obsidian启动时自动拉取提交。\")", ".setDesc(\"Commit-and-sync with default settings means staging everything -> committing -> pulling -> pushing. Ideally this is a single action that you do regularly to keep your local and remote repository in sync.\")": ".setDesc(\"使用默认设置的提交并同步意味着暂存所有内容->提交->拉取->推送。理想情况下，这是您定期执行的单个操作，以保持本地和远程仓库同步。\")", ".setDesc(\"Show the author of the commit in the history view.\")": ".setDesc(\"在历史记录视图中显示提交的作者。\")", ".setDesc(\"Show the date of the commit in the history view. The {{date}} placeholder format is used to display the date.\")": ".setDesc(\"在历史记录视图中显示提交的日期。使用{{date}}占位符格式显示日期。\")", ".setDesc(\"Milliseconds to wait after file change before refreshing the Source Control View.\")": ".setDesc(\"文件更改后等待刷新源代码控制视图的毫秒数。\")", ".setDesc(\"Disable notifications for git operations to minimize distraction (refer to status bar for updates). Errors are still shown as notifications even if you enable this setting.\")": ".setDesc(\"禁用Git操作的通知以最小化干扰(参考状态栏获取更新)。即使启用此设置，错误仍会显示为通知。\")", ".setDesc(\"Don't show notifications when there are no changes to commit or push.\")": ".setDesc(\"当没有更改可提交或推送时，不显示通知。\")", ".setDesc(\"Obsidian must be restarted for the changes to take affect.\")": ".setDesc(\"Obsidian必须重新启动才能使更改生效。\")", ".setDesc(\"These settings usually don't need to be changed, but may be requried for special setups.\")": ".setDesc(\"这些设置通常不需要更改，但可能需要用于特殊设置。\")", ".setDesc('\"Commit-and-sync\" and \"pull\" takes care of submodules. Missing features: Conflicted files, count of pulled/pushed/committed files. Tracking branch needs to be set for each submodule.')": ".setDesc('\"提交并同步\"和\"拉取\"会处理子模块。缺少的功能：冲突文件，拉取/推送/提交文件的计数。需要为每个子模块设置跟踪分支。')", ".setDesc(\"Whenever a checkout happens on the root repository, recurse the checkout on the submodules (if the branches exist).\")": ".setDesc(\"每当根仓库发生检出时，在子模块上递归检出(如果分支存在)。\")", ".setDesc(\"Use each line for a new environment variable in the format KEY=VALUE .\")": ".setDesc(\"为每个新环境变量使用格式KEY=VALUE，每行一个。\")", ".setDesc(\"Use each line for one path\")": ".setDesc(\"每行一个路径\")", ".setDesc(\"Removing previously added environment variables will not take effect until Obsidian is restarted.\")": ".setDesc(\"删除先前添加的环境变量在Obsidian重启前不会生效。\")", ".setDesc(`\n            Sets the relative path to the vault from which the Git binary should be executed.\n             Mostly used to set the path to the Git repository, which is only required if the Git repository is below the vault root directory. Use \"\\\\\" instead of \"/\" on Windows.\n            `)": ".setDesc(`\n            设置从仓库执行Git二进制文件的相对路径。\n             主要用于设置Git仓库的路径，仅当Git仓库位于保险库根目录下方时才需要。在Windows上使用\"\\\\\"代替\"/\"。\n            `)", ".setDesc('Requires restart of Obsidian to take effect. Use \"\\\\\" instead of \"/\" on Windows.')": ".setDesc('需要重启Obsidian才能生效。在Windows上使用\"\\\\\"代替\"/\"。')", ".setDesc(\"Disables the plugin on this device. This setting is not synced.\")": ".setDesc(\"在此设备上禁用插件。此设置不会同步。\")", ".setDesc(\"If you like this Plugin, consider donating to support continued development.\")": ".setDesc(\"如果您喜欢此插件，请考虑捐赠以支持持续开发。\")", ".setDesc(\"Only available on desktop currently.\")": ".setDesc(\"目前仅桌面端可用。\")", ".setDesc(\"If and how the author is displayed\")": ".setDesc(\"是否以及如何显示作者\")", ".setDesc(\"If and how the date and time of authoring the line is displayed\")": ".setDesc(\"是否以及如何显示行的创作日期和时间\")", ".setPlaceholder(\"vault backup: {{date}}\")": ".setPlaceholder(\"保险库备份: {{date}}\")", ".setPlaceholder(\"GIT_DIR=/path/to/git/dir\")": ".setPlaceholder(\"GIT_DIR=/path/to/git/dir\")", ".setPlaceholder(\"directory/directory-with-git-repo\")": ".setPlaceholder(\"directory/directory-with-git-repo\")", ".setPlaceholder(\"Type your message and select optional the version with the added date.\")": ".setPlaceholder(\"输入您的消息并选择可选添加日期的版本。\")", ".setPlaceholder(\"Not supported files will be opened by default app!\")": ".setPlaceholder(\"不支持的文件将由默认应用打开！\")", ".setTitle(\"Git: Unstage\")": ".setTitle(\"Git: 取消暂存\")", ".setTitle(\"Git: Add to .gitignore\")": ".setTitle(\"Git: 添加到.gitignore\")", ".innerText=`Color for ${i} commits`": ".innerText=`${i} 次提交的颜色`", "Notice(`Running '${n}'...`,999999)": "Notice(`正在执行 '${n}'...`,999999)", ".log('Unhandled error in \"FileSystem.exists()": ".log('在\"FileSystem.exists()\"中未处理的错误')", ".log(`Did not delete ${w} because directory is not empty`)": ".log(`未删除 ${w} 因为目录非空`)", ".log(\"Retry watch for ask pass\")": ".log(\"重试观察ask pass\")", ".log(`Git not found in PATH. Checking standard installation path(${Vu})": ".log(`在PATH中未找到Git。正在检查标准安装路径(${Vu})`)", ".error(\"Last ten effects were: \",dc.slice(-10)": ".error(\"最后十个效果是: \",dc.slice(-10)", "name:\"Diff view\"": "name:\"差异视图\"", "name:\"Add file to .gitignore\"": "name:\"将文件添加到.gitignore\"", "name:\"Raw command\"": "name:\"原始命令\"", "description:`${st} '${oa(ee)}' of ${T}`": "description:`${st} '${oa(ee)}' 的 ${T}`", ".setName(\"Diff view style\")": ".setName(\"差异视图样式\")", ".setDesc(`${n.settings.differentIntervalCommitAndPush?\"Commit\":\"Commit and sync\"} changes every X minutes. Set to 0 (default) to disable. (See below setting for further configuration!)`)": ".setDesc(`${n.settings.differentIntervalCommitAndPush?\"提交\":\"提交并同步\"}更改每X分钟。设置为0(默认)以禁用。(参见下方设置进行进一步配置！)`)", ".setDesc(`Most of the time you want to push after committing. Turning this off turns a commit-and-sync action into commit ${n.settings.pullBeforePush?\"and pull \":\"\"}only. It will still be called commit-and-sync.`)": ".setDesc(`大多数情况下您希望在提交后推送。关闭此选项会将提交并同步操作变为仅提交${n.settings.pullBeforePush?\"和拉取\":\"\"}。它仍将被称为提交并同步。`)", ".setDesc(`On commit-and-sync, pull commits as well. Turning this off turns a commit-and-sync action into commit ${n.settings.disablePush?\"\":\"and push \"}only.`)": ".setDesc(`在提交并同步时，也拉取提交。关闭此选项会将提交并同步操作变为仅${n.settings.disablePush?\"\":\"提交和推送\"}。`)", ".setDesc('Set the style for the diff view. Note that the actual diff in \"Split\" mode is not generated by Git, but the editor itself instead so it may differ from the diff generated by Git. One advantage of this is that you can edit the text in that view.')": ".setDesc('设置差异视图的样式。注意\"拆分\"模式中的实际差异不是由Git生成，而是由编辑器本身生成，因此可能与Git生成的差异不同。这样做的一个优势是您可以在该视图中编辑文本。')", ".setPlaceholder(\"1y\")": ".setPlaceholder(\"1年\")", ".setTitle(\"Open in default app\")": ".setTitle(\"在默认应用中打开\")", ".setTitle(\"Show in system explorer\")": ".setTitle(\"在系统资源管理器中显示\")", ".setDesc(\"在较慢的机器上，这可能会导致延迟。如果出现这种情况，只需禁用此选项。\")": ".setDesc(\"在较慢的机器上，这可能会导致延迟。如果发生这种情况，只需禁用此选项。\")"}}