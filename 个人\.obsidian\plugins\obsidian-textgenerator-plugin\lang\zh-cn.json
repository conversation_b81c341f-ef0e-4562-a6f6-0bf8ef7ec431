{"manifest": {"translationVersion": 1740491101975, "pluginVersion": "0.7.47"}, "description": {"original": "Text generation using AI", "translation": "Text generation using AI"}, "dict": {".setButtonText(\"Submit\")": ".setButtonText(\"提交\")", ".setName(\"Path\")": ".setName(\"路径\")", ".setPlaceholder(\"Path\")": ".setPlaceholder(\"路径\")", ".setName(\"Max number of tokens\")": ".setName(\"最大 token 数\")", ".setDesc(\"The max number of the tokens that will be generated (1000 tokens ~ 750 words)\")": ".setDesc(\"生成内容的最大token数（1000个token约合750个单词）\")", "Notice(\"processing Initialization...\",3e5)": "Notice(\"正在处理初始化…\",3e5)", "Notice(\"\\u{1F534}Error: File already exists. Choose another path.\")": "Notice(\"u{1F534}Error：文件已存在。选择其它路径。\")", "Notice(\"Generated Text copied to clipboard\")": "Notice(\"生成的文本已复制到剪贴板\")", "Notice(\"Auto Suggestion is on!\")": "Notice(\"自动建议已打开！\")", "Notice(\"Auto Suggestion is off!\")": "Notice(\"自动建议已关闭！\")", "Notice(`Selected ${e.name}`)": "Notice(`已选择${e.name}`)", "Notice(`Selected ${e.id}`)": "Notice(`已选择${e.id}`)", "Notice(`Set Max Tokens to ${I}!`)": "Notice(`将最大令牌设置为${I}！`)", "Notice(`Set Max Tokens to ${r}!`)": "Notice(`将最大令牌设置为${r}！`)", "name:\"API Key\"": "name:\"API密钥\"", "name:\"Model\"": "name:\"模型\"", "name:\"API Version\"": "name:\"API 版本\"", "name:\"Language\"": "name:\"语言\"", "name:\"Streaming\"": "name:\"流式传输\"", "name:\"Display errors in the editor\"": "name:\"编辑器中的显示错误\"", "name:\"Show Status in StatusBar\"": "name:\"在状态栏中显示状态\"", "name:\"Output generated text to blockquote\"": "name:\"将生成的文本输出到引用块\"", "name:\"Free cursor on streaming\"": "name:\"流式传输中的自由光标\"", "name:\"Experimentation Features\"": "name:\"实验性功能\"", "name:\"include Attachments\"": "name:\"包括附件\"", "name:\"Templates Path\"": "name:\"模板路径\"", "name:\"TextGenerator Path\"": "name:\"文本生成器路径\"", "name:\"Reload the plugin\"": "name:\"重新加载插件\"", "name:\"Resets all settings to default\"": "name:\"将所有设置重置为默认值\"", "name:\"User\"": "name:\"用户\"", "name:\"Provider Profile\"": "name:\"提供商简介\"", "name:\"Name\"": "name:\"名称\"", "name:\"Max tokens\"": "name:\"最大令牌数\"", "name:\"Temperature\"": "name:\"温度\"", "name:\"Frequency Penalty\"": "name:\"频率惩罚\"", "name:\"Timeout\"": "name:\"超时\"", "name:\"Prefix\"": "name:\"前缀\"", "name:\"Endpoint\"": "name:\"端点\"", "name:\"Advance mode\"": "name:\"高级模式\"", "name:\"Streamable\"": "name:\"可流式传输\"", "name:\"CORS Bypass\"": "name:\"绕过 CORS\"", "name:\"Enable Custom Headers\"": "name:\"启用自定义头\"", "name:\"Custom default generation prompt\"": "name:\"自定义默认生成提示\"", "name:\"Enable generate title instruct\"": "name:\"启用生成标题指令\"", "name:\"TG Selection Limiter(regex)\"": "name:\"TG选择限制器（正则表达式）\"", "name:\"{{context}} Variable Template\"": "name:\"{{context}} 变量模板\"", "name:\"Allow scripts\"": "name:\"允许脚本\"", "name:\"PDF Extractor\"": "name:\"PDF 提取器\"", "name:\"Web Page Extractor\"": "name:\"网页提取器\"", "name:\"Audio Extractor (Whisper)\"": "name:\"音频提取器（Whisper）\"", "name:\"Image Extractor\"": "name:\"图像提取器\"", "name:\"Embedded Image Extractor\"": "name:\"嵌入式图像提取器\"", "name:\"Youtube Extractor\"": "name:\"Youtube 提取器\"", "name:\"Enable/Disable\"": "name:\"启用/禁用\"", "name:\"Inline Suggestions\"": "name:\"内联建议\"", "name:\"Show In Markdown\"": "name:\"以 Markdown 显示\"", "name:\"Trigger Phrase\"": "name:\"触发短语\"", "name:\"Override Trigger\"": "name:\"覆盖触发器\"", "name:\"Delay milliseconds for trigger\"": "name:\"触发延迟时间（毫秒）\"", "name:\"Number of Suggestions\"": "name:\"建议数\"", "name:\"Stop Phrase\"": "name:\"停止短语\"", "name:\"Allow Suggest in new Line\"": "name:\"允许在新行中提出建议\"", "name:\"Show/Hide Auto-suggest status in Status Bar\"": "name:\"在状态栏中显示/隐藏自动建议状态\"", "name:\"Custom auto-suggest Prompt\"": "name:\"自定义自动建议提示\"", "name:\"Custom Provider\"": "name:\"自定义提供者\"", "name:\"Slash suggestions\"": "name:\"斜杠建议\"", "name:\"Chose LLM\"": "name:\"已选 LLM\"", "name:\"Keys encryption\"": "name:\"密钥加密\"", "name:\"Getting started\"": "name:\"开始\"", "name:\"Available models\"": "name:\"可用模型\"", "name:\"Base Path\"": "name:\"基本路径\"", "name:\"Create account OpenAI\"": "name:\"创建 OpenAI 帐户\"", "name:\"API documentation\"": "name:\"API 文档\"", "name:\"You can use LM Studio\"": "name:\"您可以使用 LM Studio\"", "name:\"more information\"": "name:\"更多信息\"", "name:\"Create account mistralAI\"": "name:\"创建 mistralAI 帐户 \"", "name:\"Setup API token\"": "name:\"设置 API 令牌\"", "name:\"More information\"": "name:\"更多信息\"", "name:\"Anthropic api key\"": "name:\"Anthropic api 密钥\"", "name:\"How to use locally hosted ollama (Discord Link)\"": "name:\"如何使用本地托管的 ollama（Discord 链接）\"", "name:\"Endpoint (optional)\"": "name:\"端点（可选）\"", "name:\"Instance Name\"": "name:\"实例名称\"", "name:\"Deployment name\"": "name:\"部署名称\"", "name:\"model\"": "name:\"模型\"", "name:\"Api version\"": "name:\"<PERSON><PERSON> <PERSON>\"", "name:\"Api Key\"": "name:\"API 密钥\"", "name:\"Get API KEY\"": "name:\"获取 API 密钥\"", "name:\"Generate Text!\"": "name:\"生成文本！\"", "name:\"Generate Text (use Metadata))!\"": "name:\"生成文本（使用元数据）！\"", "name:\"Templates: Generate & Insert\"": "name:\"模板：生成 & 插入\"", "name:\"Templates: Generate & Copy To Clipboard \"": "name:\"模板：生成 & 复制到剪贴板 \"", "name:\"Templates: Generate & Create Note\"": "name:\"模板：生成 & 创建笔记\"", "name:\"Templates (Batch): From Search Results\"": "name:\"模板（批处理）：来自搜索结果\"", "name:\"Templates: Insert Template\"": "name:\"模板：插入模板\"", "name:\"Templates: Insert & Create Note\"": "name:\"模板：插入 & 创建笔记\"", "name:\"Show modal From Template\"": "name:\"模板弹窗\"", "name:\"Open Template as Tool\"": "name:\"将模板作为工具打开\"", "name:\"Open Template Playground\"": "name:\"打开模板广场\"", "name:\"Set max_tokens\"": "name:\"设置 max_tokens\"", "name:\"Choose a LLM\"": "name:\"选择 LLM\"", "name:\"Choose a Model\"": "name:\"选择模型\"", "name:\"Template Packages Manager\"": "name:\"模板包管理器\"", "name:\"Create a Template\"": "name:\"创建模板\"", "name:\"Generate a Title\"": "name:\"生成标题\"", "name:\"Turn on or off the auto suggestion\"": "name:\"打开或关闭自动建议\"", "name:\"Estimate tokens for the current document\"": "name:\"估计当前文档的令牌\"", "name:\"Estimate tokens for a Template\"": "name:\"估计模板的令牌\"", "name:\"Text Extractor Tool\"": "name:\"文本提取工具\"", "name:\"Stop Stream\"": "name:\"停止输出流\"", "name:\"reload Plugin\"": "name:\"重新加载插件\"", "description:\"Pure Javascript Multilingual OCR\"": "description:\"纯 JS 多语言OCR\"", "description:\"Meta-schema for $data reference (JSON AnySchema extension proposal)\"": "description:\"$data 引用的 Meta-schema 元模式（JSON AnySchema 的扩展提案）\"", "description:\"Generative Language API client for Node.js\"": "description:\"Node.js 的生成语言 API 客户端\"", "description:\"A simple common HTTP client specifically for Google APIs and services.\"": "description:\"一个简单的通用 HTTP 客户端，专门用于Google API和服务。\"", "description:\"Google APIs Authentication Client Library for Node.js\"": "description:\"Google API 身份验证 Node.js 客户端库\"", "description:\"Google API Extensions\"": "description:\"Google API 扩展\"", "description:\"Extracts the relevant information from the passage.\"": "description:\"从文章中提取相关信息。\"", "description:\"Output formatter. Should always be used to format your response to the user.\"": "description:\"输出格式器。应始终用于格式化你对用户的响应。\"", "description:\"Output formatter. Should always be used to format your response to the user\"": "description:\"输出格式器。应始终用于格式化你对用户的响应\"", "description:\"Predicts the next user message, along with insights.\"": "description:\"预测下一条用户消息以及见解。\"", "description:\"Concise reasoning about the users internal mental state.\"": "description:\"用户内在心理状态的简明推理。\"", "description:\"Your prediction on how they will respond to the AI's most recent message.\"": "description:\"您对用户将如何响应 AI 最新消息的预测。\"", "description:\"A concise list of any additional insights that would be useful to improve prediction.\"": "description:\"改进预测所需的额外洞察简明列表。\"", "description:\"Generates violations, errors and differences between the predicted user response, and the actual response.\"": "description:\"生成预测用户响应与实际响应之间的违规、错误及差异。\"", "description:\"How was the predication violated?\"": "description:\"预测是如何被违反的？\"", "description:\"Explanations of how the prediction was violated and why\"": "description:\"解释预测是如何被违反的以及原因\"", "description:\"default to openai\"": "description:\"默认为 openai\"", "description:\"default to openai provider's apikey\"": "description:\"默认为 openai 提供程序的 apikey\"", "description:\"default to whisper-1\"": "description:\"默认为 whisper-1\"", "description:\"Api version (Azure only)\"": "description:\"Api 版本（仅限 Azure）\"", "description:\"default to (none)\"": "description:\"默认为（none）\"", "description:\"Enable streaming if supported by the provider\"": "description:\"若提供商支持流式传输，则启用该功能\"", "description:\"If you want to see the errors in the editor\"": "description:\"想要在编辑器中查看错误的话\"", "description:\"Show information in the Status Bar\"": "description:\"在状态栏中显示信息\"", "description:\"Distinguish between AI generated text and typed text using a blockquote\"": "description:\"通过块引用区分AI生成的文本与手动输入的文本\"", "description:\"Note that it might result in weird bugs, the auto-scrolling might not work\"": "description:\"请注意，这可能会导致一些奇怪的错误，自动滚动功能可能无法正常工作\"", "description:\"This adds experiment features, which might not be stable yet\"": "description:\"增加实验功能，这些功能可能还不稳定\"", "description:\"EXPERIMENTAL: adds the images that are referenced in the request, IT MIGHT CONSUME ALOT OF TOKENS\"": "description:\"实验性功能：添加请求中引用的图片（注意：此操作可能消耗大量 TOKEN）\"", "description:\"Path for Templates directory\"": "description:\"模板目录的路径\"", "description:\"Path To Folder that Text Generator can put Backups,generations...etc into\"": "description:\"文本生成器用于存储备份文件、生成内容等数据的文件夹路径\"", "description:\"Some changes might require you to reload the plugins\"": "description:\"某些更改可能需要您重新加载插件\"", "description:\"It will delete all your configurations\"": "description:\"它将删除您的所有配置\"", "description:\"This is the account settings to manage bought items from the package-manager\"": "description:\"此账户设置用于管理通过包管理器购买的已购项目\"", "description:\"Change name of the profile\"": "description:\"更改配置文件的名称\"", "description:\"The maximum number of tokens to generate in the chat completion. The total length of input tokens and generated tokens is limited by the model's context length. (1000 tokens ~ 750 words)\"": "description:\"聊天补全中允许生成的最大 token 数。输入 token 与生成 token 的总长度不得超过模型的上下文长度限制（1000个标记约对应750个英文单词）。\"", "description:\"What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic.\"": "description:\"采样温度参数，取值范围为 0 至 2 。较高值（如 0.8）会增强输出的随机性，而较低值（如 0.2 ）则会使输出更加集中且具有确定性。\"", "description:\"Number between -2.0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model's likelihood to talk about new topics.\"": "description:\"取值范围为 -2.0 到 2.0 。正值会根据新 token 是否已出现在当前文本中对其进行惩罚，从而提高模型讨论新话题的可能性。\"", "description:\"Timeout in milliseconds. If the request takes longer than the timeout, the request will be aborted. (default: 5000ms)\"": "description:\"超时时间（毫秒）。若请求耗时超过设定阈值，将强制中止请求（默认值：5000毫秒）\"", "description:\"Prefix to add to the beginning of the completion (default: '\\\\n\\\\n')\"": "description:\"在生成内容开头添加的前缀（默认值：'\\\\n\\\\n'）\"", "description:\"enable this only if you get blocked by CORS, in mobile this will result in failure in some functions\"": "description:\"仅在因跨域资源共享（CORS）被阻止时启用此选项，在移动端启用可能会导致部分功能失效\"", "description:\"Include the title of the active document in the considered context.\"": "description:\"在相关上下文中包含激活文档的标题。\"", "description:\"Include starred blocks in the considered context.\"": "description:\"在相关上下文中包含带星号的块。\"", "description:\"Include frontmatter\"": "description:\"包含前置元数据（frontmatter）\"", "description:\"Include headings with their content.\"": "description:\"包括标题及其内容。\"", "description:\"Include the content of internal md links on the page.\"": "description:\"在页面中包含内部 MD 链接的内容。\"", "description:\"Include paragraphs from mentions (linked, unliked).\"": "description:\"包括提及的段落（链接、未链接）。\"", "description:\"Include Obsidian Highlights.\"": "description:\"包括 Obsidian 高亮（Highlights）。\"", "description:\"Include Extracted Information\"": "description:\"包括提取的信息\"", "description:\"Make clipboard available for templates\"": "description:\"使剪贴板可用于模板\"", "description:\"You can customize {{context}} variable\"": "description:\"您可以自定义 {{context}} 变量\"", "description:\"You can customize generate title prompt\"": "description:\"您可自定义标题生成提示词\"", "description:\"tg_selection stopping character. Empty means disabled. Default: ^\\\\*\\\\*\\\\*\"": "description:\"tg_selection 停止字符。空表示禁用。默认值：^\\\\*\\\\*\\\\*\"", "description:\"Template for {{context}} variable\"": "description:\"{{context}} 变量的模板\"", "description:\"Only enable this if you trust the authors of the templates, or know what you're doing.\"": "description:\"只有当您信任模板的作者或知道自己在做什么时，才能启用此功能。\"", "description:\"Enable or disable PDF extractor.\"": "description:\"启用或禁用 PDF 提取器。\"", "description:\"Enable or disable web page extractor.\"": "description:\"启用或禁用网页提取器。\"", "description:\"Enable or disable audio extractor using Whisper OpenAI ($0.006 / minute) supports multi-languages and accepts a variety of formats (m4a, mp3, mp4, mpeg, mpga, wav, webm).\"": "description:\"使用 Whisper OpenAI 启用或禁用音频提取器（0.006美元/分钟）支持多语言，并接受各种格式（m4a、mp3、mp4、mpeg、mpga、wav、webm）。\"", "description:\"Enable or disable Image Extractor from URL\"": "description:\"从 URL 启用或禁用图像提取器\"", "description:\"Enable or disable Embedded Image Extractor.\"": "description:\"启用或禁用嵌入式图像提取器。\"", "description:\"Enable or disable Youtube extractor.\"": "description:\"启用或禁用 Youtube 提取器。\"", "description:\"Enable or disable auto-suggest.\"": "description:\"启用或禁用自动建议。\"", "description:\"Shows the suggestions text in the editor (EXPERIMENTAL)\"": "description:\"在编辑器中显示建议文本（实验）\"", "description:\"Shows the suggestions text compiled as markdown, may shows weird spaces at the begining and end (EXPERIMENTAL)\"": "description:\"显示编译为 markdown 的建议文本，可能在开头和结尾显示奇怪的空格（实验性）\"", "description:\"Trigger Phrase (default: *double space*)\"": "description:\"触发短语（默认：*双空格*）\"", "description:\"Overrides the trigger when suggestion is accepted (default: *single space*)\"": "description:\"接受建议时覆盖触发器（默认值：*单空格*）\"", "description:\"Enter the number of suggestions to generate. Please note that increasing this value may significantly increase the cost of usage with GPT-3.\"": "description:\"输入要生成的建议数。请注意，增加此值可能会显著增加 GPT-3 的使用成本。\"", "description:\"Enter the stop phrase to use for generating auto-suggestions. The generation will stop when the stop phrase is found. (Use a space for words, a period for sentences, and a newline for paragraphs.)\"": "description:\"输入用于生成自动建议的停止短语。当找到停止短语时，生成将停止。（单词用空格，句子用句号，段落用换行符。）\"", "description:\"This will allow it to run at the beggining of a new line\"": "description:\"这将使它能够在新生产线开始时运行\"", "description:\"You can customize auto-suggest prompt\"": "description:\"您可以自定义自动建议提示\"", "description:`use a different LLM provider than the one you're generating with.          \n            make sure to setup the llm provider in the LLM Settings, before use.`": "description:`请使用与当前生成所用不同的LLM提供商。         \n            使用前，请确保在 LLM 设置中完成该提供商的配置。`", "description:\"Trigger Phrase (default: */*)\"": "description:\"触发短语（默认值：*/*）\"", "description:\"Enable encrypting keys, this could cause incompatibility with mobile devices\"": "description:\"启用加密密钥，这可能会导致与移动设备不兼容\"", "description:\"Make sure it supports CORS\"": "description:\"确保它支持CORS\"", "description:\"Text generator is a handy plugin that helps you generate text content using GPT-3 (OpenAI).\"": "description:\"Text generator 是一个方便的插件，可以帮助您使用 GPT-3（OpenAI）生成文本内容。\"", "text:\"thought\"": "text:\"想法\"", "text:\"Max number of tokens\"": "text:\"最大令牌数\"", "text:`New Document Path ${this.info?.title?`": "text:`新的文档路径 ${this.info?.title?`", "text:\"Content\"": "text:\"内容\"", ".setTitle(\"Pin\")": ".setTitle(\"大头针\")", ".setTitle(\"Popout\")": ".setTitle(\"弹窗\")", ".setTitle(\"Create Template\")": ".setTitle(\"创建模板\")", ".setTitle(\"Source Template\")": ".setTitle(\"源模板\")", ".setTitle(\"Generate\")": ".setTitle(\"生成\")"}}