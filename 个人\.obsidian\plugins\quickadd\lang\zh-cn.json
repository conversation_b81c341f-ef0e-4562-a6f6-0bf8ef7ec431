{"manifest": {"translationVersion": 1744799982062, "pluginVersion": "1.13.2"}, "description": {"original": "Quickly add new pages or content to your vault.", "translation": "Quickly add new pages or content to your vault."}, "dict": {"Notice(\"Choice name is invalid.\")": "Notice(\"选项名称无效。\")", "Notice(g0(\"starting\",\"\")": "Notice(g0(\"开始“，”\")", "Notice(this.formatOutputString(Z)": "Notice(this.formatOutputString(Z)", ".log(a)": ".log(a)", "alog(G,b,Z)": "alog(G,b,Z)", "alog(G,I,l)": "alog(G,I,l)", ".log(this.formatOutputString(Z)": ".log(this.formatOutputString(Z)", ".log(\"Loading QuickAdd\")": ".log(\"加载快速添加\")", ".log(\"Test QuickAdd (dev)": ".log(\"Test QuickAdd (dev)", ".log(\"Unloading QuickAdd\")": ".log(\"卸载快速添加\")", ".error(\"`import faIconName from '@fortawesome/package-name/faIconName` not supported - Please use `import { faIconName } from '@fortawesome/package-name/faIconName'` instead\")": ".error(\"`不支持从“@fortawesome/package-name/faIconName”导入faIconName-请改为从“@fortawesome/paackage-name/faIkonName”导入{faIconName}\")", ".error(this.formatOutputString(Z)": ".error(this.formatOutputString(Z)", "name:\"applyStyles\"": "name:\"应用程序样式\"", "name:\"arrow\"": "name:\"箭\"", "name:\"computeStyles\"": "name:\"计算机样式\"", "name:\"eventListeners\"": "name:\"预约前1小时\"", "name:\"flip\"": "name:\"快速翻转\"", "name:\"hide\"": "name:\"隐藏\"", "name:\"offset\"": "name:\"抵消\"", "name:\"popperOffsets\"": "name:\"popper偏移\"", "name:\"preventOverflow\"": "name:\"preventOverflow\"", "name:\"sameWidth\"": "name:\"相同宽度\"", "name:\"\"},this.modelParameters={temperature:1,top_p:1,frequency_penalty:0,presence_penalty:0}}};function X5(c){let G=[],I=l=>{l.forEach(b=>{b.type===\"": "name:\"“}，this.model参数={温度：1，top_p:1，频率_惩罚：0，存在_惩罚：0}}；函数X5（c）{let G=[]，I=l=>{l.forEach（b=>{b.type===\"", "name:\"OpenAI\"": "name:\"OpenAI\"", "name:\"text-davinci-003\"": "name:\"text-davinci-003\"", "name:\"gpt-3.5-turbo\"": "name:\"gpt-3.5涡轮增压\"", "name:\"gpt-3.5-turbo-16k\"": "name:\"gpt-3.5涡轮-16k\"", "name:\"gpt-3.5-turbo-1106\"": "name:\"gpt-3.5涡轮-1106\"", "name:\"gpt-4\"": "name:\"gpt-4\"", "name:\"gpt-4-32k\"": "name:\"gpt-4-32k\"", "name:\"gpt-4-1106-preview\"": "name:\"gpt-4-1106预览\"", "name:\"gpt-4-turbo\"": "name:\"gpt-4涡轮增压器\"", "name:\"gpt-4o\"": "name:\"gpt-4o\"", "name:\"gpt-4o-mini\"": "name:\"gpt-4o-mini\"", "name:\"Run QuickAdd\"": "name:\"运行快速添加\"", "name:\"Reload QuickAdd (dev)\"": "name:\"重新加载QuickAdd（开发）\"", "name:\"Test QuickAdd (dev)\"": "name:\"测试快速添加（开发）\"", "name:\"Test\"": "name:\"测试\"", "description:\"Migrate to macro ID from embedded macro in macro choices.\"": "description:\"从宏选项中的嵌入式宏迁移到宏ID。\"", "description:\"Use QuickAdd template folder instead of Obsidian templates plugin folder / Templater templates folder.\"": "description:\"使用QuickAdd模板文件夹，而不是Obsidian模板插件文件夹/Templater模板文件夹。\"", "description:\"'Increment file name' setting moved to 'Set default behavior if file already exists' setting\"": "description:\"“增量文件名”设置移动到“如果文件已存在，则设置默认行为”设置\"", "description:\"Mutual exclusion of insertAfter and writeToBottomOfFile settings. If insertAfter is enabled, writeToBottomOfFile is disabled. To support changes in settings UI.\"": "description:\"insertAfter和writeToBottomOfFile设置的互斥。如果启用insertAfter，则禁用writeToBottomOfFile。支持设置UI中的更改。\"", "description:\"Set version to the current plugin version.\"": "description:\"将版本设置为当前插件版本。\"", "description:\"Add default AI providers to the settings.\"": "description:\"将默认AI提供程序添加到设置中。\"", "text:\"reference\"": "text:\"参考\"", "text:\"Choose folder when creating a new note\"": "text:\"创建新笔记时选择文件夹\"", "text:`${this.settings.name} Settings`": "text:`$｛this.settings.name｝设置`", "text:\"Macro Manager\"": "text:\"宏管理器\"", "text:\"AI Assistant Settings\"": "text:\"AI助手设置\"", "text:\"QuickAdd Settings\"": "text:\"快速添加设置\"", "text:\"Fetching release notes...\"": "text:\"正在获取发行说明。。。\"", ".setButtonText(\"No\")": ".setButtonText(\"不\")", ".setButtonText(\"Yes\")": ".setButtonText(\"是的\")", ".setButtonText(\"Add\")": ".setButtonText(\"添加\")", ".setButtonText(\"OK\")": ".setButtonText(\"好的\")", ".setButtonText(\"Submit\")": ".setButtonText(\"提交\")", ".setButtonText(\"Delete\")": ".setButtonText(\"删去\")", ".setButtonText(\"Configure\")": ".setButtonText(\"配置\")", ".setButtonText(\"Add macro\")": ".setButtonText(\"添加宏\")", ".setButtonText(\"Add Provider\")": ".setButtonText(\"添加提供者\")", ".setButtonText(\"Edit\")": ".setButtonText(\"编辑\")", ".setButtonText(\"Add Model\")": ".setButtonText(\"添加模型\")", ".setButtonText(\"Cancel\")": ".setButtonText(\"取消\")", ".setButtonText(\"Save\")": ".setButtonText(\"保存\")", ".setButtonText(\"Edit Providers\")": ".setButtonText(\"编辑提供者\")", ".setName(\"Template Path\")": ".setName(\"模板路径\")", ".setName(\"File Name Format\")": ".set<PERSON>ame(\"文件名格式\")", ".setName(\"Create in folder\")": ".setName(\"在文件夹中创建\")", ".setName(\"Include subfolders\")": ".setName(\"包含子文件夹\")", ".setName(\"Create in same folder as active file\")": ".setName(\"在与活动文件相同的文件夹中创建\")", ".setName(\"Append link\")": ".setName(\"附加链接\")", ".setName(\"Set default behavior if file already exists\")": ".setName(\"如果文件已存在，则设置默认行为\")", ".setName(\"Open\")": ".setName(\"打开\")", ".setName(\"New split\")": ".set<PERSON>ame(\"新拆分\")", ".setName(\"Focus new pane\")": ".setName(\"聚焦新窗格\")", ".setName(\"Capture To\")": ".setName(\"捕获到\")", ".setName(\"Write to bottom of file\")": ".setName(\"写入文件底部\")", ".setName(\"Task\")": ".setName(\"任务\")", ".setName(\"Insert after\")": ".setName(\"在之后插入\")", ".setName(\"Insert at end of section\")": ".setName(\"在章节末尾插入\")", ".setName(\"Consider subsections\")": ".setName(\"考虑各小节\")", ".setName(\"Create line if not found\")": ".setName(\"如果未找到，则创建行\")", ".setName(\"Capture format\")": ".setName(\"捕获格式\")", ".setName(\"Create file if it doesn't exist\")": ".setName(\"如果文件不存在，则创建该文件\")", ".setName(\"Create file with given template.\")": ".setName(\"使用给定的模板创建文件。\")", ".setName(\"New Tab\")": ".setName(\"新标签页\")", ".setName(\"Prompt Template\")": ".setName(\"提示模板\")", ".setName(\"Model\")": ".setName(\"模型\")", ".setName(\"Output variable name\")": ".setName(\"输出变量名称\")", ".setName(\"System Prompt\")": ".setName(\"系统提示\")", ".setName(\"Show advanced settings\")": ".setName(\"显示高级设置\")", ".setName(\"Temperature\")": ".setName(\"温度\")", ".setName(\"Top P\")": ".set<PERSON>ame(\"顶部P\")", ".setName(\"Frequency Penalty\")": ".setName(\"频率惩罚\")", ".setName(\"Presence Penalty\")": ".setName(\"出席处罚\")", ".setName(\"Obsidian command\")": ".setName(\"黑曜石命令\")", ".setName(\"Editor commands\")": ".setName(\"编辑器命令\")", ".setName(\"User Scripts\")": ".setName(\"用户脚本\")", ".setName(\"Choices\")": ".setName(\"选择\")", ".setName(\"Providers\")": ".setName(\"提供商\")", ".setName(\"Name\")": ".set名称(\"Name\")", ".setName(\"Endpoint\")": ".setName(\"端点\")", ".setName(\"API Key\")": ".setName(\"API密钥\")", ".setName(\"Add Model\")": ".setName(\"添加模型\")", ".setName(\"Default Model\")": ".setName(\"默认模型\")", ".setName(\"Prompt Template Folder Path\")": ".setName(\"提示模板文件夹路径\")", ".setName(\"Show Assistant\")": ".setName(\"演出助理\")", ".setName(\"Default System Prompt\")": ".setName(\"默认系统提示\")", ".setName(\"Announce Updates\")": ".setName(\"宣布更新\")", ".setName(\"Use Multi-line Input Prompt\")": ".setName(\"使用多行输入提示\")", ".setName(\"Template Folder Path\")": ".setName(\"模板文件夹路径\")", ".setName(\"Disable AI & Online features\")": ".setName(\"Disable AI & Online features\")", ".setName(\"Show icon in sidebar\")": ".setName(\"在侧边栏中显示图标\")", ".setName(\"Result Joiner\")": ".setName(\"结果加入者\")", ".setName(\"Chunk Separator\")": ".setName(\"块状物分离器\")", ".setName(\"Max Chunk Tokens\")": ".setName(\"最大Chunk代币\")", ".setName(\"Merge Chunks\")": ".setName(\"合并块\")", ".setDesc(\"Path to the Template.\")": ".setDesc(\"模板的路径。\")", ".setDesc(\"Set the file name format.\")": ".setDesc(\"设置文件名格式。\")", ".setDesc(\"Create the file in the specified folder. If multiple folders are specified, you will be prompted for which folder to create the file in.\")": ".setDesc(\"在指定文件夹中创建文件。如果指定了多个文件夹，系统将提示您在哪个文件夹中创建文件。\")", ".setDesc(\"Get prompted to choose from both the selected folders and their subfolders when creating the note.\")": ".setDesc(\"创建注释时，系统会提示您从所选文件夹及其子文件夹中进行选择。\")", ".setDesc(\"Creates the file in the same folder as the currently active file. Will not create the file if there is no active file.\")": ".setDesc(\"在与当前活动文件相同的文件夹中创建文件。如果没有活动文件，则不会创建文件。\")", ".setDesc(\"Append link to created file to current file.\")": ".setDesc(\"将创建文件的链接附加到当前文件。\")", ".setDesc(\"Set default behavior rather then prompting user on what to do if a file already exists.\")": ".setDesc(\"设置默认行为，而不是在文件已存在的情况下提示用户该做什么。\")", ".setDesc(\"Open the created file.\")": ".setDesc(\"打开创建的文件。\")", ".setDesc(\"Split your editor and open file in new split.\")": ".setDesc(\"拆分编辑器并在新拆分中打开文件。\")", ".setDesc(\"Focus the opened tab immediately after opening\")": ".setDesc(\"打开后立即聚焦打开的选项卡\")", ".setDesc(\"File to capture to. Supports some format syntax.\")": ".setDesc(\"要捕获的文件。支持某些格式语法。\")", ".setDesc(`Put value at the bottom of the file - otherwise at the ${this.choice?.captureToActiveFile?\"active cursor location\":\"top\"}.`)": ".setDesc(`将值放在文件的底部，否则放在${this.choice？.captureToActiveFile？“活动光标位置”：“top”}。`)", ".setDesc(\"Formats the value as a task.\")": ".setDesc(\"将值格式化为任务。\")", ".setDesc(\"Add a link on your current cursor position, linking to the file you're capturing to.\")": ".setDesc(\"在当前光标位置添加一个链接，链接到您要捕获的文件。\")", ".setDesc(\"Insert capture after specified line. Accepts format syntax.\")": ".setDesc(\"在指定行后插入捕获。接受格式语法。\")", ".setDesc(\"Insert the text at the end of the section, rather than at the top.\")": ".setDesc(\"将文本插入到节的末尾，而不是顶部。\")", ".setDesc(\"Enabling this will insert the text at the end of the section & its subsections, rather than just at the end of the target section. A section is defined by a heading, and its subsections are all the headings inside that section.\")": ".setDesc(\"Enabling this will insert the text at the end of the section & its subsections, rather than just at the end of the target section. A section is defined by a heading, and its subsections are all the headings inside that section.\")", ".setDesc(\"Creates the 'insert after' line if it is not found.\")": ".setDesc(\"如果找不到“插入后”行，则创建该行。\")", ".setDesc(\"Set the format of the capture.\")": ".setDesc(\"设置捕获的格式。\")", ".setDesc(\"Open the file that is captured to.\")": ".setDesc(\"打开捕获到的文件。\")", ".setDesc(\"Open the file that is captured to in a new tab.\")": ".setDesc(\"在新选项卡中打开捕获到的文件。\")", ".setDesc(\"Focus the created tab immediately\")": ".setDesc(\"立即聚焦创建的选项卡\")", ".setDesc(\"Enabling this will have the assistant use the prompt template you specify. If disable, the assistant will ask you for a prompt template to use.\")": ".setDesc(\"启用此选项将使助手使用您指定的提示模板。如果禁用，助手将要求您使用提示模板。\")", ".setDesc(\"The model the AI Assistant will use\")": ".setDesc(\"AI助手将使用的模型\")", ".setDesc(\"The name of the variable used to store the AI Assistant output, i.e. {{value:output}}.\")": ".setDesc(\"用于存储AI Assistant输出的变量的名称，即{{value:output}}。\")", ".setDesc(\"The system prompt for the AI Assistant\")": ".setDesc(\"AI助手的系统提示\")", ".setDesc(\"Show advanced settings such as temperature, top p, and frequency penalty.\")": ".setDesc(\"显示温度、top p和频率惩罚等高级设置。\")", ".setDesc(\"Sampling temperature. Higher values like 0.8 makes the output more random, whereas lower values like 0.2 will make it more focused and deterministic. The default is 1.\")": ".setDesc(\"取样温度。较高的值（如0.8）会使输出更加随机，而较低的值（例如0.2）会使其更加集中和确定。默认值为1。\")", ".setDesc(\"Nucleus sampling - consider this an alternative to temperature. The model considers the results of the tokens with top_p probability mass. 0.1 means only tokens compromising the top 10% probability mass are considered. The default is 1.\")": ".setDesc(\"核子取样——将其视为温度的替代方案。该模型考虑了具有top_p概率质量的令牌的结果。0.1表示只考虑影响前10%概率质量的代币。默认值为1。\")", ".setDesc(\"Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model's likelihood to repeat the same line verbatim. The default is 0.\")": ".setDesc(\"正值会根据到目前为止新标记在文本中的现有频率对其进行惩罚，从而降低模型逐字重复同一行的可能性。默认值为0。\")", ".setDesc(\"Positive values penalize new tokens based on whether they appear in the text so far, increasing the model's likelihood to talk about new topics. The default is 0.\")": ".setDesc(\"正值根据新标记是否出现在文本中来惩罚它们，从而增加了模型谈论新主题的可能性。默认值为0。\")", ".setDesc(\"Add an Obsidian command\")": ".setDesc(\"添加黑曜石命令\")", ".setDesc(\"Add editor command\")": ".setDesc(\"添加编辑器命令\")", ".setDesc(\"Add user script\")": ".setDesc(\"添加用户脚本\")", ".setDesc(\"Add existing choice\")": ".setDesc(\"添加现有选项\")", ".setDesc(\"Providers for the AI Assistant\")": ".setDesc(\"AI助手的提供者\")", ".setDesc(\"The name of the provider\")": ".setDesc(\"提供者的名称\")", ".setDesc(\"The endpoint for the AI Assistant\")": ".setDesc(\"AI助手的端点\")", ".setDesc(\"The API Key for the AI Assistant\")": ".setDesc(\"人工智能助手的API密钥\")", ".setDesc(`Max Tokens: ${b.maxTokens}`)": ".setDesc(`最大代币数：${b.maxTokens}`)", ".setDesc(\"The providers for the AI Assistant\")": ".setDesc(\"AI助手的提供者\")", ".setDesc(\"The default model for the AI Assistant\")": ".setDesc(\"AI助手的默认模型\")", ".setDesc(\"Path to your folder with prompt templates\")": ".setDesc(\"带有提示模板的文件夹路径\")", ".setDesc(\"Show status messages from the AI Assistant\")": ".setDesc(\"显示来自AI助手的状态消息\")", ".setDesc(\"The default system prompt for the AI Assistant\")": ".setDesc(\"AI助手的默认系统提示\")", ".setDesc(\"Display release notes when a new version is installed. This includes new features, demo videos, and bug fixes.\")": ".setDesc(\"安装新版本时显示发行说明。这包括新功能、演示视频和错误修复。\")", ".setDesc(\"Use multi-line input prompt instead of single-line input prompt\")": ".setDesc(\"使用多行输入提示而不是单行输入提示\")", ".setDesc(\"Path to the folder where templates are stored. Used to suggest template files when configuring QuickAdd.\")": ".setDesc(\"存储模板的文件夹的路径。用于在配置QuickAdd时建议模板文件。\")", ".setDesc(\"Add QuickAdd icon to the sidebar ribbon. Requires a reload.\")": ".setDesc(\"将快速添加图标添加到侧边栏功能区。需要重新加载。\")", ".setDesc(\"The string used to join multiple LLM responses together. The default is a newline.\")": ".setDesc(\"用于将多个LLM响应连接在一起的字符串。默认值是换行符。\")", ".setDesc(\"The string used to separate chunks of text. The default is a newline.\")": ".setDesc(\"用于分隔文本块的字符串。默认值是换行符。\")", ".setDesc(\"The maximum number of tokens in each chunk, calculated as the chunk token size + prompt template token size + system prompt token size. Make sure you leave room for the model to respond to the prompt.\")": ".setDesc(\"The maximum number of tokens in each chunk, calculated as the chunk token size + prompt template token size + system prompt token size. Make sure you leave room for the model to respond to the prompt.\")", ".setDesc(\"Merge chunks together by putting them in the same prompt, until the max tokens limit is reached. Useful for sending fewer queries overall, but may result in less coherent responses.\")": ".setDesc(\"通过将块放在同一提示中将它们合并在一起，直到达到最大令牌限制。可用于发送更少的查询，但可能会导致响应不连贯。\")", ".setPlaceholder(\"File path\")": ".setPlaceholder(\"文件路径\")", ".setPlaceholder(\"Template path\")": ".setPlaceholder(\"模板路径\")", ".setPlaceholder(\"File name format\")": ".setPlaceholder(\"文件名格式\")", ".setPlaceholder(\"Folder path\")": ".setPlaceholder(\"文件夹路径\")", ".setPlaceholder(\"Insert after\")": ".setPlaceholder(\"在之后插入\")", ".setPlaceholder(\"Format\")": ".setPlaceholder(\"格式\")", ".setPlaceholder(\"Obsidian command\")": ".setPlaceholder(\"黑曜石命令\")", ".setPlaceholder(\"User script\")": ".setPlaceholder(\"用户脚本\")", ".setPlaceholder(\"Choice\")": ".setPlaceholder(\"选择\")", ".setPlaceholder(\"Macro name\")": ".setPlaceholder(\"宏名称\")", ".setPlaceholder(\"templates/\")": ".setPlaceholder(\"模板/\")", ".setTooltip(`Toggle ${I}`)": ".setTooltip(`切换${I}`)", ".setTooltip(\"Create file if it doesn't exist\")": ".setTooltip(\"如果文件不存在，则创建该文件\")", ".setTooltip(\"Add AI Assistant command\")": ".setTooltip(\"添加AI助手命令\")", ".setTooltip(\"Add wait command\")": ".setTooltip(\"添加等待命令\")", ".setTooltip(`Add ${l} Choice`)": ".setTooltip(`添加${l}选项`)", ".setTooltip(\"Create Macro\")": ".setTooltip(\"创建宏\")", ".setTooltip(\"Configure Macro\")": ".setTooltip(\"配置宏\")", ".setTooltip(\"Use multi-line input prompt\")": ".setTooltip(\"使用多行输入提示\")", ".innerText=`${this.settings?.name}${this.settings?.author?\" by \"+this.settings?.author:\"\"}`": ".innerText=`${this.settings?.name}${this.settings?.author?\" by \"+this.settings?.author:\"\"}`", ".innerText=`Token count: ${this.systemPromptTokenLength!==Number.POSITIVE_INFINITY?this.systemPromptTokenLength:\"select a model to calculate\"}`": ".innerText=`令牌计数：${this.systemPromptTokenLength！==Number.POSITIVE_FINFINITY？this.systemPromptTokenLength：“选择一个模型进行计算”}`", ".innerText=`Token count: ${this.systemPromptTokenLength}`": ".innerText=`令牌计数：${this.systemPromptTokenLength}`"}}