{"manifest": {"translationVersion": 1743589113888, "pluginVersion": "2.7.7"}, "description": {"original": "You can batch download images from your notes on desktop, iOS, and Android platforms, and batch upload and save them to a remote server, home NAS, or cloud storage (such as Alibaba Cloud OSS, Amazon S3, Cloudflare R2, MinIO). Additionally, you can stretch, crop, and resize the images.", "translation": "You can batch download images from your notes on desktop, iOS, and Android platforms, and batch upload and save them to a remote server, home NAS, or cloud storage (such as Alibaba Cloud OSS, Amazon S3, Cloudflare R2, MinIO). Additionally, you can stretch, crop, and resize the images."}, "dict": {"Notice(x(\"\\u5C06\\u8C03\\u8BD5\\u4FE1\\u606F\\u590D\\u5236\\u5230\\u526A\\u8D34\\u677F, \\u53EF\\u80FD\\u5305\\u542B\\u654F\\u611F\\u4FE1!\")": "Notice(x(\"\\u5C06\\u8C03\\u8BD5\\u4FE1\\u606F\\u590D\\u5236\\u5230\\u526A\\u8D34\\u677F, \\u53EF\\u80FD\\u5305\\u542B\\u654F\\u611F\\u4FE1!\")", "Notice(a)": "Notice(a)", "Notice(e)": "Notice(e)", ".log(t)": ".log(t)", ".error(\"forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported\")": ".error(\"forceFrameRate取0到125之间的正整数，不支持强制帧速率高于125 fps\")", ".error(e)": ".error(e)", "name:\"\")?Ci(a):\"": "name:\"\")?Ci(a):\"", "text:\"CMD (\\u2318) + OPTION (\\u2325) + I\"": "text:\"CMD (\\u2318) + OPTION (\\u2325) + I\"", "text:\"CTRL + SHIFT + I\"": "text:\"CTRL + SHIFT + I\"", ".setName(\"| \"+x(\"\\u901A\\u7528\")": ".setName(\"| \"+x(\"\\u901A\\u7528\")", ".setName(\"| \"+x(\"API \\u7F51\\u5173\")": ".setName(\"| \"+x(\"API \\u7F51\\u5173\")", ".setName(\"| \"+x(\"\\u4E0B\\u8F7D\")": ".setName(\"| \"+x(\"\\u4E0B\\u8F7D\")", ".setName(\"| \"+x(\"\\u4E0A\\u4F20\")": ".setName(\"| \"+x(\"\\u4E0A\\u4F20\")", ".setName(\"| \"+x(\"\\u652F\\u6301\")": ".setName(\"| \"+x(\"\\u652F\\u6301\")"}}