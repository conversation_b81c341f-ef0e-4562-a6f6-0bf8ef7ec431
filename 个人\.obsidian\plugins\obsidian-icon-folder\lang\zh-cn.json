{"manifest": {"translationVersion": 1740803460381, "pluginVersion": "2.14.6"}, "description": {"original": "Add icons to anything you desire in Obsidian, including files, folders, and text.", "translation": "为 Obsidian 中的任何内容添加图标，包括文件、文件夹和文本。"}, "dict": {"Notice(`[${config.PLUGIN_NAME}] Due to a change in version v1.2.2, the icon pack folder changed. Please change it in the settings to not be directly in /plugins.`, 8000)": "Notice(`[${config.PLUGIN_NAME}]由于v1.2.2版本的更改，图标包文件夹发生了变化。请在设置中将其更改为不直接在 /plugins 中。`, 8000)", "Notice(`Directory with name ${iconPack.name} already exists.`)": "Notice(`名为${iconBack.name}的目录已存在。`)", "Notice(`Moving ${iconPack.name}...`)": "Notice(`正在移动${iconPack.name}……`)", "Notice(`...moved ${iconPack.name}`)": "Notice(`…移动了${iconPack.name}`)", "Notice(`[${config.PLUGIN_NAME}] Renamed ${normalizedFilename} to ${newFilename} to avoid duplication.`, 8000)": "Notice(`[${config.PLUGIN_NAME}]将${normalizedFilename}重命名为${newFilename}以避免重复。`, 8000)", "Notice(`[${config.PLUGIN_NAME}] Could not create duplicated icon name (${normalizedFilename})": "注意(`[${config.PLUGIN_NAME}] 无法创建重复的图标名称 (${normalizedFilename})", "Notice(`Seems like you do not have an icon pack installed. (${iconName})": "Notice(`您似乎没有安装图标包。 (${iconName})", "Notice('Please delete your old icon packs and redownload your icon packs to use the new syncing mechanism.', 20000)": "Notice('请删除旧的图标包并重新下载图标包以使用新的同步机制。', 20000)", "Notice(`[${config.PLUGIN_NAME}] Inheritance has been removed and replaced with custom rules.`)": "Notice(`[${config.PLUGIN_NAME}]继承已被删除，并替换为自定义规则。`)", "Notice(`[${config.PLUGIN_NAME}] Background Check: found missing icons. Adding missing icons...`, 10000)": "Notice(`[${config.PLUGIN_NAME}]背景检查：发现缺少图标。正在添加缺失的图标……`, 10000)", "Notice(`[${config.PLUGIN_NAME}] Background Check: added missing icons`, 10000)": "Notice(`[${config.PLUGIN_NAME}]背景检查：添加了缺失的图标`, 10000)", "Notice('Icon pack already exists.')": "Notice('图标包已存在。')", "Notice('Icon pack successfully created.')": "Notice('图标包已成功创建。')", "Notice('Try to fix icon pack...')": "Notice('尝试修复图标包……')", "Notice('...tried to fix icon pack')": "Notice('…尝试修复图标包')", "Notice('Changing icon packs...')": "Notice('正在更改图标包……')", "Notice('Done. This change requires a restart of Obsidian')": "Notice('已完成。此更改需要重新启动Obsidian')", "Notice('Icons successfully added.')": "Notice('图标已成功添加。')", "Notice('Icon pack successfully deleted.')": "Notice('图标包已成功删除。')", "Notice(`File ${file.name} is not a SVG file.`)": "Notice(`文件${file.name}不是SVG文件。`)", "Notice('Icon rule added.')": "Notice('已添加图标规则。')", "Notice('Custom rule updated.')": "Notice('自定义规则已更新。')", "Notice('Custom rule deleted.')": "Notice('自定义规则已删除。')", "Notice('Saving in progress...')": "Notice('正在保存……')", "Notice('...saved successfully')": "Notice('…已成功保存')", "Notice('You need to reload Obsidian for this to take effect.', 10000)": "Notice('您需要重新加载 Obsidian 才能生效。', 10000)", "Notice(`Adding ${item.displayName}...`)": "Notice(`正在添加${item.displayName}……`)", "Notice(`...${item.displayName} added`)": "Notice(`…已添加${item.displayName}`)", "Notice(`[${config.PLUGIN_NAME}] Please enable \"Use icon in frontmatter\".`)": "Notice(`[${config.PLUGIN_NAME}]请启用“在 frontmatter 中使用图标”。`)", "Notice(`[${config.PLUGIN_NAME}] Refreshing icons from frontmatter, please wait...`)": "Notice(`[${config.PLUGIN_NAME}]正在刷新 frontmatter 的图标，请稍候……`)", "Notice(`[${config.PLUGIN_NAME}] Refreshed icons from frontmatter. Please restart Obsidian to see the changes.`)": "Notice(`[${config.PLUGIN_NAME}]已刷新 frontmatter 的图标。请重新启动Obsidian以查看更改。`)", "Notice(`[${config.PLUGIN_NAME}] Obsidian has to be restarted for this change to take effect.`)": "Notice(`[${config.PLUGIN_NAME}]Obsidian必须重新启动才能使此更改生效。`)", "Notice('Color of icon changed.')": "Notice('图标颜色已更改。')", "Notice(`[${config.PLUGIN_NAME}] Frontmatter property type \\`icon\\` has to be of type \\`text\\`.`)": "Notice(`[${config.PLUGIN_NAME}]Frontmatter 属性类型“icon”必须为“text”类型。`)", "Notice(`[${config.PLUGIN_NAME}] Frontmatter property type \\`iconColor\\` has to be of type \\`text\\`.`)": "Notice(`[${config.PLUGIN_NAME}]Frontmatter 属性类型“iconColor”必须为“text”类型。`)", ".log('`leaf` in outline is undefined', LoggerPrefix.Outline)": ".log('outline 中的 `leaf` 未定义', LoggerPrefix.Outline)", ".log('`leaf` length in outline is 0', LoggerPrefix.Outline)": ".log('outline 中的 `leaf` 长度为 0', LoggerPrefix.Outline)", ".log(`loading ${config.PLUGIN_NAME}`)": ".log(`正在加载${config.PLUGIN_NAME}`)", ".log('unloading obsidian-icon-folder')": ".log('取消加载 Obsidian 图标文件夹')", ".error(...this.formatMessage('error', message, prefix, optionalParams)": ".error(...this.formatMessage('错误', message, prefix, optionalParams)", ".error(`Icon file with name ${iconNameWithPrefix} could not be found`)": ".error(`找不到名为${iconNameWithPrefix}的图标文件`)", ".error(`Icon SVG with name ${iconNameWithPrefix} could not be found`)": ".error(`找不到名为${iconNameWithPrefix}的图标SVG`)", "name: 'font-awesome-brands'": "name: 'font-awesome-brands'", "name: 'font-awesome-regular'": "name: 'font-awesome-regular'", "name: 'font-awesome-solid'": "name: 'font-awesome-solid'", "name: 'remix-icons'": "name: 'remix-icons'", "name: 'icon-brew'": "name: 'icon-brew'", "name: 'simple-icons'": "name: 'simple-icons'", "name: 'lucide-icons'": "name: 'lucide-icons'", "name: 'tabler-icons'": "name: 'tabler-icons'", "name: 'boxicons'": "name: 'boxicons'", "name: 'rpg-awesome'": "name: 'rpg-awesome'", "name: 'coolicons'": "name: 'coolicons'", "name: 'feather-icons'": "name: 'feather-icons'", "name: 'octicons'": "name: 'octicons'", "name: 'Set icon for file'": "name: '设置文件图标'", "text: 'Include folders and files that are part of the path.'": "text: '包括路径中的文件夹和文件。'", "text: 'Where the custom rule gets applied to.'": "text: '自定义规则应用于何处。'", "text: 'General'": "text: '常规设置'", "text: 'Visibility of icons'": "text: '图标的可见性'", "text: 'Icon customization for files/folders'": "text: '文件/文件夹的图标自定义'", "text: 'Custom icon rules'": "text: '自定义图标规则'", "text: 'Icon packs'": "text: '图标包'", "text: 'Select a color for this icon'": "text: '为此图标选择颜色'", ".setText('Edit custom rule')": ".setText('编辑自定义规则')", ".setText('Change color')": ".setText('更改颜色')", ".setButtonText('Add icon pack')": ".setButtonText('添加图标包')", ".setButtonText('Choose icon')": ".setButtonText('选择图标')", ".setButtonText('Change icon')": ".setButtonText('更改图标')", ".setButtonText('Default')": ".setButtonText('默认')", ".setButtonText('Save Changes')": ".setButtonText('保存更改')", ".setButtonText('Save')": ".setButtonText('保存')", ".setButtonText('Browse icon packs')": ".setButtonText('浏览图标包')", ".setButtonText('Refresh')": ".setButtonText('刷新')", ".setButtonText('Reset')": ".setButtonText('重置')", ".setName('Add custom icon pack')": ".setName('添加自定义图标包')", ".setName('Add icon rule')": ".setName('添加图标规则')", ".setName('Emoji style')": ".setName('表情风格')", ".setName('Extra margin (in pixels)')": ".setName('额外边距（以像素为单位）')", ".setName('Icon color')": ".setName('图标颜色')", ".setName('Icon font size (in pixels)')": ".setName('图标字体大小（以像素为单位）')", ".setName('Icon packs folder path')": ".setName('图标包文件夹路径')", ".setName('Icons background check')": ".setName('图标背景检查')", ".setName('Add predefined icon pack')": ".setName('添加预定义的图标包')", ".setName('Recently used icons limit')": ".setName('最近使用的图标限制')", ".setName('Toggle icon in tabs')": ".setName('在标签中切换图标')", ".setName('Toggle icon in title')": ".setName('在标题中切换图标')", ".setName('Use icon in frontmatter')": ".setName('在 frontmatter 中使用图标')", ".setName('Frontmatter icon field name')": ".setName('Frontmatter 图标字段名称')", ".setName('Frontmatter icon color field name')": ".setName('Frontmatter 图标颜色字段名称')", ".setName('Refresh icons from frontmatter')": ".setName('从 frontmatter 刷新图标')", ".setName('Toggle icons while editing notes')": ".setName('在编辑笔记时切换图标')", ".setName('Toggle icons in links')": ".setName('在链接中切换图标')", ".setName('Icon identifier')": ".setName('图标标识符')", ".setName('Toggle Debug Mode')": ".setName('切换调试模式')", ".setDesc('Add a custom icon pack.')": ".setDesc('添加一个自定义图标包。')", ".setDesc(`Total icons: ${iconPack.icons.length}${isLucideIconPack ? ` ${additionalLucideDescription}` : ''}`)": ".setDesc(`图标总数: ${iconPack.icons.length}${isLucideIconPack ? ` ${additionalLucideDescription}` : ''}`)", ".setDesc(`Total icons: ${iconPack.icons.length} (added: ${file.name})`)": ".setDesc(`图标总数：${iconPack.icons.length}(已添加：${file.name})`)", ".setDesc('Will add the icon based on the defined rule (as a plain string or in regex format).')": ".setDesc('将根据定义的规则添加图标（作为纯文本字符串或正则表达式格式）。')", ".setDesc(`Icon: ${rule.icon}`)": ".setDesc(`图标：${rule.icon}`)", ".setDesc('Change the style of your emojis.')": ".setDesc('更改你的情绪符号样式。')", ".setDesc('Change the margin of the icons.')": ".setDesc('更改图标的间距。')", ".setDesc('Change the color of the displayed icons.')": ".setDesc('更改显示图标的颜色。')", ".setDesc('Change the font size of the displayed icons.')": ".setDesc('更改显示图标的字体大小。')", ".setDesc('Change the default icon packs folder path.')": ".setDesc('更改默认图标包文件夹路径。')", ".setDesc('Check in the background on every load of Obsidian, if icons are missing and it will try to add them to the specific icon pack.')": ".setDesc('在每次加载 Obsidian 时检查缺失的图标，并尝试将它们添加到相应的图标包中。')", ".setDesc('Add a predefined icon pack that is officially supported.')": ".setDesc('添加一个官方支持的预定义图标包。')", ".setDesc('Change the limit for the recently used icons displayed in the icon selection modal.')": ".setDesc('更改在图标选择框中显示的最近使用的图标的数量限制。')", ".setDesc('Toggles the visibility of an icon for a file in the tab bar.')": ".setDesc('切换标签栏中文件图标的状态。')", ".setDesc('Toggles the visibility of an icon above the title of a file.')": ".setDesc('切换文件标题上方图标的可见性。')", ".setDesc('Toggles whether to set the icon based on the frontmatter property `icon`.')": ".setDesc('切换是否根据 frontmatter 属性 `icon` 设置图标。')", ".setDesc('Sets the name of the frontmatter field which contains the icon.')": ".setDesc('设置包含图标的 frontmatter 字段的名称。')", ".setDesc('Sets the name of the frontmatter field which contains the icon color.')": ".setDesc('设置包含图标颜色的 frontmatter 字段的名称。')", ".setDesc('Toggles whether you are able to add and see icons in your notes and editor (e.g., ability to have :LiSofa: as an icon in your notes).')": ".setDesc('切换是否能够在笔记和编辑器中添加和显示图标（例如，能够在笔记中使用 :LiSofa: 作为图标）。')", ".setDesc('Toggles whether you are able to see icons in the links to other notes')": ".setDesc('切换是否在链接到其他笔记时显示图标')", ".setDesc('Change the icon identifier used in notes.')": ".setDesc('更改笔记中使用的图标标识符。')", ".setDesc('Toggle debug mode to see more detailed logs in the console. Do not touch this unless you know what you are doing.')": ".setDesc('切换调试模式以在控制台查看更详细的日志。除非你知道自己在做什么，否则不要更改此设置。')", ".setPlaceholder('Your icon pack name')": ".setPlaceholder('你的图标包名称')", ".setPlaceholder('regex or simple string')": ".setPlaceholder('正则表达式或简单字符串')", ".setTooltip('Try to fix icon pack')": ".setTooltip('尝试修复图标包')", ".setTooltip('Add an icon')": ".setTooltip('添加一个图标')", ".setTooltip('Remove the icon pack')": ".setTooltip('移除图标包')", ".setTooltip('Prioritize the custom rule')": ".setTooltip('优先处理自定义规则')", ".setTooltip('Deprioritize the custom rule')": ".setTooltip('降低自定义规则的优先级')", ".setTooltip('Edit the custom rule')": ".setTooltip('编辑自定义规则')", ".setTooltip(`Icon applicable to: ${isFor}`)": ".setTooltip(`图标适用于：${isFor}`)", ".setTooltip('Set color to the default one')": ".setTooltip('将颜色设置为默认颜色')", ".setTooltip('Remove the custom rule')": ".setTooltip('移除自定义规则')", ".setTooltip('Restore default')": ".setTooltip('恢复默认')", ".setTitle('Change icon')": ".setTitle('更改图标')", ".setTitle('Remove icon')": ".setTitle('删除图标')", ".setTitle('Change color of icon')": ".setTitle('更改图标颜色')", ".innerText = 'Recently used Icons:'": ".innerText = '最近使用的图标：'", ".innerText = 'All Icons:'": ".innerText = '所有图标：'", ".createEl('h3', { text: 'Visibility of icons' })": ".createEl('h3', { text: '图标的可见性' })", ".createEl('h1', { text: 'Icon packs' })": ".createEl('h1', { text: '图标包' })", "above: 'Above title'": "above: '在标题上'", "inline: 'Next to title'": "inline: '在标题旁边'", ".setDesc('Sets the icon and color for each note in the vault based on the frontmatter properties. WARNING: This will change any manually set icons to the one defined in the frontmatter. IF A NOTE HAS NO FRONTMATTER, THE CURRENT ICON WILL BE REMOVED. Please restart Obsidian after this completes to see the changes.')\n": ".setDesc('根据 frontmatter 属性设置库中每个注释的图标和颜色。警告：这将把任何手动设置的图标更改为 frontmatter 中定义的图标。如果笔记没有Frontmatter，则当前图标将被删除。完成此操作后，请重新启动Obsidian以查看更改。')\n", "'(Native Pack has fewer icons but 100% Obsidian Sync support)'": "'(Native Pack 图标较少，但支持 100% Obsidian同步)'", "'Top'": "'顶部'", "'Right'": "'右'", "'Bottom'": "'底部'", "'Left'": "'左'", "'Select to download icon pack'": "'选择要下载的图标包'", ".setName('EXPERIMENTAL: Use internal plugins')": ".setName('实验性：使用内部插件')", ".setDesc('Toggles whether to try to add icons to the bookmark and outline internal plugins.')": ".setDesc('切换是否尝试为书签和大纲内部插件添加图标。')"}}