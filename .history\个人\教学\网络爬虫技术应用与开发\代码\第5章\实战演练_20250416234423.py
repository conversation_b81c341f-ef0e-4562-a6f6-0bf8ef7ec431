import time								#导入time模块
import random							#导入random模块
import requests							#导入requests模块
from urllib.parse import urlencode	#导入urlencode模块
from bs4 import BeautifulSoup			#导入BeautifulSoup模块
import csv								#导入csv模块
#定义base_url字符串
base_url = 'https://search.bilibili.com/all?'
headersvalue = {
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.97 Safari/537.36'
}											#设置请求头User-Agent信息
proxiesvalue = [
    {'http': 'http://*************:8080'},
    {'http': 'http://***********:8800'},
    {'http': 'http://**************:8443'}
]											#定义代理IP列表
#定义解析内容函数
def parse_content(content):
    items = []							#定义items列表
    #创建BeautifulSoup对象，并设置使用lxml解析器
    soup = BeautifulSoup(content, 'lxml')
    #查找包含视频信息的li节点
    video_list = soup.select('.video-list > li')
    #遍历video_list,依次获取每一个视频信息
    for video in video_list:
        #获取视频标题
        title = video.select('.info a')[0].text.strip()
        #获取视频时长
        viedo_time=video.select('.so-imgTag_rb')[0].text.strip()
        #获取观看次数
        view_count = video.select('.tags span')[0].text.strip()
        #获取上传时间
        up_time = video.select('.tags span')[2].text.strip()
        #获取up主
        up_master = video.select('.tags span')[3].text.strip()
        #获取视频链接
        viedo_link='http:'+video.select('.info a')[0].attrs['href']
        item = {
            '视频标题': title,
            '视频时长': viedo_time,
            '观看次数': view_count,
            '上传时间': up_time,
            'up主': up_master,
            '视频链接': viedo_link
        }										#添加到字典中
        items.append(item)					#添加到items列表中
    return items								#返回items
#定义获取每一页信息的函数
def get_one_page(kw,page):
    params = {
        'keyword': kw,
        'page': str(page)
    }											#定义params字典
    url = base_url + urlencode(params)	#合并URL
    #异常判断
    try:
        #设置请求头和代理IP，发送HTTP请求
        r = requests.get(url, headers=headersvalue, proxies=
        random.choice(proxiesvalue))
    except:
        print('请求失败')						#请求错误，输出“请求失败”
    else:
        if r.status_code == 200:			#判断状态码
            items = parse_content(r.text)	#调用parse_content
    #设置随机休眠时间
    sleep_time = random.randint(2, 5) + random.random()
    time.sleep(sleep_time)					#程序休眠sleep_time
    return items								#返回items
if __name__=='__main__':
    keyword = input('请输入搜索关键字：')	#输入搜索关键字
    #打开data.csv文件写入数据
    with open(keyword + '.csv', 'w', newline='', encoding='utf-8') as file:
        names = ['视频标题', '视频时长', '观看次数', '上传时间', 'up主', '视频链接']									#定义表头
        #初始化writer对象
        writer = csv.DictWriter(file, fieldnames=names)
        writer.writeheader()					#写入表头
        for i in range(1, 51):
            #输出正在爬取的页码提示
            print('正在爬取第%d页的视频信息' % i)
            items = get_one_page(keyword, i)	#调用get_one_page
            writer.writerows(items)			#写入items
